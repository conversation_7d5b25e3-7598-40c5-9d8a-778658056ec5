<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mapper.manage.MerchantMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.model.vo.MerchantVO">
        <id column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="mname" property="mname" jdbcType="VARCHAR"/>
        <result column="followId" property="followId" jdbcType="INTEGER"/>
        <result column="dangerDay" property="dangerDay" jdbcType="INTEGER"/>
        <result column="mcontact" property="mcontact" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="islock" property="islock" jdbcType="TINYINT"/>
        <result column="rank_id" property="rankId" jdbcType="TINYINT"/>
        <result column="register_time" property="registerTime" jdbcType="TIMESTAMP"/>
        <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
        <result column="invitecode" property="invitecode" jdbcType="VARCHAR"/>
        <result column="channel_code" property="channelCode" jdbcType="VARCHAR"/>
        <result column="inviter_channel_code" property="inviterChannelCode" jdbcType="VARCHAR"/>
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
        <result column="audit_user" property="auditUser" jdbcType="INTEGER"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="area" property="area" jdbcType="VARCHAR"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="size" property="size" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="trade_area" property="tradeArea" jdbcType="VARCHAR"/>
        <result column="trade_group" property="tradeGroup" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="adminRealname" property="adminRealname" jdbcType="VARCHAR"/>
        <result column="adminName" property="adminName" jdbcType="VARCHAR"/>
        <result column="areaName" property="areaName" jdbcType="VARCHAR"/>
        <result column="poi_note" property="poiNote" jdbcType="VARCHAR"/>
        <result column="direct" property="direct" jdbcType="INTEGER"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="server" property="server" jdbcType="INTEGER"/>
        <result column="member_integral" property="memberIntegral" jdbcType="DECIMAL" />
        <result column="grade" property="grade" jdbcType="INTEGER"/>
        <result column="addTime" property="addTime"  />
        <result column="reason" property="reason"  />
        <result column="recharge_amount" property="rechargeAmount" />
        <result column="last_order_time" property="lastOrderTime"/>
        <result column="register_time" property="registerTime"/>
        <result column="audit_time" property="auditTime"/>
        <result column="audit_user" property="auditUser"/>
        <result column="house_number" property="houseNumber"/>
        <result column="enterprise_scale" property="enterpriseScale"/>
        <result column="company_brand" property="companyBrand"/>
        <result column="map_section" property="mapSection"/>
        <result column="clue_pool" property="cluePool"/>
        <result column="merchant_type" property="merchantType"/>
        <result column="examine_type" property="examineType"/>
        <result column="invoice_title" property="invoiceTitle"/>
        <result column="display_button" property="displayButton"/>
        <result column="realname" property="realName"/>
        <result column="name_remakes" property="nameRemakes"/>
        <result column="operate_status" property="operateStatus"/>
        <result column="pull_black_remark" property="pullBlackRemark"/>
        <result column="pull_black_operator" property="pullBlackOperator"/>
        <result column="contact_id" property="contactId"/>
        <result column="large_area_no" property="largeAreaNo"/>
        <result column="door_pic" property="doorPic"/>
        <result column="sku_show" property="skuShow"/>
        <result column="business_line" property="businessLine"/>
        <result column="main_business_type" property="mainBusinessType"/>
        <result column="side_business_type" property="sideBusinessType"/>
        <result column="merchant_chain_type" property="merchantChainType"/>
    </resultMap>


    <resultMap id="merchant" type="net.summerfarm.model.vo.MerchantVO">
        <id column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="mname" property="mname" jdbcType="VARCHAR"/>
        <result column="areaNo" property="areaNo" jdbcType="INTEGER"/>
        <result column="adminName" property="adminName" jdbcType="VARCHAR"/>
        <result column="adminId" property="adminId" jdbcType="INTEGER"/>
        <result column="size" property="size" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="grade" property="grade" jdbcType="INTEGER"/>
        <collection property="contacts" ofType="net.summerfarm.model.domain.Contact">
            <id column="contact_id" property="contactId" />
            <result column="address" property="address" />
            <result column="phone" property="phone"/>
            <result column="contact" property="contact"/>
            <result column="status" property="status"/>
        </collection>
    </resultMap>

    <resultMap id="contactsMap" type="net.summerfarm.common.util.es.dto.EsMerchantIndexDTO">
        <id column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="area" property="area" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="mname" property="mname" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="grade" property="grade" jdbcType="INTEGER"/>
        <result column="islock" property="islock" jdbcType="TINYINT"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="size" property="size" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="last_order_time" property="lastOrderTime"/>
        <result column="operate_status" property="operateStatus"/>
        <result column="admin_id" property="bdId"/>
        <result column="admin_name" property="bdName"/>
        <result column="danger_day" property="dangerDay"/>
    </resultMap>

    <resultMap id="esResultMap" type="net.summerfarm.common.util.es.dto.EsMerchantIndexDTO">
        <id column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="area" property="area" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="mname" property="mname" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="grade" property="grade" jdbcType="INTEGER"/>
        <result column="type" property="merchantType" javaType="java.lang.String"/>
        <result column="islock" property="islock" jdbcType="TINYINT"/>
        <result column="area_no" property="areaNo" jdbcType="INTEGER"/>
        <result column="size" property="size" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="last_order_time" property="lastOrderTime"/>
        <result column="operate_status" property="operateStatus"/>
        <result column="admin_id" property="bdId"/>
        <result column="admin_name" property="bdName"/>
        <result column="danger_day" property="dangerDay"/>
        <result column="reason" property="dangerDayReason"/>
        <result column="register_time" property="registerTime"/>
        <result column="reassign_time" property="reassignTime"/>
        <result column="source" property="lastFollowUpBdId" />
        <result column="business_line" property="businessLine"/>
        <result column="release_time" property="releaseTime" jdbcType="TIMESTAMP"/>
        <result column="protect_reason" property="protectReason" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="resultMapForListByRangeId" type="net.summerfarm.model.DTO.market.circle.EsMerchantTagDTO">
        <id column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="area_no" property="areaNo"/>
        <result column="large_area_no" property="largeAreaNo"/>
        <result column="size" property="merchantType" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result column="type" property="mainBusiness"/>
        <result column="last_order_time" property="lastOrderTime"/>
        <result column="register_time" property="registerTime"/>
    </resultMap>

    <sql id="Base_Column_List">
    m_id, mname, mcontact, phone, islock, rank_id, register_time, login_time,last_order_time,sku_show,
    invitecode, audit_time, audit_user, province, city, area, address,remark,area_no,size,type,recharge_amount,
    trade_area,trade_group,admin_id,direct,server,member_integral,grade,house_number,enterprise_scale,company_brand,merchant_type
  </sql>

    <select id="selectMerchantList" parameterType="net.summerfarm.model.vo.MerchantVO" resultType="net.summerfarm.model.vo.MerchantVO">
        select m.m_id mId, m.mname, m.mcontact, m.phone, m.area_no areaNo, m.area,m.province,m.city,m.address, m.remark,m.member_integral memberIntegral,m.size,m.direct,c.province,c.city,c.address,
        c.area,f.admin_name adminName,m.grade, c.house_number houseNumber,m.islock as islock
        from merchant m
        left join  contact c on m.m_id=c.m_id and c.is_default=1 and c.status=1
        left join follow_up_relation f on m.m_id=f.m_id  and f.reassign=0
        <where>
            <if test="mId != null">
                AND m.m_id = #{mId}
            </if>
            <if test="mname != null">
                AND mname  LIKE CONCAT('%',#{mname},'%')
            </if>
            <if test="openid != null">
                AND m.openid = #{openid}
            </if>
            <if test="phone != null">
                AND m.phone = #{phone}
            </if>
            <if test="adminId != null">
                AND f.admin_id = #{adminId}
            </if>

            <if test="area != null">
                AND c.area LIKE CONCAT('%',#{area},'%')
            </if>
            <if test="city != null">
                AND c.city LIKE CONCAT('%',#{city},'%')
            </if>
            <if test="province != null">
                AND c.province LIKE CONCAT('%',#{province},'%')
            </if>

            <if test="address != null">
                AND c.address  LIKE CONCAT('%',#{address},'%')
            </if>
        </where>
    </select>

    <select id="getGrade" resultType="net.summerfarm.model.input.GradeReq">
        select m.grade grade ,count(*) number
        from merchant m
        left join follow_up_relation f on m.m_id=f.m_id  and f.reassign=0
        where f.admin_id=#{adminId} and m.area_no=#{areaNo} and m.grade is not null
        group by m.grade
    </select>

    <select id="selectOne" parameterType="hashmap" resultType="net.summerfarm.model.domain.Merchant">
        select m.m_id mId, m.mname, m.mcontact, m.phone, m.openid, m.islock, m.area_no areaNo, m.area,m.province,m.city,m.address,
        m.last_order_time lastOrderTime, m.inviter_channel_code inviterChannelCode, m.remark,m.member_integral memberIntegral,m.size,m.direct,
        m.house_number houseNumber, m.admin_id adminId
        from merchant m
        <where>
            <if test="mId != null">
                AND m.m_id = #{mId}
            </if>
            <if test="mname != null">
                AND mname = #{mname}
            </if>
            <if test="openid != null">
                AND m.openid = #{openid}
            </if>
            <if test="phone != null">
                AND m.phone = #{phone}
            </if>
            <if test="channelCode != null">
                AND m.channel_code = #{channelCode}
            </if>
            <if test="adminId != null">
                AND m.admin_id = #{adminId}
            </if>
        </where>
    </select>

    <select id="countNewMerchants" resultType="java.lang.Integer">
    SELECT count(1)
    FROM merchant m
    WHERE m.register_time <![CDATA[>=]]> #{startTime}
    AND m.register_time <![CDATA[<]]> #{endTime}
  </select>

    <update id="rebackReview" parameterType="net.summerfarm.model.domain.Merchant">
        UPDATE merchant
        SET islock = 1,
            audit_user = #{auditUser},
            audit_time = now()
        WHERE m_id = #{mId}
    </update>

    <update id="rebackReviewNew" parameterType="net.summerfarm.model.domain.Merchant">
        UPDATE merchant
        SET islock = 0,
            operate_status = 3,
            direct = 2,
            sku_show = 1,
            admin_id = #{adminId},
            size = #{size},
            audit_user = #{auditUser},
            audit_time = now()
        WHERE m_id = #{mId}
    </update>

    <select id="countMerchantsToGmv" resultType="java.lang.Integer">
        SELECT count(1)
        FROM (
            SELECT m.m_id mId,m.area_no areaNo
            FROM merchant m
            WHERE m.islock=0
            <if test="areaNo != null">
                AND m.area_no = #{areaNo}
            </if>
            <if test="startTime != null">
                AND m.register_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND m.register_time <![CDATA[<]]> #{endTime}
            </if>
            <if test="dataPermission != null and dataPermission.size!=0">
                AND m.area_no IN
                <foreach collection="dataPermission" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        ) t
    </select>

    <select id="selectIssueUser" parameterType="java.lang.String" resultMap="BaseResultMap">
    /*FORCE_MASTER*/
    SELECT m.m_id, m.mname, m.mcontact, m.phone  from merchant m
    WHERE m.islock = 0 AND m.phone = #{phone}

  </select>



    <select id="selectOpenSea"  resultType="net.summerfarm.model.vo.MerchantVO">
        select m.m_id mId,m.mname,m.phone,c.city,c.province,c.address,c.area,f.reassign_time reassignTime,f.reassign,f.reason,f.id followId,f.follow_type followType,f.add_time addTime,m.area_no areaNo
        ,f.timing_follow_type timingFollowType,mw.status whiteListType,c.house_number houseNumber
        from follow_up_relation f
        left join merchant m on m.m_id=f.m_id
        left join contact c on m.m_id=c.m_id and c.is_default=1 and c.status=1
        left join merchant_follow_white_list mw on mw.m_id = m.m_id
        where m.islock=0 and f.reassign=1
        <if test="start !=null">
            and f.reassign_time > #{start}
        </if>
        <if test="reason !=null">
            and f.reason=#{reason}
        </if>
        <if test="followType != null">
            and f.follow_type=#{followType}
        </if>
        <if test="timingFollowType != null">
            and f.timing_follow_type = #{timingFollowType}
        </if>
        <if test="whiteListType != null">
            and mw.status = #{whiteListType}
        </if>
        <if test="mId !=null">
            and m.m_id=#{mId}
        </if>
        <if test="mname !=null">
            and m.mname like CONCAT('%',#{mname},'%')
        </if>
        <if test="area != null">
            AND c.area LIKE CONCAT('%',#{area},'%')
        </if>
        <if test="city != null">
            AND c.city LIKE CONCAT('%',#{city},'%')
        </if>
        <if test="province != null">
            AND c.province LIKE CONCAT('%',#{province},'%')
        </if>

        <if test="address != null">
            AND c.address  LIKE CONCAT('%',#{address},'%')
        </if>
        order by f.reassign_time desc
    </select>

    <select id="selectPrivateSea"  resultType="net.summerfarm.model.vo.MerchantVO">
        select m.m_id mId,m.mname,c.phone,c.province,c.city,c.address,c.area,f.reason,a.realname adminRealname,
        f.danger_day dangerDay,f.add_time addTime,f.id followId,c.house_number houseNumber
        from merchant m
        left join  contact c on m.m_id=c.m_id and c.is_default=1  and c.status=1
        left join follow_up_relation f on m.m_id=f.m_id
        LEFT join admin a on a.admin_id=f.admin_id
        where m.islock=0 and f.reassign=0
        <if test="start !=null">
        and f.add_time > #{start}
        </if>
        <if test="reason !=null">
            and f.reason=#{reason}
        </if>
        <if test="mname !=null">
            and m.mname like CONCAT('%',#{mname},'%')
        </if>
        <if test="adminId !=null">
            and f.admin_id=#{adminId}
        </if>
        <if test="address !=null">
            and c.address LIKE CONCAT('%',#{address},'%')
        </if>
        order by f.danger_day
    </select>

    <select id="selectMerchants" parameterType="net.summerfarm.model.domain.Merchant" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM merchant
        <where>
            <if test="islock != null">
                AND islock = #{islock}
            </if>
            <if test="areaNo != null">
                AND area_no = #{areaNo}
            </if>
            <if test="size != null">
                AND size = #{size}
            </if>
            <if test="skuShow != null">
                AND sku_show = #{skuShow}
            </if>
            <if test="adminId != null">
                AND admin_id = #{adminId}
            </if>
        </where>
    </select>

    <select id="select" resultMap="BaseResultMap" parameterType="net.summerfarm.model.domain.Merchant">
        SELECT m.m_id,m.mname, m.poi_note, m.mcontact, m.openid, m.phone, m.islock, m.rank_id,m.register_time,
        m.login_time,m.pull_black_remark pullBlackRemark,m.pull_black_operator pullBlackOperator,m.display_button,
        m.invitecode, m.audit_time, m.audit_user, m.province, m.city, m.area, m.address,m.remark,m.area_no,m.examine_type,
        m.size,m.type,m.trade_area,m.trade_group, m.inviter_channel_code,m.direct,m.admin_id,m.server, m.area_no areaNo,
        m.grade,m.show_price showPrice,m.clue_pool,m.house_number,m.company_brand,m.enterprise_scale,
        <if test="adminType != null">
            aie.admin_type adminType,aie.realname,aie.name_remakes,
        </if>
        <if test="cluePool != null">
            mcp.es_id esId,mcp.m_name clueMName,mcp.address clueAddress,mcp.phone cluePhone,
        </if>
        <if test="chat !=null and chat==2">
            ac.create_time register_time,
        </if>
        <if test="chat !=null and chat==1">
            c.contact_id,c.create_time register_time,
        </if>
        m.merchant_type
        FROM merchant m
        <if test="adminType != null">
            left join admin aie on aie.admin_id = m.admin_id
        </if>
        <if test="cluePool != null">
            LEFT JOIN merchant_clue_pool mcp on mcp.m_id = m.m_id and mcp.status = 0
        </if>
        <if test="chat !=null and chat==2">
            LEFT JOIN account_change ac on ac.m_id=m.m_id and ac.status=1
        </if>
        <if test="chat !=null and chat==1">
            LEFT JOIN contact c on c.m_id=m.m_id
        </if>
        <where>
            <if test="adminType!=null">
                and aie.admin_type = #{adminType}
            </if>
            <if test="cluePool != null and cluePool == 0">
                and mcp.es_id is null
            </if>
            <if test="cluePool != null and cluePool == 1">
                and mcp.es_id is not null
            </if>
            <if test="chat !=null and chat==2">
                and ac.status=1
            </if>
            <if test="chat !=null and chat==1">
                and c.status=3
            </if>
            <if test="enterpriseScale!=null">
                and m.enterprise_scale = #{enterpriseScale}
            </if>
            <if test="mId !=null and mId !=0 ">
                and m.m_id=#{mId,jdbcType=BIGINT}
            </if>
            <if test="size !=null ">
                and m.size = #{size}
            </if>
            <if test="direct !=null ">
                and m.direct=#{direct}
            </if>
            <if test="invitecode !=null and invitecode !=0">
                and m.invitecode=#{invitecode}
            </if>
            <if test="islock !=null">
                and m.islock=#{islock,jdbcType=BIT}
            </if>
            <if test="mname !=null">
                and m.mname LIKE CONCAT('%',#{mname,jdbcType=VARCHAR},'%')
            </if>
            <if test="registerTime !=null ">
                and m.register_time >=#{registerTime,jdbcType=TIMESTAMP}
            </if>
            <if test="areaNo !=null ">
                and m.area_no =#{areaNo}
            </if>
            <if test="city !=null">
                and m.city LIKE CONCAT('%',#{city},'%')
            </if>
            <if test="examineType !=null ">
                and m.examine_type = #{examineType}
            </if>
            <if test="grade != null">
                <choose>
                    <when test="grade == 100">
                        AND  m.grade IS NULL
                    </when>
                    <otherwise>
                        AND m.grade = #{grade}
                    </otherwise>
                </choose>
            </if>
            <if test="mIdList !=null and mIdList.size() > 0">
                and m.m_id in
                <foreach collection="mIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="province != null and province != ''">
                and m.province = #{province,jdbcType=VARCHAR}
            </if>
            <if test="city != null and city != ''">
                and m.city = #{city,jdbcType=VARCHAR}
            </if>
            <if test="area != null and area != ''">
                and m.area = #{area,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY m.m_id DESC
    </select>

    <update id="updateInfo" parameterType="net.summerfarm.model.domain.Merchant">
        update merchant
        <set>
            direct = #{direct},
            admin_id = #{adminId},
            grade = #{grade},
            sku_show = #{skuShow},
            <if test="mname != null">
                mname = #{mname},
            </if>
            <if test="mcontact != null">
                mcontact = #{mcontact},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="province != null">
                province = #{province,jdbcType=VARCHAR},
                city = #{city,jdbcType=VARCHAR},
                area = #{area,jdbcType=VARCHAR},
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="areaNo != null">
                area_no = #{areaNo},
            </if>
            <if test="size != null">
                size = #{size},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="mainBusinessType != null">
                main_business_type = #{mainBusinessType,jdbcType=VARCHAR},
            </if>
            <if test="sideBusinessType != null">
                side_business_type = #{sideBusinessType,jdbcType=VARCHAR},
            </if>
            <if test="merchantChainType != null">
                merchant_chain_type = #{merchantChainType,jdbcType=INTEGER},
            </if>
            <if test="tradeArea != null">
                trade_area = #{tradeArea},
            </if>
            <if test="tradeGroup != null">
                trade_group = #{tradeGroup},
            </if>
            <if test="server != null">
                server = #{server},
            </if>
            <if test="showPrice != null">
                show_price = #{showPrice},
            </if>
            <if test="houseNumber != null">
                house_number = #{houseNumber},
            </if>
            <if test="enterpriseScale != null">
                enterprise_scale =#{enterpriseScale} ,
            </if>
            <if test="companyBrand != null">
                company_brand =#{companyBrand},
            </if>
            <if test ="cluePool != null">
                clue_pool = #{cluePool},
            </if>
            <if test ="merchantType != null">
                merchant_type = #{merchantType},
            </if>
            <if test ="displayButton != null">
                display_button = #{displayButton},
            </if>
            <if test ="doorPic != null">
                door_pic = #{doorPic},
            </if>
            <if test="updater != null">
                updater = #{updater},
            </if>
        </set>
        where m_id = #{mId,jdbcType=BIGINT}
    </update>

    <update id="updateStatus" parameterType="net.summerfarm.model.domain.Merchant">
        update merchant
        <set>
            <if test="islock != null">
                islock = #{islock},
            </if>
            <if test="adminId!= null">
                admin_id = #{adminId},
            </if>
            <if test="direct != null">
                direct = #{direct},
            </if>
            <if test="pullBlackRemark != null">
                pull_black_remark = #{pullBlackRemark},
            </if>
            <if test="pullBlackOperator != null">
                pull_black_operator = #{pullBlackOperator},
            </if>
        </set>
        where m_id = #{mId,jdbcType=BIGINT}
    </update>

    <!--auto Code-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        /*FORCE_MASTER*/
        select
        m_id, mname, mcontact, phone, islock, rank_id, register_time, login_time,last_order_time,
        invitecode, channel_code, audit_time, audit_user, province, city, area, address,remark,m.area_no areaNo,size,type,
        trade_area,trade_group,admin_id,direct,server,member_integral,recharge_amount,grade,area_no,
        cash_amount,cash_update_time, house_number houseNumber,examine_type examineType,operate_status,updater,openid,door_pic,business_line,
        main_business_type,side_business_type,merchant_chain_type
        from merchant m
        where m_id = #{mId,jdbcType=BIGINT}
    </select>


    <insert id="insertSelective" parameterType="net.summerfarm.model.domain.Merchant" useGeneratedKeys="true" keyProperty="mId">
        insert into merchant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                m_id,
            </if>
            <if test="mname != null">
                mname,
            </if>
            <if test="mcontact != null">
                mcontact,
            </if>
            <if test="openid != null">
                openid,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="islock != null">
                islock,
            </if>
            <if test="rankId != null">
                rank_id,
            </if>
            <if test="registerTime != null">
                register_time,
            </if>
            <if test="loginTime != null">
                login_time,
            </if>
            <if test="invitecode != null">
                invitecode,
            </if>
            <if test="auditTime != null">
                audit_time,
            </if>
            <if test="auditUser != null">
                audit_user,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="area != null">
                area,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="size != null">
                size ,
            </if>
            <if test="type != null">
                type ,
            </if>
            <if test="tradeArea != null">
                trade_area ,
            </if>
            <if test="tradeGroup != null">
                trade_group ,
            </if>
            <if test="adminId != null">
                admin_id ,
            </if>
            <if test="direct != null">
                direct ,
            </if>
            <if test="server != null">
                server ,
            </if>
            <if test="houseNumber != null">
                house_number ,
            </if>
            <if test="enterpriseScale != null">
                enterprise_scale ,
            </if>
            <if test="companyBrand != null">
                company_brand ,
            </if>
            <if test ="cluePool != null">
                clue_pool,
            </if>
            <if test="merchantType != null">
                merchant_type,
            </if>
            <if test="poiNote != null">
                poi_note ,
            </if>
            <if test="skuShow != null">
                sku_show,
            </if>
            <if test="examineType != null">
                examine_type,
            </if>
            <if test="displayButton != null">
                display_button,
            </if>
            <if test="mainBusinessType != null">
                main_business_type,
            </if>
            <if test="sideBusinessType != null">
                side_business_type,
            </if>
            <if test="merchantChainType != null">
                merchant_chain_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mId != null">
                #{mId,jdbcType=BIGINT},
            </if>
            <if test="mname != null">
                #{mname,jdbcType=VARCHAR},
            </if>
            <if test="mcontact != null">
                #{mcontact,jdbcType=VARCHAR},
            </if>
            <if test="openid != null">
                #{openid,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="islock != null">
                #{islock,jdbcType=BIT},
            </if>
            <if test="rankId != null">
                #{rankId,jdbcType=TINYINT},
            </if>
            <if test="registerTime != null">
                #{registerTime,jdbcType=TIMESTAMP},
            </if>
            <if test="loginTime != null">
                #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="invitecode != null">
                #{invitecode,jdbcType=INTEGER},
            </if>
            <if test="auditTime != null">
                #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditUser != null">
                #{auditUser,jdbcType=INTEGER},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                #{area,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="size != null">
                #{size},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="tradeArea != null">
                #{tradeArea},
            </if>
            <if test="tradeGroup != null">
                #{tradeGroup},
            </if>
            <if test="adminId != null">
                #{adminId},
            </if>
            <if test="direct != null">
                 #{direct},
            </if>
            <if test="server != null">
                server = #{server},
            </if>
            <if test="houseNumber != null">
                #{houseNumber ,jdbcType=VARCHAR},
            </if>
            <if test="enterpriseScale != null">
                 #{enterpriseScale} ,
            </if>
            <if test="companyBrand != null">
                 #{companyBrand},
            </if>
            <if test ="cluePool != null">
                 #{cluePool},
            </if>
            <if test="merchantType != null">
                 #{merchantType},
            </if>
            <if test="poiNote != null">
                #{poiNote,jdbcType=VARCHAR},
            </if>
            <if test="skuShow != null">
                #{skuShow},
            </if>
            <if test="examineType != null">
                #{examineType},
            </if>
            <if test="displayButton != null">
                #{displayButton},
            </if>
            <if test="mainBusinessType != null">
                #{mainBusinessType,jdbcType=VARCHAR},
            </if>
            <if test="sideBusinessType != null">
                #{sideBusinessType,jdbcType=VARCHAR},
            </if>
            <if test="merchantChainType != null">
                #{merchantChainType,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.model.domain.Merchant">
        update merchant
        <set>
            <if test="mname != null">
                mname = #{mname,jdbcType=VARCHAR},
            </if>
            <if test="mcontact != null">
                mcontact = #{mcontact,jdbcType=VARCHAR},
            </if>
            <if test="openid != null">
                openid = #{openid,jdbcType=VARCHAR},
            </if>
            <if test="unionid != null">
                unionid = #{unionid,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="islock != null">
                islock = #{islock,jdbcType=BIT},
            </if>
            <if test="rankId != null">
                rank_id = #{rankId,jdbcType=TINYINT},
            </if>
            <if test="registerTime != null">
                register_time = #{registerTime,jdbcType=TIMESTAMP},
            </if>
            <if test="loginTime != null">
                login_time = #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="invitecode != null">
                invitecode = #{invitecode,jdbcType=INTEGER},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditUser != null">
                audit_user = #{auditUser,jdbcType=INTEGER},
            </if>
            <if test="province != null">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                area = #{area,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="lastOrderTime != null">
                last_order_time = #{lastOrderTime},
            </if>
            <if test="areaNo != null">
                area_no = #{areaNo},
            </if>
            <if test="size != null">
                size = #{size},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="tradeArea != null">
                trade_area = #{tradeArea},
            </if>
            <if test="tradeGroup != null">
                trade_group = #{tradeGroup},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="adminId != null">
                admin_id = #{adminId},
            </if>
            <if test="direct != null">
                direct = #{direct},
            </if>
            <if test="skuShow != null">
                sku_show = #{skuShow},
            </if>
            <if test="grade != null">
                grade = #{grade},
            </if>
            <if test="poiNote != null">
                poi_note = #{poiNote},
            </if>
            <if test="mpOpenid != null">
                mp_openid = #{mpOpenid},
            </if>
            <if test="cashAmount != null">
                cash_amount = #{cashAmount},
            </if>
            <if test="cashUpdateTime">
                cash_update_time = #{cashUpdateTime},
            </if>
            <if test="mergeAdmin != null">
                merge_admin = #{mergeAdmin},
            </if>
            <if test="mergeTime != null">
                merge_time = #{mergeTime},
            </if>
            <if test="rechargeAmount != null">
                recharge_amount = #{rechargeAmount},
            </if>
            <if test="memberIntegral != null">
                member_integral = #{memberIntegral},
            </if>
            <if test="showPrice != null">
                show_price = #{showPrice},
            </if>
            <if test="changePop != null">
                change_pop = #{changePop},
            </if>
            <if test="houseNumber != null">
                house_number = #{houseNumber},
            </if>
            <if test="enterpriseScale != null">
                enterprise_scale =#{enterpriseScale} ,
            </if>
            <if test="companyBrand != null">
                company_brand =#{companyBrand},
            </if>
            <if test ="cluePool != null">
                clue_pool =#{cluePool},
            </if>
            <if test ="merchantType != null">
                merchant_type = #{merchantType},
            </if>
            <if test="examineType != null">
                examine_type = #{examineType},
            </if>
            <if test="operateStatus != null">
                operate_status = #{operateStatus},
            </if>
            <if test="doorPic != null">
                door_pic = #{doorPic},
            </if>
            <if test="updater != null">
                updater = #{updater},
            </if>
            <if test="mainBusinessType != null">
                main_business_type = #{mainBusinessType,jdbcType=VARCHAR},
            </if>
            <if test="sideBusinessType != null">
                side_business_type = #{sideBusinessType,jdbcType=VARCHAR},
            </if>
            <if test="merchantChainType != null">
                merchant_chain_type = #{merchantChainType,jdbcType=INTEGER},
            </if>
        </set>
        where m_id = #{mId,jdbcType=BIGINT}
    </update>

    <select id="merchatSizeData" resultType="net.summerfarm.model.vo.DataVO">
        select size name ,count(*) as number
        from merchant
        where register_time <![CDATA[<=]]> #{date} and size = '单店'
        GROUP by size
    </select>

    <select id="merchatEnterpriseScaleData" resultType="net.summerfarm.model.vo.DataVO">
        select enterprise_scale name ,count(*) as number
        from merchant
        where register_time <![CDATA[<=]]> #{date} and enterprise_scale is not null and size = '大客户'
        and enterprise_scale in ('加盟店','托管店','直营店','未知')
        GROUP by enterprise_scale
    </select>

    <select id="merchatTypeData" resultType="net.summerfarm.model.vo.DataVO">
        select type name ,count(*) as number
        from merchant
        where register_time <![CDATA[<=]]> #{date}
        GROUP by type
    </select>

    <update id="updateInviter2Null" parameterType="java.lang.Long">
        update merchant set `inviter_channel_code` = NULL where m_id = #{mId}
    </update>

    <select id="selectMerchantByAdminId" parameterType="java.util.HashMap" resultType="net.summerfarm.model.vo.MerchantVO">
        SELECT m_id mId,mname,address,direct,mcontact,phone
        FROM merchant
        WHERE admin_id=#{adminId}
        AND islock=0
        AND size='大客户'
    </select>



    <update id="updateByAdminId" parameterType="java.lang.Integer">
        update merchant set islock= 3 where admin_id = #{adminId}  and islock=0
    </update>

    <update id="updateSkuShowByAdminId" >
        update merchant set sku_show=#{skuShow},direct=#{direct} where admin_id = #{adminId} and islock=0
    </update>

    <update id="updateSkuShowByDirect" >
        update merchant set sku_show=#{skuShow} where admin_id = #{adminId} and direct=#{direct} and islock=0
    </update>

    <select id="selectMajorMerchantDetail" parameterType="java.lang.Integer" resultType="net.summerfarm.model.vo.MerchantVO">
        SELECT m.m_id as mId,m.mname,m.mcontact,m.phone,m.area_no areaNo,m.address,fur.admin_name adminRealname,
               m.type,m.direct,m.house_number houseNumber,m.enterprise_scale enterpriseScale,m.islock,m.direct,
               a.contract_method contractMethod,ic.invoice_title invoiceTitle,a.realname realName
        FROM merchant m
        LEFT JOIN follow_up_relation fur ON m.m_id=fur.m_id AND fur.reassign=0
        left join admin a on m.admin_id = a.admin_id
        left join invoice_config ic on m.m_id = ic.merchant_id and ic.type = 0 and ic.valid_status = 0
        WHERE m.admin_id=#{adminId}
        <if test="enterpriseScale!=null and enterpriseScale!=''">
           and m.enterprise_scale = #{enterpriseScale}
        </if>
        <if test="islock!=null">
           and m.islock = #{islock}
        </if>
        <if test="direct!=null">
           and m.direct =#{direct}
        </if>
        <if test="merchantName!=null">
            and m.mname like concat(#{merchantName}, '%')
        </if>
        <if test="areaNo!=null">
            and m.area_no = #{areaNo}
        </if>
        <if test="invoiceTitle!=null">
            and ic.invoice_title = #{invoiceTitle}
        </if>
        GROUP BY m.`m_id`
    </select>

    <update id="updateRechargeAmount">
        UPDATE merchant
        SET recharge_amount = recharge_amount + #{amount}
        WHERE m_id = #{mId,jdbcType=BIGINT}
    </update>

    <update id="subtractRechargeAmount">
        UPDATE merchant
        SET recharge_amount = recharge_amount - #{amount}
        WHERE m_id = #{mId,jdbcType=BIGINT}
    </update>

    <select id="queryAllMerchantVO" resultType="net.summerfarm.model.vo.MerchantVO">
        SELECT m.m_id mId, m.mname, m.phone, m.address,m.grade,mw.status whiteListType,m.area_no areaNo,m.size,f.admin_name adminName
        FROM merchant m
        LEFT JOIN follow_up_relation f ON m.m_id = f.m_id
        LEFT JOIN merchant_follow_white_list mw ON mw.m_id = m.m_id
        WHERE m.islock=0
        <if test="mId != null">
            AND m.m_id = #{mId}
        </if>
        <if test="keyword != null">
            AND m.mname  LIKE CONCAT(#{keyword,jdbcType=VARCHAR},'%')
        </if>
        <if test="grade != null">
            AND m.grade = #{grade}
        </if>
        <if test="adminId != null">
            AND f.admin_id = #{adminId} AND f.reassign = 0
        </if>
        <if test="areaNo != null">
            AND m.area_no = #{areaNo}
        </if>
        <if test="size != null">
            AND m.size = #{size}
        </if>
        ORDER BY m.m_id DESC
    </select>

    <update id="updateGrade">
        UPDATE merchant
        SET grade = #{grade}
        WHERE m_id = #{mId,jdbcType=BIGINT}
    </update>


    <select id="selectMerchantListBD"  resultType="net.summerfarm.model.vo.MerchantVO">
        select m.m_id mId, m.mname, m.mcontact, m.phone, m.area_no areaNo, m.remark,m.member_integral memberIntegral,m.size,m.direct,f.admin_name adminName,m.grade
        , f.danger_day dangerDay,f.follow_type followType,f.timing_follow_type timingFollowType,mw.status whiteListType, m.house_number houseNumber,m.address
        from merchant m
        left join follow_up_relation f on m.m_id=f.m_id  and f.reassign=0
        left join merchant_follow_white_list mw on mw.m_id = m.m_id
        WHERE m.islock = 0
            <if test="areaNo != null">
                and m.area_no = #{areaNo}
            </if>
            <if test="mId != null">
                AND m.m_id = #{mId}
            </if>
            <if test="mname != null">
                AND m.mname  LIKE CONCAT('%',#{mname,jdbcType=VARCHAR},'%')
            </if>
            <if test="openid != null">
                AND m.openid = #{openid}
            </if>
            <if test="phone != null">
                AND m.phone = #{phone}
            </if>
            <if test="grade != null">
                AND m.grade = #{grade}
            </if>
            <if test="mSize != null">
                AND m.size != #{mSize}
            </if>
            <if test="adminId != null">
                AND f.admin_id = #{adminId}
            </if>
            <if test="dangerDay != null">
                AND f.danger_day <![CDATA[<=]]> #{dangerDay}
            </if>
            <if test="address != null">
                AND m.address  LIKE CONCAT('%',#{address,jdbcType=VARCHAR},'%')
            </if>
        order by f.danger_day
    </select>
    <select id="selectMerchantByMid"  resultType="net.summerfarm.model.vo.MerchantVO">
        select m.m_id mId, m.mname, m.mcontact, m.phone, m.area_no areaNo, m.area,m.province,m.city,m.address, m.remark,m.member_integral memberIntegral,m.size,m.direct,f.admin_name adminName,m.grade
        ,m.admin_id adminId,a.realname realName, m.poi_note poiNote,m.house_number houseNumber,m.company_brand companyBrand,m.enterprise_scale enterpriseScale,m.last_order_time latestOrderTime,
        a.close_order_time closeOrderTime,mw.status whiteListType,f.follow_type followType,f.timing_follow_type timingFollowType,a.name_remakes nameRemakes,m.operate_status operateStatus
        ,f.admin_id followId
        from merchant m
        left join follow_up_relation f on m.m_id=f.m_id AND reassign = 0
        left JOIN admin a on a.admin_id = m.admin_id
        left join merchant_follow_white_list mw on mw.m_id = m.m_id
        where m.m_id = #{mId}
    </select>
    <select id="queryPrivateSea"  resultType="net.summerfarm.model.vo.MerchantVO">
        select m.m_id mId,m.mname,m.phone,f.reassign_time reassignTime,f.reassign,f.reason,f.id followId,f.follow_type followType,f.add_time addTime,m.area_no areaNo
        ,f.timing_follow_type timingFollowType
        from follow_up_relation f
        left join merchant m on m.m_id=f.m_id
        where m.islock=0 and f.reassign=0
        order by f.reassign_time desc
    </select>

    <select id="queryBDPrivateSea"  resultMap="merchant">
        select m.m_id ,m.mname,m.area_no areaNo ,m.grade,m.size ,m.type,f.admin_id adminId,concat (c.address,ifnull(c.house_number,'')) address,c.contact_id,c.phone,c.contact,c.status,
        (case
          when  f.reassign = 1 then a.realname = null
          else a.realname
        end ) adminName
        from  follow_up_relation f
        left join merchant m on m.m_id=f.m_id
        left join admin a on a.admin_id = f.admin_id
        left join contact c on c.m_id = m.m_id
        <where>
        m.islock=0
            <if test="mId != null">
                AND m.m_id = #{mId}
            </if>
            <if test="reassign != null">
                AND f.reassign = #{reassign}
            </if>
            <if test="mname != null">
                AND mname  LIKE CONCAT('%',#{mname,jdbcType=VARCHAR},'%')
            </if>
            <if test="grade != null">
                AND m.grade = #{grade}
            </if>
            <if test="adminId != null">
                AND f.admin_id = #{adminId}
            </if>
            <if test="address != null">
                AND m.address  LIKE CONCAT('%',#{address,jdbcType=VARCHAR},'%')
            </if>
            <if test="areaNo != null">
                AND m.area_no = #{areaNo}
            </if>
        </where>
    </select>

    <select id="existChannelCode" resultType="boolean">
        select count(0) > 0 from merchant where channel_code = #{channelCode}
    </select>
    <update id="updateChannelCode">
        UPDATE merchant m
        SET m.channel_code = #{channelCode}
        WHERE m.m_id = #{mId}
  </update>
    <select id="selectByMId" resultType="net.summerfarm.model.domain.Merchant">
    /*FORCE_MASTER*/
    select m_id mId,
       role_id roleId,
       mname,
       mcontact,
       openid,
       phone,
       islock,
       rank_id rankId,
       register_time registerTime,
       login_time loginTime,
       invitecode,
       channel_code channelCode,
       inviter_channel_code inviterChannelCode,
       audit_time auditTime,
       audit_user auditUser,
       business_license businessLicense,
       remark,
       shop_sign shopSign,
       other_proof otherProof,
       last_order_time lastOrderTime,
       area_no areaNo,
       size,
       type,
       trade_area tradeArea,
       trade_group tradeGroup,
       unionid,
       mp_openid mpOpenid,
       admin_id adminId,
       direct,
       server,
       pop_view popView,
       member_integral memberIntegral,
       grade,
       sku_show skuShow,
       recharge_amount rechargeAmount,
       cash_amount cashAmount,
       cash_update_time cashUpdateTime,
       merge_admin mergeAdmin,
       merge_time mergeTime,
       house_number houseNumber,
       city,area, poi_note poiNote,
       operate_status operateStatus,
       main_business_type mainBusinessType,
       side_business_type sideBusinessType,
       merchant_chain_type merchantChainType
    from merchant where m_id = #{mId}
    </select>
    <delete id="delete">
        delete from merchant where m_id = #{mId}
    </delete>

    <select id="selectOpenSeas"  resultType="net.summerfarm.model.vo.MerchantVO">
        select m.m_id mId,m.mname,m.phone,m.address,m.last_order_time latestOrderTime,fur.reason,fur.follow_type followType,
        m.area_no areaNo,fur.timing_follow_type timingFollowType,mw.`status` whiteListType,m.grade grade
        FROM follow_up_relation fur
        left join merchant m ON m.m_id = fur.m_id
        left join merchant_follow_white_list mw on mw.m_id = fur.m_id
        where m.islock = 0 and fur.reassign = 1
        <if test="mname !=null">
            and m.mname like CONCAT(#{mname},'%')
        </if>
        <if test="operateStatus !=null">
            and m.operate_status = #{operateStatus}
        </if>
        <if test ="areaNo !=null">
            and m.area_no = #{areaNo}
        </if>
        <if test="grade !=null">
            and m.grade = #{grade}
        </if>
        <if test="followType != null">
            and fur.follow_type=#{followType}
        </if>
        <if test="timingFollowType != null">
            and fur.timing_follow_type = #{timingFollowType}
        </if>
        <if test="whiteListType != null">
            and mw.status = #{whiteListType}
        </if>
        <if test="mId !=null">
            and m.m_id=#{mId}
        </if>
        <if test="address != null">
            AND m.address  LIKE CONCAT('%',#{address},'%')
        </if>
        <if test="orderCurrentMonth != null and orderCurrentMonth">
            and m.last_order_time <![CDATA[ > ]]>  #{addTime}
        </if>
        <if test="orderCurrentMonth != null and !orderCurrentMonth">
            and (m.last_order_time <![CDATA[ < ]]>  #{addTime} or  m.last_order_time is null)
        </if>
        ORDER BY m.last_order_time DESC
    </select>

    <select id="countAllMerchantsToGmv" resultType="java.lang.Integer">
        SELECT count(1)
        FROM (
        SELECT m.m_id mId,m.area_no areaNo
        FROM merchant m
        WHERE m.islock in (0,3)
        <if test="areaNo != null">
            AND m.area_no = #{areaNo}
        </if>
        <if test="startTime != null">
            AND m.register_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            AND m.register_time <![CDATA[<]]> #{endTime}
        </if>
        <if test="dataPermission != null and dataPermission.size!=0">
            AND m.area_no IN
            <foreach collection="dataPermission" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ) t
    </select>

    <select id="queryMerchantWithContact" resultMap="contactsMap">
        select m.m_id,m.province,m.city,m.area,m.address,m.mname,m.phone,m.grade,m.islock,m.area_no,m.size ,m.operate_status
            ,IFNULL(m.last_order_time,'2016-06-19 10:00:00') last_order_time
            ,if(f.reassign = 0,f.admin_id,0) admin_id
            ,if(f.reassign = 0,f.admin_name,'') admin_name,f.danger_day
        from merchant m
        inner join follow_up_relation f on m.m_id = f.m_id
        <where>
            <if test="mId != null">
                and m.m_id = #{mId}
            </if>
        </where>
        limit #{startRow}, #{endRow}
    </select>

    <select id="queryMerchantWithContactToEs" resultMap="esResultMap">
        select m.m_id,m.province,m.city,m.area,m.address,m.mname,m.phone,m.grade,m.islock,m.area_no,m.size ,m.operate_status
        ,m.last_order_time last_order_time,m.register_time,if(f.reassign = 0,f.admin_id,0) admin_id, m.type
        ,if(f.reassign = 0,f.admin_name,'') admin_name,f.danger_day, f.reason, f.reassign_time, f.source, m.business_line, f.release_time, f.protect_reason
        from merchant m
        left join follow_up_relation f on m.m_id = f.m_id
        <where>
            <if test="mId != null">
                and m.m_id = #{mId}
            </if>
        </where>
        limit #{startRow}, #{endRow}
    </select>


    <select id="selectIsUserName" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT m.m_id, m.mname, m.mcontact, m.phone  from merchant m
    <where>
        <if test="mname != null">
            AND m.mname = #{mname}
        </if>
        <if test="phone != null">
            AND m.phone = #{phone}
        </if>
    </where>

  </select>

    <update id="updateMerchantSize">
        update merchant set admin_id = null, size = '单店' , direct = null , sku_show = null , islock = 0
        where m_id = #{mid}
    </update>

    <select id="selectRegisterPrivateSea" resultType="net.summerfarm.model.vo.MerchantVO">
        select m.m_id mId,
               mname,
               mcontact,
               phone,
               province,
               city,
               area,
               address,
               remark,
               area_no areaNo,
               size,
               type,
               unionid,
               direct,
               server,
               grade,
              m.house_number houseNumber
        from merchant m left join follow_up_relation fur on m.m_id = fur.m_id and fur.reassign = 0
        where m.islock = 0
            and fur.admin_id = #{bdAdminId}
            and date(register_time) = #{registerDate}
            and date(m.audit_time) = #{registerDate}
            <if test="mname != null and mname != ''">
                and m.mname like concat('%', #{mname}, '%')
            </if>
    </select>
    <select id="selectByMname" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from merchant where mname = #{mname} AND islock = 0
    </select>

    <select id="selectByNameList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from merchant where islock = 0
        and mname in
        <foreach collection="nameList" item="name" separator="," close=")" open="(">
            #{name}
        </foreach>
    </select>

    <select id="selectMerchantsByParam" resultType="net.summerfarm.model.domain.Merchant">
    select m_id mId,
       role_id roleId,
       mname,
       mcontact,
       openid,
       phone,
       islock,
       rank_id rankId,
       login_time loginTime,
       channel_code channelCode,
       inviter_channel_code inviterChannelCode,
       audit_time auditTime,
       audit_user auditUser,
       area_no areaNo,
       `size`,
       `type`,
       unionid,
       mp_openid mpOpenid,
       admin_id adminId
       from merchant
       where phone = #{phone}
    </select>
    <select id="selectMerchantsBySelectKeys" resultType="net.summerfarm.model.vo.MerchantVO">
        select m_id mId,
        role_id roleId,
        mname,
        mcontact,
        openid,
        phone,
        islock,
        rank_id rankId,
        register_time registerTime,
        login_time loginTime,
        invitecode,
        channel_code channelCode,
        inviter_channel_code inviterChannelCode,
        audit_time auditTime,
        audit_user auditUser,
        business_license businessLicense,
        remark,
        shop_sign shopSign,
        other_proof otherProof,
        last_order_time lastOrderTime,
        area_no areaNo,
        size,
        type,
        trade_area tradeArea,
        trade_group tradeGroup,
        unionid,
        mp_openid mpOpenid,
        admin_id adminId,
        direct,
        server,
        pop_view popView,
        member_integral memberIntegral,
        grade,
        sku_show skuShow,
        recharge_amount rechargeAmount,
        cash_amount cashAmount,
        cash_update_time cashUpdateTime,
        merge_admin mergeAdmin,
        merge_time mergeTime,
        house_number houseNumber,
        company_brand companyBrand,
        enterprise_scale enterpriseScale,
        clue_pool cluePool,
        merchant_type merchantType
        FROM
        merchant
        <where>
            <if test="islock != null">
                AND islock = #{islock}
            </if>
            <if test="adminId != null">
                AND admin_id = #{adminId}
            </if>
            <if test="mId != null">
                AND m_id = #{mId}
            </if>
            <if test="mname != null">
                AND mname = #{mname}
            </if>
            <if test="mnameList != null  and mnameList.size > 0">
                AND mname In
                <foreach collection="mnameList" item="mname" separator="," open="(" close=")">
                    #{mname}
                </foreach>
            </if>
            <if test="phoneList != null and phoneList.size > 0 ">
                AND phone In
                <foreach collection="phoneList" item="phone" separator="," open="(" close=")">
                    #{phone}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updateMerchantType">
        UPDATE merchant m
        SET m.type = #{type}
        WHERE m.m_id = #{mId}
    </update>

    <update id="updateMerchantAreaNo" parameterType="net.summerfarm.model.vo.MerchantVO">
    update merchant set area_no = #{areaNo}
    where area_no = #{splitAreaNo}
        and province = #{province}
        and city = #{city}
        and area = #{area}
        <if test="mIdList != null and mIdList.size > 0 ">
            AND m_id not In
            <foreach collection="mIdList" item="mId" separator="," open="(" close=")">
                #{mId}
            </foreach>
        </if>
    </update>

    <select id="selectMerchantByMsg" parameterType="net.summerfarm.model.domain.Merchant"
    resultType="java.lang.Long">
        select m_id
        from merchant
        where
        area_no = #{splitAreaNo}
        and province = #{province}
        and city = #{city}
        and area = #{area}
    </select>

    <update id="updateAreaNoByCity" parameterType="net.summerfarm.model.domain.Merchant">
        update merchant set area_no = #{areaNo}
        where city = #{city}
        <if test="area != null">
            AND area = #{area}
        </if>
    </update>

    <select id="selectStoreNum" resultType="integer">
        select IFNULL(count(1),0)
        from merchant
        where islock = 0 and audit_time <![CDATA[<=]]> #{localDateTime}
    </select>

    <select id="merchantCategoryTOP" resultType="net.summerfarm.model.vo.CustomerAnalysisVO">
        SELECT c.id,c.category tradeName,count(oi.category_id) customerPurchaseQuantity
        FROM orders o
        INNER JOIN order_item oi ON o.order_no = oi.order_no
        INNER JOIN category c ON oi.category_id = c.id
        WHERE c.outdated = 0
        AND c.parent_id IS NOT NULL
        AND oi.`status` IN (2,3,6,8,15)
        AND o.m_id = #{mId}
        AND o.order_time <![CDATA[>=]]> #{startTime}
        GROUP BY oi.category_id
        ORDER BY customerPurchaseQuantity DESC
        LIMIT 10
    </select>

    <select id="peerCategoryTOP" resultType="net.summerfarm.model.vo.CustomerAnalysisVO">
        SELECT c.id,c.category peerBrandName,count(oi.category_id) peerPurchaseQuantity FROM orders o
        INNER JOIN order_item oi ON o.order_no = oi.order_no
        INNER JOIN category c ON oi.category_id = c.id
        INNER JOIN
            (SELECT m.m_id FROM merchant m INNER JOIN
                (SELECT type,area_no FROM merchant WHERE m_id = #{mId} AND islock = 0) mta
            ON m.area_no = mta.area_no AND m.type = mta.type) ids
        ON ids.m_id = o.m_id
        WHERE c.outdated = 0 AND c.parent_id IS NOT NULL
        AND oi.`status` IN (2,3,6,8,15)
        AND o.order_time <![CDATA[>=]]> #{startTime}
        GROUP BY oi.category_id
        ORDER BY peerPurchaseQuantity DESC
        LIMIT 10
    </select>

    <select id="whetherNotToBuy" resultType="java.lang.Integer">
        SELECT count(1) FROM orders o
        INNER JOIN order_item oi ON o.order_no = oi.order_no
        WHERE oi.`status` IN (2,3,6,8,15)
        AND o.order_time <![CDATA[>=]]> #{startTime}
        AND o.m_id = #{mId}
        AND oi.category_id = #{categoryId}
    </select>

    <select id="merchantSPUTOP" resultType="net.summerfarm.model.vo.CustomerAnalysisVO">
        SELECT p.pd_id id,p.pd_name tradeName,count(i.pd_id) customerPurchaseQuantity FROM orders o
        INNER JOIN order_item oi ON o.order_no = oi.order_no
        INNER JOIN inventory i ON i.sku = oi.sku
        INNER JOIN products p ON p.pd_id = i.pd_id
        WHERE p.outdated in (-1,0) AND oi.`status` IN (2,3,6,8,15)
        AND o.order_time <![CDATA[>=]]> #{startTime}
        AND o.m_id = #{mId}
        GROUP BY i.pd_id
        ORDER BY customerPurchaseQuantity DESC
        LIMIT 10
    </select>

    <select id="peerSPUTOP" resultType="net.summerfarm.model.vo.CustomerAnalysisVO">
        SELECT p.pd_id id,p.pd_name peerBrandName,count(i.pd_id) peerPurchaseQuantity FROM orders o
        INNER JOIN order_item oi ON o.order_no = oi.order_no
        INNER JOIN inventory i ON i.sku = oi.sku
        INNER JOIN products p ON p.pd_id = i.pd_id
        INNER JOIN
        (SELECT m.m_id FROM merchant m
        INNER JOIN (SELECT type,area_no FROM merchant WHERE m_id = #{mId} AND islock = 0) mta
        ON m.area_no = mta.area_no AND m.type = mta.type) ids ON ids.m_id = o.m_id
        WHERE p.outdated in (-1,0) AND oi.`status` IN (2,3,6,8,15)
        AND o.order_time <![CDATA[>=]]> #{startTime}
        GROUP BY i.pd_id
        ORDER BY peerPurchaseQuantity DESC
        LIMIT 10
    </select>

    <select id="whetherNotToBuySPU" resultType="java.lang.Integer">
        SELECT count(1) FROM orders o
        INNER JOIN order_item oi ON o.order_no = oi.order_no
        INNER JOIN inventory i ON i.sku = oi.sku
        WHERE oi.`status` IN (2,3,6,8,15)
        AND o.order_time <![CDATA[>=]]> #{startTime}
        AND o.m_id = #{mId}
        AND i.pd_id = #{pdId}
    </select>

    <select id="peerSPUMax" resultType="net.summerfarm.model.vo.CustomerAnalysisVO">
        SELECT ids.m_id id,count(ids.m_id) peerPurchaseQuantity FROM orders o
        INNER JOIN order_item oi ON o.order_no = oi.order_no
        INNER JOIN inventory i ON i.sku = oi.sku
        INNER JOIN products p ON p.pd_id = i.pd_id
        INNER JOIN
        (SELECT m.m_id FROM merchant m
        INNER JOIN (SELECT type,area_no FROM merchant WHERE m_id = #{mId} AND islock = 0) mta
        ON m.area_no = mta.area_no AND m.type = mta.type) ids ON ids.m_id = o.m_id
        WHERE p.outdated in (-1,0) AND oi.`status` IN (2,3,6,8,15)
        AND o.order_time <![CDATA[>=]]> #{startTime}
        AND i.pd_id = #{pdId}
        GROUP BY ids.m_id
        ORDER BY peerPurchaseQuantity DESC
        LIMIT 1
    </select>

    <select id="selectNotExamineMerchantInOneDayList" resultType="net.summerfarm.model.vo.NotExamineStoreVO">
        SELECT a.area_name as city,m.area_no areaNo,count(*) as notExamineStoreNum FROM `merchant`  m LEFT JOIN area a on m.area_no = a.area_no   WHERE m.islock = 1 and m.register_time <![CDATA[>=]]> #{oneDayBefore} GROUP BY  m.area_no
    </select>

    <select id="selectNotExamineMerchantMoreThanOneDayList" resultType="net.summerfarm.model.vo.NotExamineStoreVO">
        SELECT  a.area_name as city,m.area_no areaNo,count(*) as notExamineStoreNum FROM `merchant`  m LEFT JOIN area a on m.area_no = a.area_no  WHERE m.islock = 1 and m.register_time <![CDATA[<]]> #{oneDayBefore} GROUP BY m.area_no
    </select>

    <select id="selectNotExamineMerchantList" resultType="net.summerfarm.model.vo.NotExamineStoreVO">
        SELECT a.area_name as city,m.area_no areaNo,count(*) as notExamineStoreNum FROM `merchant` m LEFT JOIN area a on m.area_no = a.area_no WHERE m.islock = 1 GROUP BY m.area_no
    </select>
    <select id="selectExtByPrimaryKey" resultType="net.summerfarm.model.domain.Merchant">
        select
            m.m_id, mname, mcontact, phone, islock, rank_id, register_time, login_time,last_order_time,
            invitecode, channel_code, audit_time, audit_user, province, city, area, address,remark,m.area_no areaNo,size,type,
            trade_area,trade_group,admin_id,direct,server,member_integral,recharge_amount,grade,area_no,
            cash_amount,cash_update_time, house_number houseNumber,examine_type examineType
        from merchant m

        where m.m_id = #{mId,jdbcType=BIGINT}

    </select>
    <select id="selectAllByTime" resultType="net.summerfarm.model.vo.MerchantExtVo">
        select m.m_id mId,a.delivery_rule deliveryRule from merchant m
                                                                inner join area a on m.area_no = a.area_no where m.register_time > #{dayEnd}
    </select>
    <select id="fuzzySelectByName" resultType="net.summerfarm.model.domain.Merchant">
        select m.m_id mId,m.mname
        from merchant m
        where m.mname like concat('%', #{mname}, '%') and m.islock = 0;
    </select>

    <select id="selectMerchantAdminId" parameterType="integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from merchant m
        left join invoice_merchant_relation i on m.m_id = i.merchant_id and i.status= 0
        where m.admin_id = #{adminId} and m.islock = 0
    </select>
    <select id="selectNotExistUser" resultType="net.summerfarm.model.domain.Merchant">
        SELECT m.m_id mId, m.mname, m.mcontact, m.phone  from merchant m
        WHERE m.islock = 0 AND m.phone  in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectByMnameSaler" resultType="net.summerfarm.model.vo.MerchantVO">
        select m.m_id         mId,
               m.mname,
               fur.admin_id   salerId,
               fur.admin_name salerName
        from merchant m
        left join follow_up_relation fur ON fur.m_id = m.m_id AND fur.reassign = 0
        where m.mname like concat('%', #{mname}, '%') and m.islock = 0
    </select>

    <select id="selectDetail" resultMap="BaseResultMap" parameterType="net.summerfarm.model.vo.MerchantVO">
        SELECT
        <if test="islock==0 or islock==3">
            fur.admin_name adminRealname,
        </if>
        <if test="islock==1 or islock==2">
            a.realname adminRealname,
        </if>
        m.m_id,m.mname, m.poi_note, m.mcontact, m.openid, m.phone, m.islock, m.rank_id,
        m.login_time,m.pull_black_remark,m.pull_black_operator,m.register_time,
        m.invitecode, m.audit_time, m.audit_user, m.province, m.city, m.area, m.address,m.remark,m.area_no,ar.area_name areaName,ar.map_section,
        m.size,m.type,m.trade_area,m.trade_group, m.inviter_channel_code,m.direct,m.admin_id,m.server, m.area_no areaNo,m.grade,m.show_price showPrice,
        m.clue_pool,m.house_number,m.company_brand,m.enterprise_scale,m.merchant_type,
        examine_type,ar.parent_no,ar.large_area_no,aie.admin_type adminType,aie.realname,aie.name_remakes,m.display_button,m.door_pic
        FROM merchant m
        LEFT JOIN area ar on ar.area_no = m.area_no
        left join admin aie on aie.admin_id = m.admin_id
        <if test="islock==0 or islock==3">
            LEFT JOIN follow_up_relation fur ON m.m_id = fur.m_id AND fur.reassign = 0
        </if>
        <if test="islock==1 or islock==2">
            LEFT JOIN invitecode i on i.invitecode = m.invitecode
            LEFT JOIN admin a on a.admin_id = i.admin_id
        </if>
        where m.m_id = #{mId}
    </select>


    <select id="getMerchantInfoByCId" resultType="net.summerfarm.model.vo.BatchUpdateDeliveryDateVo">
        SELECT
            a.phone,
            m.openid,
            c.phone AS sendPhone
        FROM
            contact c
                LEFT JOIN merchant m ON m.m_id = c.m_id
                LEFT JOIN follow_up_relation fur ON m.m_id = fur.m_id
                AND fur.reassign = 0
                LEFT JOIN admin a ON fur.admin_id = a.admin_id
        WHERE
            c.contact_id = #{contactId}
    </select>

    <select id="listByMIds" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            merchant
        <where>
          m_id in
          <foreach collection="list" item="mId" open="(" separator="," close=")">
            #{mId}
          </foreach>
            and islock =0
        </where>
    </select>

    <select id="getLastMId" resultType="java.lang.Long">
        select m_id from merchant order by m_id desc limit 1
    </select>
    
    <select id="listByRangeId" resultMap="resultMapForListByRangeId">
        select
            m.m_id,ar.large_area_no,m.area_no,m.size,m.type,m.last_order_time,m.register_time
        from
            merchant m left join area ar on m.area_no = ar.area_no
        where
            m_id between #{beginMId} and #{endMId}
            and m.islock =0
    </select>

    <select id="listEsMerchantByMIds" resultMap="resultMapForListByRangeId">
        /*FORCE_MASTER*/
        select
            m.m_id,ar.large_area_no,m.area_no,m.size,m.type,m.last_order_time,m.register_time
        from
            merchant m left join area ar on m.area_no = ar.area_no
        where
        m_id in
        <foreach collection="list" item="mId" open="(" separator="," close=")">
            #{mId}
        </foreach>
        and m.islock =0
    </select>
    <select id="selectSomeMerchants" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM merchant
        <where>
            <if test="pulledBlack != null and pulledBlack">
                AND islock != 3
            </if>
            <if test="areaNos != null and areaNos.size()>0">
                AND area_no IN
                <foreach collection="areaNos" item="areaNo" open="(" close=")" separator=",">
                    #{areaNo}
                </foreach>
            </if>
            <if test="sizes != null and sizes.size()>0">
                AND `size` IN
                <foreach collection="sizes" item="size" open="(" close=")" separator=",">
                    #{size}
                </foreach>
            </if>
            <if test="grades != null and grades.size()>0">
                AND (grade IN
                <foreach collection="grades" item="grade" open="(" close=")" separator=",">
                    #{grade}
                </foreach>
                <if test="gradeIsNull != null and gradeIsNull">
                    OR grade IS NULL
                </if>
                )
            </if>
            <if test="types != null and types.size()>0">
                AND `type` IN
                <foreach collection="types" item="type" open="(" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryContactByAdminId" resultType="java.lang.Long">
        SELECT
            c.contact_id
        FROM
            merchant m
        LEFT JOIN contact c ON m.m_id = c.m_id where m.admin_id= #{adminId} and c.status = 1
    </select>
    <select id="selectByPhone" resultType="java.lang.Long">
        select t.m_id
        from merchant_sub_account t
                 LEFT JOIN merchant m ON t.m_id = m.m_id
        WHERE t.phone like CONCAT(#{phone,jdbcType=VARCHAR},'%')
              and t.delete_flag = 1
    </select>

    <select id="getListByName" resultType="net.summerfarm.model.domain.Merchant">
        select m.m_id mId,m.mname
        from merchant m
        where m.mname like concat('%', #{mName}, '%')
        <if test ="size != null">
            and m.size = #{size}
        </if>
          and m.islock = 0 limit 500;
    </select>

    <select id="selectByAreaNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        merchant
        <where>
            area_no in
            <foreach collection="list" item="areaNo" open="(" separator="," close=")">
                #{areaNo}
            </foreach>
            and islock =0
        </where>
    </select>

    <select id="listByAllMIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from merchant
        where m_id in
        <foreach collection="list" item="mId" open="(" separator="," close=")">
            #{mId}
        </foreach>
    </select>
    <update id="updateMerchantArea">
        update merchant set area_no = #{newAreaNo}
        where city = #{city} and business_line = #{businessLine}
        <if test ="area != null">
            and area = #{area}
        </if>
        <if test ="province != null">
            and province = #{province}
        </if>
    </update>

    <select id="listByAdminId" resultType="java.lang.Integer">
        select m_id
        from merchant
        where admin_id = #{adminId}
    </select>
</mapper>
