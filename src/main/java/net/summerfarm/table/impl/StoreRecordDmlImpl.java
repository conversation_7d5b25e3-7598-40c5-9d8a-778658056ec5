package net.summerfarm.table.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.DtsModelTypeEnum;
import net.summerfarm.enums.StoreRecordType;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.service.price.CostChangeHandlerContext;
import net.summerfarm.table.DbTableDml;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/4/22 15:08
 */
@Slf4j
@Service
public class StoreRecordDmlImpl implements DbTableDml {

    @Resource
    private CostChangeHandlerContext costChangeHandlerContext;

    @Override
    public void tableDml(DtsModel dtsModel) {
        if (Objects.equals(DtsModelTypeEnum.DELETE.name(),dtsModel.getType())) {
            return;
        }
        dtsModel.consumerData(map -> {
            int type = Integer.parseInt(map.get("type"));
            if (type == StoreRecordType.STORE_ALLOCATION_IN.getId() || type == StoreRecordType.PURCHASE_IN.getId()
                    || type == StoreRecordType.TRANSFER_IN.getId()) {
                String sku = map.get("sku");
                int warehouseNo = Integer.parseInt(map.get("area_no"));
                costChangeHandlerContext.handler(warehouseNo, sku);
            }
        });
    }
}
