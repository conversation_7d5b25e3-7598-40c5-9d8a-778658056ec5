package net.summerfarm.controller;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.validation.groups.Add;
import net.summerfarm.facade.wnc.DeliveryFenceQueryFacade;
import net.summerfarm.facade.wnc.dto.ContactStoreNoDTO;
import net.summerfarm.model.domain.Contact;
import net.summerfarm.model.domain.crm.ContactOperateLog;
import net.summerfarm.model.vo.ContactStoreNoVO;
import net.summerfarm.model.vo.merchant.ContactAddressRemarkUpdateVO;
import net.summerfarm.model.vo.merchant.ContactQueryVO;
import net.summerfarm.model.vo.merchant.ContactStoreNoQueryVO;
import net.summerfarm.model.vo.merchant.ContactUpdateVO;
import net.summerfarm.service.MerchantService;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static net.xianmu.common.result.ResultStatusEnum.SERVER_ERROR;

/**
 * 联系人相关
 */
@RestController
@RequestMapping("/merchant")
public class ContactController {
    @Resource
    MerchantService merchantService;
    @Resource
    private DeliveryFenceQueryFacade deliveryFenceQueryFacade;

    /**
     * 修改联系人单独接口信息
     *
     * @param contactUpdateVO
     * @param bindingResult
     * @return
     */
    @ApiOperation(value = "修改联系人信息", httpMethod = "PUT", tags = "客户管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "商户id", paramType = "path", required = true)
    })
     @RequiresPermissions(value = {"merchant:contact:update"})
    @RequestMapping(value = "/updateContact", method = RequestMethod.POST, produces = {"application/json"})
    public AjaxResult<Boolean> updateContact(@Validated @RequestBody ContactUpdateVO contactUpdateVO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(bindingResult.getFieldError().getDefaultMessage());
        }
        return merchantService.updateContactDetail(contactUpdateVO);
    }

    /**
     * 查看联系人操作日志详情
     *
     * @param queryVO
     * @return
     */
    @RequestMapping(value = "/contactLog", method = RequestMethod.POST)
    public CommonResult<PageInfo<ContactOperateLog>> query(@Validated @RequestBody ContactQueryVO queryVO) {
        return merchantService.queryContactLog(queryVO);
    }

    /**
     * 后台添加联系人
     *
     * @param contact
     * @param bindingResult
     * @return
     */
    @ApiOperation(value = "后台添加收货地址", httpMethod = "POST", tags = "客户管理")
    @RequestMapping(value = "/addContact", method = RequestMethod.POST)
    @RequiresPermissions(value = {"merchant:contact:add"})
    public AjaxResult addContact(@Validated(Add.class)@RequestBody Contact contact, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.getError(bindingResult.getFieldError().getDefaultMessage());
        }
        return merchantService.addContact(contact);
    }


    /**
     * 修改联系人单独接口信息
     *
     * @param contactUpdateVO
     * @param bindingResult
     * @return
     */
    @ApiOperation(value = "修改联系人地址备注信息", httpMethod = "PUT", tags = "客户管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "商户id", paramType = "path", required = true)
    })
    @RequestMapping(value = "/update/address-remark", method = RequestMethod.POST, produces = {"application/json"})
    public CommonResult<Boolean> updateAddressRemark(@Validated @RequestBody ContactAddressRemarkUpdateVO contactUpdateVO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return CommonResult.fail(SERVER_ERROR, bindingResult.getFieldError().getDefaultMessage());
        }
        return merchantService.updateAddressRemark(contactUpdateVO);
    }

    /**
     * 根据省省市区获取城配仓信息
     * @return
     */
    @PostMapping("/query/storeNoInfoByAddress")
    public CommonResult<ContactStoreNoVO> queryStoreNoInfoByAddress(@Validated @RequestBody ContactStoreNoQueryVO contactStoreNoQueryVO){
        ContactStoreNoDTO contactStoreNoDTO = deliveryFenceQueryFacade.queryStoreByAddress(contactStoreNoQueryVO.getCity(), contactStoreNoQueryVO.getArea(), contactStoreNoQueryVO.getPoiNote());
        ContactStoreNoVO vo = new ContactStoreNoVO();
        vo.setStoreNo(contactStoreNoDTO.getStoreNo());
        vo.setStoreName(contactStoreNoDTO.getStoreName());
        return CommonResult.ok(vo);
    }


}
