package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.repository.AdminRepository;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.QueryManageAreaInput;
import net.summerfarm.model.input.SaveManageAreaInput;
import net.summerfarm.model.vo.*;
import net.summerfarm.service.ManageAreaService;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ManageAreaServiceImpl extends BaseService implements ManageAreaService {

    @Resource
    CrmManageAreaMapper crmManageAreaMapper;
    @Resource
    CrmManageBdMapper crmManageBdMapper;

    @Resource
    ConfigMapper configMapper;

    @Resource
    AdminMapper adminMapper;


    @Resource
    AdminRepository adminRepository;

    @Resource
    CrmCommissionSkuMapper crmCommissionSkuMapper;

    @Resource
    CrmCommissionMerchantMapper crmCommissionMerchantMapper;

    @Resource
    CrmManageAdministrativeCityMapper crmManageAdministrativeCityMapper;

    /**
     * @param pageIndex 页码
     * @param pageSize 数量
     * @param queryManageAreaInput 查询条件
     * @return 区域配置列表
     */
    @Override
    public AjaxResult selectManageArea(int pageIndex, int pageSize, QueryManageAreaInput queryManageAreaInput) {
        PageHelper.startPage(pageIndex,pageSize);
        List<ManageAreaVo> manageAreaVo = crmManageAreaMapper.selectManageArea(queryManageAreaInput);
        // 查询区域负责运营区域
        for (ManageAreaVo areaVo : manageAreaVo) {
            List<MajorRebate> area = crmManageAreaMapper.selectArea(areaVo.getId(),null);
            areaVo.setAreaCity(area);
        }
        // 查询区域负责行政城市
        for (ManageAreaVo areaVo : manageAreaVo) {
            List<String> cityList = crmManageAdministrativeCityMapper.selectByZoneId(areaVo.getId());
            areaVo.setAdministrativeCitys(cityList);
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(manageAreaVo));
    }
    @Resource
    private AdCodeMsgMapper adCodeMsgMapper;
    @Resource
    private DynamicConfig dynamicConfig;

    /**
     * 编辑区域配置数据
     * @param saveManageAreaInput 插入信息
     * @return 执行成功状态
     */
    @Override
    @Transactional(rollbackOn = { Exception.class })
    public AjaxResult saveManageArea(SaveManageAreaInput saveManageAreaInput) {
        if(dynamicConfig.isManageCustomFenceSwitch()) {
            logger.error("该接口[adCodeMsgMapper.selectByCityList]已下线，请联系管理员处理!");
            throw new BizException("该接口已下线，请联系管理员处理");
        }

        //区域名称不能重复
        int existZoneName = crmManageBdMapper.existZoneName(saveManageAreaInput);
        if(existZoneName>0){
            return AjaxResult.getError(ResultConstant.AREA_NAME_REPEAT,"区域名称已存在");
        }
        // 判断下属城市是否已经在别的区域内
        List<MajorRebate> area = crmManageAreaMapper.selectArea(null,saveManageAreaInput.getId());
        List<Integer> collect = area.stream().map(MajorRebate::getAreaNo).collect(Collectors.toList());
        List<Integer> subCity = saveManageAreaInput.getSubCity();
        collect.retainAll(subCity);
        if (!collect.isEmpty()) {
            return AjaxResult.getError(ResultConstant.AREA_NAME_REPEAT,"下属运营区域需要唯一");
        }
        // 判断负责行政城市是否在别的区域内
        List<String> administrativeCityList = crmManageAdministrativeCityMapper.selectCity(saveManageAreaInput.getId());
        List<String> administrativeCitys = saveManageAreaInput.getAdministrativeCitys();
        administrativeCityList.retainAll(administrativeCitys);
        if(!CollectionUtil.isEmpty(administrativeCityList)){
            return AjaxResult.getError(ResultConstant.AREA_NAME_REPEAT,"下属行政城市需要唯一");
        }
        // 行政城市需要在围栏内
        List<AdCodeMsg> adCodeMsgList = adCodeMsgMapper.selectByCityList(administrativeCitys);
        if(CollectionUtil.isEmpty(adCodeMsgList) || adCodeMsgList.size() != administrativeCitys.size()){
            return AjaxResult.getError(ResultConstant.AREA_NAME_REPEAT,"所选下属行政城市不在围栏内");
        }
        //id为空，保存
        CrmManageBd crmManageBd = new CrmManageBd();
        //取当前用户id
        Integer adminId = getAdminId();
        crmManageBd.setUpdater(adminId);
        BeanUtils.copyProperties(saveManageAreaInput,crmManageBd);
        if(Objects.isNull(saveManageAreaInput.getId())){
            crmManageBd.setCreator(adminId);
            //插入crmMangeBd
            crmManageBdMapper.insert(crmManageBd);
            //插入crmManageArea
            crmManageAreaMapper.insertArea(crmManageBd.getId(),saveManageAreaInput.getSubCity(),adminId);
            // 插入行政城市
            crmManageAdministrativeCityMapper.insertCity(crmManageBd.getId(),saveManageAreaInput.getAdministrativeCitys(),adminId);
        }else{
            crmManageBdMapper.updateByPrimaryKey(crmManageBd);
            // 运营区域 先删除，再新增
            crmManageAreaMapper.deleteArea(crmManageBd.getId());
            crmManageAreaMapper.insertArea(crmManageBd.getId(),saveManageAreaInput.getSubCity(),adminId);
            // 行政城市 先删除,再新增
            crmManageAdministrativeCityMapper.deleteCity(crmManageBd.getId());
            crmManageAdministrativeCityMapper.insertCity(crmManageBd.getId(),saveManageAreaInput.getAdministrativeCitys(),adminId);
        }
        return AjaxResult.getOK();
    }

    /**
     * 删除区域配置数据
     * @param id
     * @return
     */
    @Override
    @Transactional
    public AjaxResult deleteManageArea(int id) {
        //取id对应的区域负责人
        CrmManageBd crmManageBd = crmManageBdMapper.selectByPrimaryKey(id);
        //删除crmMangeBd
        crmManageBdMapper.deleteByPrimaryKey(id);
        //删除crmManageArea表数据
        crmManageAreaMapper.deleteArea(id);
        //删除按件奖励中关于此区域的数据
        crmCommissionSkuMapper.deleteByZoneName(crmManageBd.getZoneName());
        //删除所属的行政城市
        crmManageAdministrativeCityMapper.deleteCity(id);
        //删除拉新奖励中关于此区域的数据
        crmCommissionMerchantMapper.deleteByZoneName(crmManageBd.getZoneName());
        return  AjaxResult.getOK();
    }

    /**
     * 模糊查询区域名称
     * @param zoneName
     * @return
     */

    @Override
    public AjaxResult queryZoneName(String zoneName) {
        List<String> info  = crmManageBdMapper.queryZoneName(zoneName);
        return AjaxResult.getOK(info);
    }

    /**
     * 模糊查询负责人
     * @param adminName
     * @return
     */

    @Override
    public AjaxResult queryAdmin(String adminName) {
        //从配置表取权限
        Config crmAdminAuth = configMapper.selectOne("crm_admin_auth");
        List<Integer> roleIds = Arrays.asList(crmAdminAuth.getValue().split(Global.SEPARATING_SYMBOL)).stream().map(o -> Integer.valueOf(o)).collect(Collectors.toList());
        //根据权限模糊查询负责人名称
        List<ValuesVo> adminVOS = adminRepository.selectByRoleTypesNames(roleIds,adminName);
        return AjaxResult.getOK(adminVOS);
    }

    @Override
    public AjaxResult queryExistArea() {
        Area query = new Area();
        List<AreaResultVo> areaLists = crmManageBdMapper.queryExistArea(query);
        return AjaxResult.getOK(areaLists);
    }

    @Override
    public AjaxResult queryExistCity() {
        List<String> list = crmManageAdministrativeCityMapper.selectByZoneId(null);
        return AjaxResult.getOK(list);
    }

    @Override
    public CrmManageBd getManageBdByAdminId(Integer adminId) {
        CrmManageBd crmManageBd = crmManageBdMapper.getManageBdByAdminId(adminId);
        return crmManageBd;
    }

    @Override
    public CrmManageBd getManageBdByCity(String city) {
        CrmManageBd crmManageBd = crmManageBdMapper.getManageBdByCity(city);
        return crmManageBd;
    }
}
