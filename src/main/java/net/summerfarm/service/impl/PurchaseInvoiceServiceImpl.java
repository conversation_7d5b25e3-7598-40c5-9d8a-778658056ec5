package net.summerfarm.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkworkflow_1_0.models.ProcessForecastRequest;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.DefaultPutRet;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.excel.utils.ExcelUtils;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.object.WalletsEstimatePayInfo;
import net.summerfarm.common.util.*;
import net.summerfarm.common.util.qiNiu.Auth;
import net.summerfarm.common.util.qiNiu.UploadTokenFactory;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.config.CommonDynamicConfig;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.QiNiuConstant;
import net.summerfarm.dingding.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.enums.*;
import net.summerfarm.enums.bms.WalletEnum;
import net.summerfarm.enums.login.SystemLoginTypeEnum;
import net.summerfarm.mapper.FinanceAccountStatementDetailMapper;
import net.summerfarm.mapper.FinanceAccountStatementMapper;
import net.summerfarm.mapper.FinancePurchaseInvoiceWalletsMapper;
import net.summerfarm.mapper.PurchaseSupplierPaymentMapper;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.repository.AdminAuthExtendRepository;
import net.summerfarm.model.DTO.WechatDTO;
import net.summerfarm.model.DTO.srm.WechatTemplateMiniProgramEntity;
import net.summerfarm.model.DTO.srm.WechatTemplateMsgDataEntity;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.domain.easyexcel.ArchiveSummaryBmsDetailExcel;
import net.summerfarm.model.domain.easyexcel.ArchiveSummaryExcel;
import net.summerfarm.model.domain.easyexcel.ArchiveSummaryInvoiceExcel;
import net.summerfarm.model.domain.easyexcel.PurchaseInvoiceAnalysesExcel;
import net.summerfarm.model.input.*;
import net.summerfarm.model.vo.*;
import net.summerfarm.model.vo.bms.BmsPaymentDocumentVO;
import net.summerfarm.module.bms.common.constant.InvoiceEnum;
import net.summerfarm.module.bms.common.constant.QuotationEnum;
import net.summerfarm.module.bms.common.model.BillFeature;
import net.summerfarm.module.bms.facade.FmsFacade;
import net.summerfarm.module.bms.model.output.wallet.WallDetailOutput;
import net.summerfarm.module.bms.model.output.wallet.WalletsEstimatePayOutput;
import net.summerfarm.module.fms.common.constant.InputInvoiceConstants;
import net.summerfarm.module.fms.common.enums.InputInvoiceEnum;
import net.summerfarm.module.fms.common.enums.InputInvoicePoolEnum;
import net.summerfarm.module.fms.common.error.FmsBizErrorEnum;
import net.summerfarm.module.fms.common.error.FmsCommonErrorEnum;
import net.summerfarm.module.pms.PmsServiceClientFacade;
import net.summerfarm.module.pms.model.excel.FinanceAccountDetailExcelVO;
import net.summerfarm.module.pms.model.input.FinanceAccountDetailInput;
import net.summerfarm.mq.MQData;
import net.summerfarm.mq.MType;
import net.summerfarm.mq.constant.SrmMqConstant;
import net.summerfarm.pms.client.resp.statement.StatementResp;
import net.summerfarm.service.*;
import net.summerfarm.service.bms.BmsPaymentDocumentService;
import net.summerfarm.warehouse.mapper.WarehouseStorageCenterMapper;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @title: PurchaseInvoiceServiceImpl
 * @date 2021/8/414:27
 */
@Service
@Slf4j
public class PurchaseInvoiceServiceImpl extends BaseService implements PurchaseInvoiceService {

    @Resource
    private PurchaseInvoiceMapper purchaseInvoiceMapper;

    @Resource
    private ConfigMapper configMapper;

    @Resource
    private PurchaseInvoiceFileMapper purchaseInvoiceFileMapper;

    @Resource
    private SupplierAccountMapper supplierAccountMapper;

    @Resource
    private PurchaseInvoiceLogMapper purchaseInvoiceLogMapper;

    @Resource
    private PurchaseInvoiceAnalysisMapper purchaseInvoiceAnalysisMapper;

    @Resource
    private SupplierMapper supplierMapper;

    @Resource
    private FinanceAccountStatementMapper financeAccountStatementMapper;

    @Resource
    private FinanceAccountStatementDetailMapper financeAccountStatementDetailMapper;

    @Resource
    private FinancePurchaseInvoiceWalletsMapper financePurchaseInvoiceWalletsMapper;

    @Resource
    private FileDownloadRecordMapper fileDownloadRecordMapper;

    @Resource
    private FinancePaymentOrderService financePaymentOrderService;

    @Resource
    private QiNiuService qiNiuService;

    @Resource
    private PurchaseSupplierPaymentMapper purchaseSupplierPaymentMapper;

    @Resource
    private PurchaseBindingPrepaymentService purchaseBindingPrepaymentService;

    @Resource
    private SettlementConfigMapper settlementConfigMapper;

    @Resource
    private FinanceOperatorLogMapper financeOperatorLogMapper;

    @Resource
    private AdminAuthExtendMapper adminAuthExtendMapper;

    @Resource
    private AdminAuthExtendRepository adminAuthExtendRepository;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;
    @Resource
    private WarehouseStorageCenterMapper warehouseStorageCenterMapper;

    @Resource
    private CarrierService carrierService;

    @Resource
    private BmsPaymentDocumentService bmsPaymentDocumentService;

    @Resource
    private FinanceInvoicePartnerMapper financeInvoicePartnerMapper;
    @Resource
    private FinanceAccountVerificationService financeAccountVerificationService;
    @Lazy
    @Resource
    private FinanceAccountStatementService financeAccountStatementService;

    @Autowired
    MqProducer mqProducer;

    @Resource
    private DynamicConfig dynamicConfig;

    @Resource
    private CommonDynamicConfig commonDynamicConfig;

    @Resource
    private PmsServiceClientFacade pmsServiceClientFacade;

    @Resource
    private FmsFacade fmsFacade;

    @Resource
    private WalletsCommonService walletsCommonService;

    private static final Logger logger = LoggerFactory.getLogger(PurchaseInvoiceService.class);

    /**
     * 上传参数不为空
     */
    private static final String UPLOAD_PARAMETER_NOT_NULL = "上传参数不为空";

    /**
     * 符合规范
     */
    private static final String REVIEW_SPECIFICATION = "符合规范";

    /**
     * 票夹数量上限
     */
    private static final String WALLETS_NUM = "wallets_num";

    /**
     * 票夹金额上限
     */
    private static final String WALLETS_PRICE = "wallets_price";

    public static final String SUCCESS = "SUCCESS";

    public static final Integer ONE_HUNDRED = 100;

    /**
     * 微信模板API
     */
    public static String TEMPLATE_ID = "IbDsVk5XlKwO3wLYJZgBb3hc-wT0u1u9SePunCebN7s";

    /**
     * 跳转开关key
     */
    public static String WECHAT_TEMPLATE_JUMP_SWITCH = "WECHAT_TEMPLATE_JUMP_SWITCH";

    public static String WECHAT_TEMPLATE_JUMP_SWITCH_ON = "on";

    public static final String INVOICE_LOCK_PREFIX = "lock:INVOICE_LOCK_PREFIX:%s";


    @Override
    public AjaxResult selectCanMatch(Integer pageIndex, Integer pageSize, PurchaseInvoiceQuery purchaseInvoiceQuery) {
        PageHelper.startPage(pageIndex, pageSize);
        List<PurchaseInvoiceVO> purchaseInvoiceVOS = purchaseInvoiceMapper.selectList(purchaseInvoiceQuery);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(purchaseInvoiceVOS));
    }

    @Override
    public AjaxResult selectToBeSubmitted(Integer pageIndex, Integer pageSize, PurchaseInvoiceQuery purchaseInvoiceQuery) {
        PageHelper.startPage(pageIndex, pageSize);
        List<PurchaseInvoiceVO> purchaseInvoiceVOS = purchaseInvoiceMapper.selectListWait(purchaseInvoiceQuery);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(purchaseInvoiceVOS));
    }

    @Override
    public AjaxResult selectToBeFiled(Integer pageIndex, Integer pageSize, FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        PageHelper.startPage(pageIndex, pageSize);
        List<FinancePurchaseInvoiceWalletsVO> financePurchaseInvoiceWalletsVOS = financePurchaseInvoiceWalletsMapper.selectList(financePurchaseInvoiceWalletsInput);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(financePurchaseInvoiceWalletsVOS));
    }

    @Override
    public AjaxResult selectWaitingArchiving(Integer pageIndex, Integer pageSize, FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        PageHelper.startPage(pageIndex, pageSize);
        List<FinancePurchaseInvoiceWalletsVO> financePurchaseInvoiceWalletsVOS = financePurchaseInvoiceWalletsMapper.selectList(financePurchaseInvoiceWalletsInput);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(financePurchaseInvoiceWalletsVOS));
    }

    @Override
    public AjaxResult selectArchived(Integer pageIndex, Integer pageSize, FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        PageHelper.startPage(pageIndex, pageSize);
        List<FinancePurchaseInvoiceWalletsVO> financePurchaseInvoiceWalletsVOS = financePurchaseInvoiceWalletsMapper.selectList(financePurchaseInvoiceWalletsInput);
        for (FinancePurchaseInvoiceWalletsVO financePurchaseInvoiceWalletsVO : financePurchaseInvoiceWalletsVOS) {
            BigDecimal result = this.calcInvoiceTax(financePurchaseInvoiceWalletsVO.getId());

            financePurchaseInvoiceWalletsVO.setDeductibleTax(result);
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(financePurchaseInvoiceWalletsVOS));
    }

    @NotNull
    private BigDecimal calcInvoiceTax(Long id) {
        BigDecimal deductibleTax = purchaseInvoiceMapper.addDeductibleTax(id);
        if (Objects.isNull(deductibleTax)) {
            deductibleTax = BigDecimal.ZERO;
        }
        BigDecimal deductibleTaxRed = purchaseInvoiceMapper.addDeductibleTaxRed(id);
        if (Objects.isNull(deductibleTaxRed)) {
            deductibleTaxRed = BigDecimal.ZERO;
        }
        BigDecimal result = deductibleTax.subtract(deductibleTaxRed);
        return result;
    }

    @Override
    public void upload(PurchaseInvoiceQuery purchaseInvoiceQuery) {

        List<PurchaseInvoiceVO> purchaseInvoiceVOS = purchaseInvoiceMapper.selectList(purchaseInvoiceQuery);
        if (CollectionUtils.isEmpty(purchaseInvoiceVOS)) {
            throw new DefaultServiceException("暂无数据");
        }

        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();

        //设置单元格宽度
        sheet.setAutobreaks(true);
        sheet.setColumnWidth(0, 5000);
        sheet.setColumnWidth(1, 5000);
        sheet.setColumnWidth(2, 5000);
        sheet.setColumnWidth(3, 3000);
        sheet.setColumnWidth(4, 3000);
        sheet.setColumnWidth(5, 3000);
        sheet.setColumnWidth(6, 7000);
        sheet.setColumnWidth(7, 7000);

        if (Objects.equals(purchaseInvoiceQuery.getStatus(), PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId())) {
            canMatchUpload(purchaseInvoiceVOS, workbook, sheet, purchaseInvoiceQuery);
        } else {
            throw new DefaultServiceException("导出异常");
        }

    }

    /**
     * 待匹配列表数据下载
     *
     * @param purchaseInvoiceVOS
     * @param workbook
     * @param sheet
     */
    private void canMatchUpload(List<PurchaseInvoiceVO> purchaseInvoiceVOS, Workbook workbook, Sheet sheet, PurchaseInvoiceQuery purchaseInvoiceQuery) {

        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("开票日期");
        title.createCell(1).setCellValue("发票代码");
        title.createCell(2).setCellValue("发票号码");
        title.createCell(3).setCellValue("平均税率");
        title.createCell(4).setCellValue("含税金额");
        title.createCell(5).setCellValue("发票字体颜色");
        title.createCell(6).setCellValue("发票销售方名称");
        title.createCell(7).setCellValue("提交时间");

        int index = 1;
        for (PurchaseInvoiceVO purchaseInvoiceVO : purchaseInvoiceVOS) {
            Row row = sheet.createRow(index);
            row.createCell(0).setCellValue(purchaseInvoiceVO.getBillingDate().toString());
            row.createCell(1).setCellValue(purchaseInvoiceVO.getInvoiceCode());
            row.createCell(2).setCellValue(purchaseInvoiceVO.getInvoiceNumber());
            row.createCell(3).setCellValue(purchaseInvoiceVO.getTaxRate() + "%");
            row.createCell(4).setCellValue(purchaseInvoiceVO.getIncludedTax().toString());
            if (Objects.equals(purchaseInvoiceVO.getInvoiceTypeFace(), PurchaseInvoiceAnalysisEnum.RED_INK_INVOICE.getId())) {
                row.createCell(5).setCellValue(PurchaseInvoiceAnalysisEnum.RED_INK_INVOICE.getStatus());
            } else if (Objects.equals(purchaseInvoiceVO.getInvoiceTypeFace(), PurchaseInvoiceAnalysisEnum.BLUE_INK_INVOICE.getId())) {
                row.createCell(5).setCellValue(PurchaseInvoiceAnalysisEnum.BLUE_INK_INVOICE.getStatus());
            }
            row.createCell(6).setCellValue(purchaseInvoiceVO.getSupplierName());
            row.createCell(7).setCellValue(BaseDateUtils.localDateTimeToString(purchaseInvoiceVO.getUpdateTime()));
            index++;
        }

        try {
            ExcelUtils.outputExcel(workbook, "待匹配发票" + purchaseInvoiceQuery.getBillingDateStart().toString() + "-" + purchaseInvoiceQuery.getBillingDateEnd().toString() + ".xls", RequestHolder.getResponse());
        } catch (IOException e) {
            throw new ProviderException(FmsBizErrorEnum.INVOICE_EXPORT_ERROR);
        }

    }

    @Override
    public AjaxResult includedTaxAmount(PurchaseInvoiceQuery purchaseInvoiceQuery) {
        PurchaseInvoiceResultVO purchaseInvoiceResultVO = null;
        if (Objects.equals(PurchaseInvoiceEnum.TO_BE_SUBMITTED_STATUS.getId(), purchaseInvoiceQuery.getStatus())) {
            purchaseInvoiceResultVO = purchaseInvoiceMapper.sumWaiting(purchaseInvoiceQuery);
            return AjaxResult.getOK(purchaseInvoiceResultVO);

        }
        if (Objects.equals(PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId(), purchaseInvoiceQuery.getStatus())) {
            purchaseInvoiceResultVO = purchaseInvoiceMapper.sumMatching(purchaseInvoiceQuery);
            return AjaxResult.getOK(purchaseInvoiceResultVO);

        }
        purchaseInvoiceResultVO = purchaseInvoiceMapper.selectListSum(purchaseInvoiceQuery);
        return AjaxResult.getOK(purchaseInvoiceResultVO);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public AjaxResult save(PurchaseInvoiceVO purchaseInvoiceVO) {
        purchaseInvoiceVO.setCreator(getAdminName());
        purchaseInvoiceVO.setCreatorAdminId(getAdminId());
        purchaseInvoiceVO.setInvoiceCommit(InputInvoiceEnum.InvoiceCommit.PURCHASE.getValue());
        purchaseInvoiceVO.setInvoiceUpdater(InputInvoiceEnum.InvoiceUpdater.PURCHASE.getValue());
        //新增的这条数据直接提交匹配，变成待匹配状态，数据全部需要校验
        //正常的保存则未待提交状态,可全部为空，如果存在发票编码则校验
        if (Objects.equals(InputInvoicePoolEnum.WriteTypeAction.SUBMIT.getValue(), purchaseInvoiceVO.getWriteType())) {
            //检验新增采购发票内容是否为空
            String checkSubmit = checkSubmitNull(purchaseInvoiceVO);
            if (!Objects.equals(UPLOAD_PARAMETER_NOT_NULL, checkSubmit)) {
                return AjaxResult.getErrorWithMsg(checkSubmit);
            }
            String check = checkSubmit(purchaseInvoiceVO);
            if (!Objects.equals(check, REVIEW_SPECIFICATION)) {
                return AjaxResult.getErrorWithMsg(check);
            }
            toBeSubmitted(purchaseInvoiceVO, PurchaseInvoiceLogEnum.IN_USE_STATE.getId(), ExistenceEnum.EXISTENCE.getId(),PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());
            //记录可匹配状态（生效状态）
//            addPurchaseInvoiceLog(purchaseInvoiceVO.getId(), PurchaseInvoiceLogEnum.IN_USE_STATE.getId(), PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());
            return AjaxResult.getOK();
        }
        //保存
        if (Objects.equals(InputInvoicePoolEnum.WriteTypeAction.SAVE.getValue(), purchaseInvoiceVO.getWriteType())) {
            String s = checkSubmit(purchaseInvoiceVO);
            if (!Objects.equals(s, REVIEW_SPECIFICATION)) {
                return AjaxResult.getErrorWithMsg(s);
            }
            //有发票地址
            if (!CollectionUtils.isEmpty(purchaseInvoiceVO.getFileAdd())) {
                toBeSubmitted(purchaseInvoiceVO, PurchaseInvoiceLogEnum.IN_USE_STATE.getId(), ExistenceEnum.EXISTENCE.getId(),PurchaseInvoiceEnum.TO_BE_SUBMITTED_STATUS.getId());
                return AjaxResult.getOK();
            }
            toBeSubmitted(purchaseInvoiceVO, PurchaseInvoiceLogEnum.IN_USE_STATE.getId(), ExistenceEnum.NON_EXISTENT.getId(),PurchaseInvoiceEnum.TO_BE_SUBMITTED_STATUS.getId());
            return AjaxResult.getOK();
        }
        return AjaxResult.getErrorWithMsg("没有接收到提交数据");
    }
//
//    @Override
//    @Transactional(rollbackFor = RuntimeException.class)
//    public String srmSave(PurchaseInvoiceVO purchaseInvoiceVO) {
//        purchaseInvoiceVO.setCreator(purchaseInvoiceVO.getSupplierName());
//        purchaseInvoiceVO.setCreatorAdminId(purchaseInvoiceVO.getSupplierId());
//        purchaseInvoiceVO.setInvoiceCommit(InputInvoiceEnum.InvoiceCommit.SUPPLIER.getValue());
//        purchaseInvoiceVO.setInvoiceUpdater(InputInvoiceEnum.InvoiceUpdater.SUPPLIER.getValue());
//        //新增的这条数据直接提交匹配，变成待匹配状态，数据全部需要校验
//        //正常的保存则未待提交状态,可全部为空，如果存在发票编码则校验
//        //检验新增采购发票内容是否为空
//        String checkSubmit = checkSubmitNull(purchaseInvoiceVO);
//        if (!Objects.equals(UPLOAD_PARAMETER_NOT_NULL, checkSubmit)) {
//            return checkSubmit;
//        }
//        String check = checkSubmit(purchaseInvoiceVO);
//        if (!Objects.equals(check, REVIEW_SPECIFICATION)) {
//            return check;
//        }
//
//        //添加采购发票主表数据
//        purchaseInvoiceVO.setInvoiceAttachment(ExistenceEnum.EXISTENCE.getId());
//        purchaseInvoiceVO.setCreatorAdminId(getAdminId());
//        purchaseInvoiceVO.setSupplierId(purchaseInvoiceVO.getSupplierId());
//        purchaseInvoiceVO.setInvoiceSupplierType(InvoiceSupplierTypeEnum.SUPPLIER.getId());
//        purchaseInvoiceMapper.insertSelective(purchaseInvoiceVO);
//        log.info("srm新增采购发票" + purchaseInvoiceVO.getId());
//        Integer id = purchaseInvoiceVO.getId();
//        //有无发票图片地址
//        if (Objects.equals(ExistenceEnum.EXISTENCE.getId(), purchaseInvoiceVO.getInvoiceAttachment())) {
//            //添加附件地址信息
//            PurchaseInvoiceFile purchaseInvoiceFile = new PurchaseInvoiceFile();
//            purchaseInvoiceFile.setPurchaseInvoiceId(purchaseInvoiceVO.getId());
//            purchaseInvoiceFile.setCreator(purchaseInvoiceVO.getSupplierName());
//            purchaseInvoiceFile.setUpdater(purchaseInvoiceVO.getSupplierName());
//            purchaseInvoiceVO.getFileAdd().forEach(f -> {
//                purchaseInvoiceFile.setFileAddress(f);
//                purchaseInvoiceFileMapper.insert(purchaseInvoiceFile);
//            });
//        }
//        //符合规范，添加数据，先记录下待提交状态
//        //addPurchaseInvoiceLog(id, PurchaseInvoiceLogEnum.TO_BE_SUBMITTED_STATE.getId(), PurchaseInvoiceEnum.TO_BE_SUBMITTED_STATUS.getId());
//        PurchaseInvoiceLog purchaseInvoiceLog = new PurchaseInvoiceLog();
//        purchaseInvoiceLog.setPurchaseInvoiceId(id);
//        purchaseInvoiceLog.setState(PurchaseInvoiceLogEnum.TO_BE_SUBMITTED_STATE.getId());
//        purchaseInvoiceLog.setStatus(PurchaseInvoiceEnum.TO_BE_SUBMITTED_STATUS.getId());
//        purchaseInvoiceLog.setCreator(getAdminName());
//        purchaseInvoiceLog.setUpdater(getAdminName());
//        purchaseInvoiceLogMapper.insert(purchaseInvoiceLog);
//        //记录可匹配状态（生效状态）
//        //addPurchaseInvoiceLog(purchaseInvoiceVO.getId(), PurchaseInvoiceLogEnum.IN_USE_STATE.getId(), PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());
//        PurchaseInvoiceLog invoiceLog = new PurchaseInvoiceLog();
//        invoiceLog.setPurchaseInvoiceId(purchaseInvoiceVO.getId());
//        invoiceLog.setState(PurchaseInvoiceLogEnum.IN_USE_STATE.getId());
//        invoiceLog.setStatus(PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());
//        invoiceLog.setCreator(getAdminName());
//        invoiceLog.setUpdater(getAdminName());
//        purchaseInvoiceLogMapper.insert(invoiceLog);
//
//        return null;
//    }

    @Override
    public PurchaseInvoiceVO queryInvoiceDetail(Integer id) {
        PurchaseInvoiceVO purchaseInvoiceList = purchaseInvoiceMapper.selectByVO(id);
        PurchaseInvoiceLog purchaseInvoiceLog = purchaseInvoiceLogMapper.selectById(id);
        List<String> fileAddress = purchaseInvoiceMapper.fileAddress(purchaseInvoiceList.getPurchaseInvoiceId(), purchaseInvoiceLog.getStatus());
        purchaseInvoiceList.setFileAdd(fileAddress);
        return purchaseInvoiceList;
    }

    /**
     * 提交新增采购发票数据
     *
     * @param purchaseInvoiceVO
     * @param status
     */
    private void toBeSubmitted(PurchaseInvoiceVO purchaseInvoiceVO, Integer state, Integer invoiceAttachment,Integer status) {
        //添加采购发票主表数据
        purchaseInvoiceVO.setInvoiceAttachment(invoiceAttachment);
        purchaseInvoiceVO.setCreatorAdminId(getAdminId());
        FinanceInvoicePartner financeInvoicePartner = financeInvoicePartnerMapper.selectByPrimaryKey(Long.valueOf(purchaseInvoiceVO.getSupplierId()));
        purchaseInvoiceVO.setSupplierId(financeInvoicePartner.getSupplierId());
        purchaseInvoiceVO.setInvoiceSupplierType(financeInvoicePartner.getSupplierType());
        addInvoice(purchaseInvoiceVO, state, status);

    }

    private void addInvoice(PurchaseInvoiceVO purchaseInvoiceVO, Integer state, Integer status) {
        if(commonDynamicConfig.getInputInvoiceSwitch()){
            purchaseInvoiceVO.setStatus(status);
            if(Objects.equals(purchaseInvoiceVO.getInvoiceSupplierType(), InvoiceSupplierTypeEnum.SUPPLIER.getId())){
                SupplierReq supplierReq = supplierMapper.selectDetail(purchaseInvoiceVO.getSupplierId());
                if(supplierReq!=null){
                    purchaseInvoiceVO.setPurchaser(supplierReq.getManager());
                }
            }
            fmsFacade.createInputInvoice(purchaseInvoiceVO);
        }else{
            purchaseInvoiceMapper.insertSelective(purchaseInvoiceVO);
            log.info("新增采购发票" + purchaseInvoiceVO.getId());
            Integer id = purchaseInvoiceVO.getId();
            //有无发票图片地址
            if (Objects.equals(ExistenceEnum.EXISTENCE.getId(), purchaseInvoiceVO.getInvoiceAttachment())) {
                addFileAddress(purchaseInvoiceVO);
            }
            //符合规范，添加数据，先记录下待提交状态
            addPurchaseInvoiceLog(id, state, status);
        }
    }


    /**
     * 检验新增采购发票内容是否为空
     *
     * @param purchaseInvoiceVO
     * @return
     */
    private String checkSubmitNull(PurchaseInvoiceVO purchaseInvoiceVO) {
        // TODO 这里需要和产品及前端沟通,看下设计为什么是全校验并且返回的文案样式没有隔开
        StringBuffer stringBuffer = new StringBuffer();
        if (ObjectUtils.isEmpty(purchaseInvoiceVO.getBillingDate())) {
            stringBuffer.append("开票日期不可为空");
        }
        if (ObjectUtils.isEmpty(purchaseInvoiceVO.getType())) {
            stringBuffer.append("发票销售方类型不可为空");
        }
        if (ObjectUtils.isEmpty(purchaseInvoiceVO.getInvoiceTypeFace())) {
            stringBuffer.append("发票字体颜色不可为空");
        }
        if (ObjectUtils.isEmpty(purchaseInvoiceVO.getInvoiceForm())) {
            stringBuffer.append("发票形式不可为空");
        }
        if (ObjectUtils.isEmpty(purchaseInvoiceVO.getInvoiceType())) {
            stringBuffer.append("发票类型不可为空");
        }
        if (StringUtils.isEmpty(purchaseInvoiceVO.getSupplierName())) {
            stringBuffer.append("供应商名称不可为空");
        }
        if (!InputInvoiceEnum.InvoiceForm.ALL_ELECTRONIC.getValue().equals(purchaseInvoiceVO.getInvoiceForm())
                && StringUtils.isEmpty(purchaseInvoiceVO.getInvoiceCode())) {
            stringBuffer.append("发票代码不可为空");
        }
        if (StringUtils.isEmpty(purchaseInvoiceVO.getInvoiceNumber())) {
            stringBuffer.append("发票号码不可为空");
        }
        if (ObjectUtils.isEmpty(purchaseInvoiceVO.getExcludingTax())) {
            stringBuffer.append("不含税金额不可为空");
        }
        if (ObjectUtils.isEmpty(purchaseInvoiceVO.getTaxAmount())) {
            stringBuffer.append("税额不可为空");
        }
        if (ObjectUtils.isEmpty(purchaseInvoiceVO.getIncludedTax())) {
            stringBuffer.append("含税金额不可为空");
        }
        if (ObjectUtils.isEmpty(purchaseInvoiceVO.getTaxRate())) {
            stringBuffer.append("平均税率不可为空");
        }
        if (stringBuffer.length() < 1) {
            stringBuffer.append(UPLOAD_PARAMETER_NOT_NULL);
        }
        return stringBuffer.toString();
    }

    /**
     * 检验待匹配状态保存的采购发票新增信息
     *
     * @param purchaseInvoiceVO
     * @return
     */
    private String checkSubmit(PurchaseInvoiceVO purchaseInvoiceVO) {
        StringBuffer stringBuffer = new StringBuffer();
        List<PurchaseInvoice> purchaseInvoices = purchaseInvoiceMapper.selectInvoiceId(purchaseInvoiceVO.getInvoiceCode(), purchaseInvoiceVO.getInvoiceNumber());
        if (purchaseInvoices.size() > 1) {
            throw new ProviderException(FmsBizErrorEnum.INVOICE_REPEAT_DATA_ERROR);
        }
        if (!Objects.equals(InputInvoiceEnum.InvoiceForm.ALL_ELECTRONIC.getValue(), purchaseInvoiceVO.getInvoiceForm())
                && (purchaseInvoiceVO.getInvoiceCode().length() < InputInvoiceConstants.InvoiceCode.MIN_LENGTH
                || purchaseInvoiceVO.getInvoiceCode().length() > InputInvoiceConstants.InvoiceCode.MAX_LENGTH)) {
            stringBuffer.append("发票代码的位数不在10-12位数字之间");
        } else if (!Objects.equals(InputInvoiceEnum.InvoiceForm.ALL_ELECTRONIC.getValue(), purchaseInvoiceVO.getInvoiceForm())
                && !Objects.equals(InputInvoiceConstants.InvoiceNumber.DEFAULT_LENGTH, purchaseInvoiceVO.getInvoiceNumber().length())) {
            stringBuffer.append("发票号码不符合八位数字规范");
        } else if (Objects.equals(InputInvoiceEnum.InvoiceForm.ALL_ELECTRONIC.getValue(), purchaseInvoiceVO.getInvoiceForm())
                && !Objects.equals(InputInvoiceConstants.InvoiceNumber.ALL_ELECTRONIC_LENGTH, purchaseInvoiceVO.getInvoiceNumber().length())) {
            stringBuffer.append("全电发票号码不符合二十位数字规范");
        } else if (purchaseInvoices.size() > 0 && !Objects.equals(purchaseInvoiceVO.getId(), purchaseInvoices.get(0).getId())) {
            stringBuffer.append("该发票编码已经被录入");
        } else {
            stringBuffer.append(REVIEW_SPECIFICATION);
        }
        return stringBuffer.toString();
    }

    /**
     * 上传文件地址
     *
     * @param purchaseInvoiceVO
     */
    private void addFileAddress(PurchaseInvoiceVO purchaseInvoiceVO) {
        if(CollUtil.isEmpty(purchaseInvoiceVO.getFileAdd())){
            return;
        }
        //添加附件地址信息
        PurchaseInvoiceFile purchaseInvoiceFile = new PurchaseInvoiceFile();
        purchaseInvoiceFile.setPurchaseInvoiceId(purchaseInvoiceVO.getId());
        purchaseInvoiceFile.setCreator(getAdminName());
        purchaseInvoiceFile.setUpdater(getAdminName());
        purchaseInvoiceVO.getFileAdd().forEach(f -> {
            purchaseInvoiceFile.setFileAddress(f);
            purchaseInvoiceFileMapper.insert(purchaseInvoiceFile);
        });
    }

    /**
     * 采购发票操作日志
     *
     * @param id
     * @param state
     * @param status
     */
    private void addPurchaseInvoiceLog(Integer id, Integer state, Integer status) {
        //操作日志
        PurchaseInvoiceLog purchaseInvoiceLog = new PurchaseInvoiceLog();
        purchaseInvoiceLog.setPurchaseInvoiceId(id);
        purchaseInvoiceLog.setState(state);
        purchaseInvoiceLog.setStatus(status);
        purchaseInvoiceLog.setCreator(getAdminName());
        purchaseInvoiceLog.setUpdater(getAdminName());
        purchaseInvoiceLogMapper.insert(purchaseInvoiceLog);
    }

    @Override
    public AjaxResult checkInvoice(String invoiceCode, String invoiceNumber) {
        int selectInvoiceCodes = purchaseInvoiceMapper.selectInvoiceCodes(invoiceCode, invoiceNumber);
        if (selectInvoiceCodes > 0) {
            return AjaxResult.getOK("重复", 0);
        }
        return AjaxResult.getOK("不重复", 1);
    }

    @Override
    public void download(HttpServletResponse response) throws IOException {
        Map<String, List<List<String>>> data = new HashMap<>();
        List<String> rowData = new ArrayList<>();
        rowData.add("开票日期（yyyy/MM/dd）");
        rowData.add("发票字体颜色");
        rowData.add("发票代码");
        rowData.add("发票号码");
        rowData.add("不含税金额（小数点后，保留2位）");
        rowData.add("税额（小数点后，保留2位）");
        rowData.add("供应商性质");
        rowData.add("发票销售方名称");
        rowData.add("发票销售方税号/身份证号");
        rowData.add("发票类型");
        rowData.add("电子/纸质");
        List<String> oneDate = new ArrayList<>();
        oneDate.add("2021/6/4");
        oneDate.add("红字");
        oneDate.add("3100203130");
        oneDate.add("33181243");
        oneDate.add("99734.51");
        oneDate.add("12965.49");
        oneDate.add("个人");
        oneDate.add("个人真实姓名");
        oneDate.add("身份证号");
        oneDate.add("增值税专用发票");
        oneDate.add("电子");
        List<String> twoDate = new ArrayList<>();
        twoDate.add("2021/6/4");
        twoDate.add("蓝字");
        twoDate.add("3100203130");
        twoDate.add("33181243");
        twoDate.add("99734.51");
        twoDate.add("12965.49");
        twoDate.add("企业");
        twoDate.add("上海鸿权进出口有限公司");
        twoDate.add("91310115786293166W");
        twoDate.add("增值税普通发票");
        twoDate.add("纸质");
        List<List<String>> sheetData = new ArrayList<>();
        sheetData.add(rowData);
        sheetData.add(oneDate);
        sheetData.add(twoDate);
        data.put("发票模板", sheetData);
        ExcelUtils.outputExcel(data, "批量导入发票模板.xls", response);
    }

    @Override
    public AjaxResult selectCanMatchDownload() {

        String fileName = "业务方发票" + System.currentTimeMillis() + ".xls";
        Workbook workbook = new HSSFWorkbook();

        Sheet sheet = workbook.createSheet("发票模板");
        Row title = sheet.createRow(0);
        title.createCell(5).setCellValue("增值税专用发票（电子）");

        Row row = sheet.createRow(1);
        row.createCell(0).setCellValue("序号");
        row.createCell(1).setCellValue("发票代码");
        row.createCell(2).setCellValue("发票号码");
        row.createCell(3).setCellValue("开票日期");
        row.createCell(4).setCellValue("发票状态");
        row.createCell(5).setCellValue("销售方税号");
        row.createCell(6).setCellValue("销售方名称");
        row.createCell(7).setCellValue("购买方税号");
        row.createCell(8).setCellValue("购买方名称");
        row.createCell(9).setCellValue("金额");
        row.createCell(10).setCellValue("税额");
        row.createCell(11).setCellValue("价税合计");
        row.createCell(12).setCellValue("校验码");
        row.createCell(13).setCellValue("销售方地址、电话");
        row.createCell(14).setCellValue("销售方开户行及账号");
        row.createCell(15).setCellValue("购买方地址、电话");
        row.createCell(16).setCellValue("购买方开户行及账号");
        row.createCell(17).setCellValue("密码区");
        row.createCell(18).setCellValue("备注");
        row.createCell(19).setCellValue("开票人");
        row.createCell(20).setCellValue("收款人");
        row.createCell(21).setCellValue("复核人");

        //根据文件名获得token
        Map<String, String> data = UploadTokenFactory.createToken(fileName, QiNiuConstant.DEFAULT_EXPIRES);
        if (ObjectUtils.isEmpty(data)) {
            return AjaxResult.getErrorWithMsg("文件不存在，请重新再试");
        }

        //上传文件至七牛云
        AjaxResult result = qiNiuService.uploadFile(fileName, workbook);
        if (Objects.equals(result.getCode(), SUCCESS)) {
            return AjaxResult.getOK(fileName);
        }
        return AjaxResult.getErrorWithMsg("文件不存在，请重新再试");
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult batchImport(MultipartFile file, Integer invoiceType, Integer invoiceForm) {
        //获取文件对象
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(file.getInputStream());
        } catch (Exception e) {
            throw new ProviderException(FmsCommonErrorEnum.EXCEL_CREATE_ERROR);
        }
        ExcelUtils excelUtils = new ExcelUtils(workbook, 0);
        List<Map<String, String>> mapData = excelUtils.getMapData();
        List<PurchaseInvoiceAnalysis> purchaseInvoiceAnalyses = new ArrayList<>();

        assembleInvoice(mapData, purchaseInvoiceAnalyses, invoiceType, invoiceForm);

        try {
            if (!CollectionUtils.isEmpty(purchaseInvoiceAnalyses)) {
                //是否存在错误数据 上传到七牛云
                String fileName = "批量导入发票-" + getAdminName() + System.currentTimeMillis() + ".xls";

                //根据文件名获得token
                UploadTokenFactory.createToken(fileName, QiNiuConstant.DEFAULT_EXPIRES);

                List<PurchaseInvoiceAnalysesExcel> purchaseInvoiceAnalysesExcels = new ArrayList<>(16);
                for (PurchaseInvoiceAnalysis purchaseInvoiceAnalysis : purchaseInvoiceAnalyses) {
                    PurchaseInvoiceAnalysesExcel purchaseInvoiceAnalysesExcel = new PurchaseInvoiceAnalysesExcel();
                    purchaseInvoiceAnalysesExcel.setBillingDate(purchaseInvoiceAnalysis.getBillingDate());
                    purchaseInvoiceAnalysesExcel.setInvoiceCode(purchaseInvoiceAnalysis.getInvoiceCode());
                    purchaseInvoiceAnalysesExcel.setInvoiceNumber(purchaseInvoiceAnalysis.getInvoiceNumber());
                    purchaseInvoiceAnalysesExcel.setSupplierName(purchaseInvoiceAnalysis.getSupplierName());
                    purchaseInvoiceAnalysesExcel.setReason(purchaseInvoiceAnalysis.getFailReason());
                    purchaseInvoiceAnalysesExcels.add(purchaseInvoiceAnalysesExcel);
                }

                workbook = excelInvoiceAnalyses(purchaseInvoiceAnalysesExcels);

                //上传文件到七牛云
                AjaxResult result = qiNiuService.uploadFile(fileName, workbook);
                log.info("invoke qiNiu upload {} :", JSON.toJSONString(result));

                if (Objects.equals(result.getCode(), SUCCESS)) {
                    JSONObject json = new JSONObject();
                    json.put("invoiceDate", purchaseInvoiceAnalyses);
                    json.put("fileName", fileName);
                    return AjaxResult.getOK(json);
                }
            }
        } catch (Exception ex) {
            log.error("提供方异常,上传七牛云异常,异常信息 = {}", ex.getMessage(), ex);
            return AjaxResult.getErrorWithMsg("模板数据导入异常，请重新导入！");
        }
        return AjaxResult.getOK();
    }

    public Workbook excelInvoiceAnalyses(List<PurchaseInvoiceAnalysesExcel> purchaseInvoiceAnalysesExcels) {
        //构建一个excel对象,这里注意type要是xls不能是xlsx,否则下面的写入后流会关闭,导致报错
        ExcelWriter excelWriter = EasyExcel.write().excelType(ExcelTypeEnum.XLS).build();
        //构建一个sheet页
        WriteSheet firstSheet = EasyExcel.writerSheet("错误信息发票").build();
        WriteTable writeTableOne = EasyExcel.writerTable(0).head(PurchaseInvoiceAnalysesExcel.class).needHead(Boolean.TRUE).build();
        excelWriter.write(purchaseInvoiceAnalysesExcels, firstSheet, writeTableOne);
        return excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
    }

    /**
     * 检验导入数据情况
     *
     * @param mapData
     * @param purchaseInvoiceAnalyses
     * @param invoiceType
     * @param invoiceForm
     */
    private void assembleInvoice(List<Map<String, String>> mapData, List<PurchaseInvoiceAnalysis> purchaseInvoiceAnalyses, Integer invoiceType, Integer invoiceForm) {
        for (Map<String, String> data : mapData) {
            PurchaseInvoiceAnalysis purchaseInvoiceAnalysis = new PurchaseInvoiceAnalysis();
            if (ObjectUtils.isEmpty(data.get("序号"))) {
                break;
            }
            if (!Objects.equals(InputInvoiceEnum.InvoiceForm.ALL_ELECTRONIC.getValue(), invoiceForm) && ObjectUtils.isEmpty(data.get("发票代码"))) {
                insertFailData(purchaseInvoiceAnalysis, data);
                purchaseInvoiceAnalysis.setFailReason("发票代码为空");
                purchaseInvoiceAnalyses.add(purchaseInvoiceAnalysis);
                continue;
            }

            if (ObjectUtils.isEmpty(data.get("发票号码"))) {
                insertFailData(purchaseInvoiceAnalysis, data);
                purchaseInvoiceAnalysis.setFailReason("发票号码为空");
                purchaseInvoiceAnalyses.add(purchaseInvoiceAnalysis);
                continue;
            }

            if (ObjectUtils.isEmpty(data.get("开票日期"))) {
                insertFailData(purchaseInvoiceAnalysis, data);
                purchaseInvoiceAnalysis.setFailReason("开票日期为空");
                purchaseInvoiceAnalyses.add(purchaseInvoiceAnalysis);
                continue;
            } else if(!DateUtils.isValidDate(data.get("开票日期"))) {
                insertFailData(purchaseInvoiceAnalysis, data);
                purchaseInvoiceAnalysis.setFailReason("开票日期格式错误，正确格式：1997-01-01");
                purchaseInvoiceAnalyses.add(purchaseInvoiceAnalysis);
                continue;
            }

            if (ObjectUtils.isEmpty(data.get("销售方税号"))) {
                insertFailData(purchaseInvoiceAnalysis, data);
                purchaseInvoiceAnalysis.setFailReason("销售方税号为空");
                purchaseInvoiceAnalyses.add(purchaseInvoiceAnalysis);
                continue;
            }

            if (ObjectUtils.isEmpty(data.get("销售方名称"))) {
                insertFailData(purchaseInvoiceAnalysis, data);
                purchaseInvoiceAnalysis.setFailReason("销售方名称为空");
                purchaseInvoiceAnalyses.add(purchaseInvoiceAnalysis);
                continue;
            }

            if (ObjectUtils.isEmpty(data.get("金额"))) {
                insertFailData(purchaseInvoiceAnalysis, data);
                purchaseInvoiceAnalysis.setFailReason("金额为空");
                purchaseInvoiceAnalyses.add(purchaseInvoiceAnalysis);
                continue;
            }

            if (ObjectUtils.isEmpty(data.get("税额"))) {
                insertFailData(purchaseInvoiceAnalysis, data);
                purchaseInvoiceAnalysis.setFailReason("税额为空");
                purchaseInvoiceAnalyses.add(purchaseInvoiceAnalysis);
                continue;
            }

            if (ObjectUtils.isEmpty(data.get("价税合计"))) {
                insertFailData(purchaseInvoiceAnalysis, data);
                purchaseInvoiceAnalysis.setFailReason("价税合计为空");
                purchaseInvoiceAnalyses.add(purchaseInvoiceAnalysis);
                continue;
            }
            //查询是否存在
            PurchaseInvoiceVO purchaseInvoiceVO = new PurchaseInvoiceVO();
            purchaseInvoiceVO.setInvoiceCode(Objects.equals(InputInvoiceEnum.InvoiceForm.ALL_ELECTRONIC.getValue(), invoiceForm) ? null : data.get("发票代码"));
            purchaseInvoiceVO.setInvoiceNumber(data.get("发票号码"));
            purchaseInvoiceVO.setInvoiceType(invoiceType);
            purchaseInvoiceVO.setInvoiceForm(invoiceForm);
            String check = checkSubmit(purchaseInvoiceVO);
            if (!Objects.equals(check, REVIEW_SPECIFICATION)) {
                insertFailData(purchaseInvoiceAnalysis, data);
                purchaseInvoiceAnalysis.setFailReason(check);
                purchaseInvoiceAnalyses.add(purchaseInvoiceAnalysis);
                continue;
            }
            String taxNumber = data.get("销售方税号");

            String merchantName = data.get("销售方名称");
            Supplier supplier = supplierMapper.selectByTaxNumber(merchantName,taxNumber);
            if (ObjectUtils.isEmpty(supplier)) {
                CarrierVo carrier = carrierService.queryByTaxNumber(merchantName,taxNumber);
                if (ObjectUtils.isEmpty(carrier)) {
                    insertFailData(purchaseInvoiceAnalysis, data);
                    purchaseInvoiceAnalysis.setFailReason("销售方名称与税号不匹配");
                    purchaseInvoiceAnalyses.add(purchaseInvoiceAnalysis);
                    continue;
                }

                purchaseInvoiceVO.setSupplierId(carrier.getId().intValue());
                purchaseInvoiceVO.setInvoiceSupplierType(InvoiceSupplierTypeEnum.CARRIER.getId());
            } else {

                purchaseInvoiceVO.setSupplierId(supplier.getId());
                purchaseInvoiceVO.setInvoiceSupplierType(InvoiceSupplierTypeEnum.SUPPLIER.getId());
            }
            purchaseInvoiceVO.setSupplierName(data.get("销售方名称"));
            purchaseInvoiceVO.setTaxNumber(data.get("销售方税号"));
            BigDecimal excludingTax = BigDecimal.valueOf(Double.parseDouble(data.get("金额")));
            BigDecimal includedTax = BigDecimal.valueOf(Double.parseDouble(data.get("价税合计")));
            BigDecimal taxAmount = BigDecimal.valueOf(Double.parseDouble(data.get("税额")));
            int taxRate = taxAmount.divide(excludingTax, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(ONE_HUNDRED)).intValue();
            if (excludingTax.compareTo(BigDecimal.ZERO) > 0) {
                purchaseInvoiceVO.setExcludingTax(excludingTax);
                purchaseInvoiceVO.setIncludedTax(includedTax);
                purchaseInvoiceVO.setTaxAmount(taxAmount);
                purchaseInvoiceVO.setTaxRate(taxRate);
                purchaseInvoiceVO.setInvoiceTypeFace(PurchaseInvoiceAnalysisEnum.BLUE_INK_INVOICE.getId());
            } else {
                purchaseInvoiceVO.setExcludingTax(excludingTax.negate());
                purchaseInvoiceVO.setIncludedTax(includedTax.negate());
                purchaseInvoiceVO.setTaxAmount(taxAmount.negate());
                purchaseInvoiceVO.setTaxRate(taxRate);
                purchaseInvoiceVO.setInvoiceTypeFace(PurchaseInvoiceAnalysisEnum.RED_INK_INVOICE.getId());
            }
            //时间格式的日期字段
            DateTimeFormatter df = DateTimeFormatter.ofPattern(DateUtils.SPECIFIC_DATE);
            LocalDate localDate = LocalDate.parse(data.get("开票日期"), df);
            purchaseInvoiceVO.setBillingDate(localDate);
            purchaseInvoiceVO.setInvoiceAttachment(CommonNumbersEnum.ONE.getNumber());
            purchaseInvoiceVO.setCreator(getAdminName());
            purchaseInvoiceVO.setCreatorAdminId(getAdminId());
            purchaseInvoiceVO.setType(PurchaseInvoiceAnalysisEnum.ENTERPRISE_TYPE.getId());
            purchaseInvoiceVO.setInvoiceCommit(InputInvoiceEnum.InvoiceCommit.FINANCE_IMPORT.getValue());
            //验证后没有问题的数据插入
            addInvoice(purchaseInvoiceVO, PurchaseInvoiceLogEnum.IN_USE_STATE.getId(), PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());
//            purchaseInvoiceMapper.insertSelective(purchaseInvoiceVO);
//
//            addPurchaseInvoiceLog(purchaseInvoiceVO.getId(), PurchaseInvoiceLogEnum.IN_USE_STATE.getId(), PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());

        }
    }

    /**
     * 上传失败的数据
     *
     * @param purchaseInvoiceAnalysis
     * @param data
     */
    private void insertFailData(PurchaseInvoiceAnalysis purchaseInvoiceAnalysis, Map<String, String> data) {
        purchaseInvoiceAnalysis.setBillingDate(data.get("开票日期"));
        purchaseInvoiceAnalysis.setInvoiceCode(data.get("发票代码"));
        purchaseInvoiceAnalysis.setInvoiceNumber(data.get("发票号码"));
        purchaseInvoiceAnalysis.setSupplierName(data.get("销售方名称"));
        purchaseInvoiceAnalysis.setTaxNumber(data.get("销售方税号"));
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public AjaxResult importFile(MultipartFile file) {
        Integer adminId = getAdminId();
        checkAdmin();
        Workbook workbook = null;
        try {
            //获取文件对象
            workbook = WorkbookFactory.create(file.getInputStream());
            ExcelUtils en = new ExcelUtils(workbook);
            List<Map<String, String>> allData = en.getMapData();
            List<PurchaseInvoiceAnalysis> purchaseInvoiceAnalyses = new ArrayList<>();
            for (Map<String, String> data : allData) {
                PurchaseInvoiceAnalysis purchaseInvoiceAnalysis = new PurchaseInvoiceAnalysis();
                //yyyy/MM/dd转为yyyy-MM-dd
                List<Integer> collect = Arrays.stream(data.get("开票日期（yyyy/MM/dd）").split("/")).map(Integer::parseInt).collect(Collectors.toList());
                if (Objects.equals(String.valueOf(collect.get(0)).length(), CommonNumbersEnum.FOUR.getNumber())) {
                    //文本格式的日期字段
                    LocalDate localDate = LocalDate.of(collect.get(0), collect.get(1), collect.get(2));
                    String localDateToString = DateUtils.localDateToString(localDate, DateUtils.SPECIFIC_DATE);
                    purchaseInvoiceAnalysis.setBillingDate(localDateToString);
                } else {
                    //时间格式的日期字段
                    LocalDate localDate = LocalDate.of(collect.get(2) + 2000, collect.get(0), collect.get(1));
                    String localDateToString = DateUtils.localDateToString(localDate, DateUtils.SPECIFIC_DATE);
                    purchaseInvoiceAnalysis.setBillingDate(localDateToString);
                }
                if (Objects.equals(data.get("发票字体颜色"), PurchaseInvoiceAnalysisEnum.RED_INK_INVOICE.getStatus())) {
                    purchaseInvoiceAnalysis.setInvoiceTypeFace(PurchaseInvoiceAnalysisEnum.RED_INK_INVOICE.getId());
                } else if (Objects.equals(data.get("发票字体颜色"), PurchaseInvoiceAnalysisEnum.BLUE_INK_INVOICE.getStatus())) {
                    purchaseInvoiceAnalysis.setInvoiceTypeFace(PurchaseInvoiceAnalysisEnum.BLUE_INK_INVOICE.getId());
                }
                purchaseInvoiceAnalysis.setInvoiceCode(data.get("发票代码"));
                purchaseInvoiceAnalysis.setInvoiceNumber(data.get("发票号码"));
                if (Objects.equals(data.get("供应商性质"), PurchaseInvoiceAnalysisEnum.ENTERPRISE_TYPE.getStatus())) {
                    purchaseInvoiceAnalysis.setType(PurchaseInvoiceAnalysisEnum.ENTERPRISE_TYPE.getId());
                } else if (Objects.equals(data.get("供应商性质"), PurchaseInvoiceAnalysisEnum.PERSONAL_TYPE.getStatus())) {
                    purchaseInvoiceAnalysis.setType(PurchaseInvoiceAnalysisEnum.PERSONAL_TYPE.getId());
                }
                purchaseInvoiceAnalysis.setSupplierName(data.get("发票销售方名称"));
                purchaseInvoiceAnalysis.setTaxNumber(data.get("发票销售方税号/身份证号"));
                purchaseInvoiceAnalysis.setExcludingTax(BigDecimal.valueOf(Double.valueOf(data.get("不含税金额（小数点后，保留2位）"))));
                purchaseInvoiceAnalysis.setTaxAmount(BigDecimal.valueOf(Double.valueOf(data.get("税额（小数点后，保留2位）"))));
                BigDecimal excludingTax = BigDecimal.valueOf(Double.valueOf(data.get("不含税金额（小数点后，保留2位）")));
                BigDecimal taxAmount = BigDecimal.valueOf(Double.valueOf(data.get("税额（小数点后，保留2位）")));
                BigDecimal includedTax = excludingTax.add(taxAmount);
                purchaseInvoiceAnalysis.setIncludedTax(includedTax);
                int taxRate = taxAmount.divide(excludingTax, CommonNumbersEnum.TWO.getNumber(), RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(ONE_HUNDRED)).intValue();
                purchaseInvoiceAnalysis.setTaxRate(taxRate);
                if (Objects.equals((data.get("发票类型")), PurchaseInvoiceAnalysisEnum.VAT_ORDINARY_INVOICE.getStatus())) {
                    purchaseInvoiceAnalysis.setInvoiceType(PurchaseInvoiceAnalysisEnum.VAT_ORDINARY_INVOICE.getId());
                } else if (Objects.equals((data.get("发票类型")), PurchaseInvoiceAnalysisEnum.VAT_SPECIAL_INVOICE.getStatus())) {
                    purchaseInvoiceAnalysis.setInvoiceType(PurchaseInvoiceAnalysisEnum.VAT_SPECIAL_INVOICE.getId());
                }
                if (Objects.equals((data.get("电子/纸质")), PurchaseInvoiceAnalysisEnum.ELECTRONICS_TYPE.getStatus())) {
                    purchaseInvoiceAnalysis.setInvoiceForm(PurchaseInvoiceAnalysisEnum.ELECTRONICS_TYPE.getId());
                } else if (Objects.equals(data.get("电子/纸质"), PurchaseInvoiceAnalysisEnum.PAPER_TYPE.getStatus())) {
                    purchaseInvoiceAnalysis.setInvoiceForm(PurchaseInvoiceAnalysisEnum.PAPER_TYPE.getId());
                }
                if (Objects.equals((data.get("发票字体颜色")), PurchaseInvoiceAnalysisEnum.RED_INK_INVOICE.getStatus())) {
                    purchaseInvoiceAnalysis.setInvoiceTypeFace(PurchaseInvoiceAnalysisEnum.RED_INK_INVOICE.getId());
                } else if (Objects.equals(data.get("发票字体颜色"), PurchaseInvoiceAnalysisEnum.BLUE_INK_INVOICE.getStatus())) {
                    purchaseInvoiceAnalysis.setInvoiceTypeFace(PurchaseInvoiceAnalysisEnum.BLUE_INK_INVOICE.getId());
                }
                purchaseInvoiceAnalyses.add(purchaseInvoiceAnalysis);
            }
            Map<String, List<PurchaseInvoiceAnalysis>> collect = purchaseInvoiceAnalyses.stream().collect(Collectors.groupingBy(p -> p.getInvoiceCode() + p.getInvoiceNumber()));
            collect.entrySet().stream().forEach(c -> {
                if (c.getValue().size() > CommonNumbersEnum.ONE.getNumber()) {
                    for (PurchaseInvoiceAnalysis purchaseInvoiceAnalysis : c.getValue()) {
                        purchaseInvoiceAnalysis.setAnalysisType(CommonNumbersEnum.ONE.getNumber());
                        purchaseInvoiceAnalysis.setDeleteStatus(CommonNumbersEnum.ZERO.getNumber());
                        purchaseInvoiceAnalysis.setUpdater(getAdminName());
                        purchaseInvoiceAnalysis.setCreator(getAdminName());
                        purchaseInvoiceAnalysis.setAdminId(adminId);
                        purchaseInvoiceAnalysis.setFailReason("输入的发票编码重复");
                        purchaseInvoiceAnalysisMapper.insert(purchaseInvoiceAnalysis);
                    }
                } else {
                    PurchaseInvoiceAnalysis purchaseInvoiceAnalysis = c.getValue().get(CommonNumbersEnum.ZERO.getNumber());
                    StringBuffer stringBuffer = new StringBuffer();
                    String invoiceCode = purchaseInvoiceAnalysis.getInvoiceCode();
                    String invoiceNumber = purchaseInvoiceAnalysis.getInvoiceNumber();
                    //发票代码10-12位，发票号码8位
                    if (invoiceCode.length() > CommonNumbersEnum.NINE.getNumber() && invoiceCode.length() < CommonNumbersEnum.THIRTEEN.getNumber() && Objects.equals(invoiceNumber.length(), CommonNumbersEnum.EIGHT.getNumber())) {
                        int selectInvoiceCodes = purchaseInvoiceMapper.selectInvoiceCodes(invoiceCode, invoiceNumber);
                        if (selectInvoiceCodes > CommonNumbersEnum.ZERO.getNumber()) {
                            stringBuffer.append("发票编码重复，");
                        }
                    } else {
                        stringBuffer.append("发票编码/代码格式错误，");
                    }
                    String supplierName = purchaseInvoiceAnalysis.getSupplierName();
                    int selectAccountName = supplierAccountMapper.selectAccountName(supplierName);
                    if (selectAccountName < CommonNumbersEnum.ONE.getNumber()) {
                        stringBuffer.append("无此发票销售方名称/个人姓名，");
                    } else if (selectAccountName > CommonNumbersEnum.ONE.getNumber()) {
                        SupplierAccount supplierAccount = supplierAccountMapper.selectName(supplierName);
                        Supplier supplier = supplierMapper.selectManager(supplierAccount.getSupplierId());
                        purchaseInvoiceAnalysis.setPurchaser(supplier.getManager());
                    }
                    String taxNumber = purchaseInvoiceAnalysis.getTaxNumber();
                    int selectTaxNumber = supplierMapper.selectTaxNumber(taxNumber);
                    if (selectTaxNumber < CommonNumbersEnum.ONE.getNumber()) {
                        stringBuffer.append("无此发票销售方税号/身份证号，");
                    }
                    int taxRate = purchaseInvoiceAnalysis.getTaxRate();
                    if (taxRate > CommonNumbersEnum.THIRTEEN.getNumber()) {
                        stringBuffer.append("税率超过13%。");
                    }
                    String failReason = stringBuffer.toString();
                    if (failReason.length() > CommonNumbersEnum.ZERO.getNumber()) {
                        purchaseInvoiceAnalysis.setFailReason(failReason);
                        purchaseInvoiceAnalysis.setAnalysisType(CommonNumbersEnum.ONE.getNumber());
                    } else {
                        purchaseInvoiceAnalysis.setAnalysisType(CommonNumbersEnum.ZERO.getNumber());
                    }
                    purchaseInvoiceAnalysis.setDeleteStatus(CommonNumbersEnum.ZERO.getNumber());
                    purchaseInvoiceAnalysis.setUpdater(getAdminName());
                    purchaseInvoiceAnalysis.setCreator(getAdminName());
                    purchaseInvoiceAnalysis.setAdminId(adminId);
                    purchaseInvoiceAnalysisMapper.insert(purchaseInvoiceAnalysis);
                }
            });
        } catch (Exception e) {
            return AjaxResult.getErrorWithMsg("模板数据导入异常，请重新导入！");
        }
        return AjaxResult.getOK("导入成功");
    }

    private void checkAdmin() {
        Integer adminId = getAdminId();
        String updater = getAdminName();
        int selectFile = purchaseInvoiceAnalysisMapper.selectFile(adminId);
        if (selectFile > CommonNumbersEnum.ZERO.getNumber()) {
            purchaseInvoiceAnalysisMapper.updateStatus(adminId, updater);
        }
    }

    @Override
    public AjaxResult selectSupplier() {
        List<SupplierVO> supplierVOS = supplierMapper.selectList();
        return AjaxResult.getOK(supplierVOS);
    }

    @Override
    public AjaxResult analysis(Integer pageIndex, Integer pageSize, Integer analysisType) {
        Integer adminId = getAdminId();
        PageHelper.startPage(pageIndex, pageSize);
        List<PurchaseInvoiceAnalysis> purchaseInvoiceAnalyses = purchaseInvoiceAnalysisMapper.selectList(adminId, analysisType);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(purchaseInvoiceAnalyses));
    }

    @Override
    public AjaxResult includedTaxSum(Integer analysisType) {
        Integer adminId = getAdminId();
        int selectHaveMoney = purchaseInvoiceAnalysisMapper.selectHaveMoney(adminId, analysisType);
        PurchaseInvoiceAnalysisVO purchaseInvoiceAnalysisVO = new PurchaseInvoiceAnalysisVO();
        if (selectHaveMoney < CommonNumbersEnum.ONE.getNumber()) {
            purchaseInvoiceAnalysisVO.setIncludedTaxSum(BigDecimal.valueOf(CommonNumbersEnum.ZERO.getNumber()));
        } else {
            BigDecimal includedTaxSum = purchaseInvoiceAnalysisMapper.selectIncludedTaxSum(adminId, analysisType);
            purchaseInvoiceAnalysisVO.setIncludedTaxSum(includedTaxSum);
        }
        return AjaxResult.getOK(purchaseInvoiceAnalysisVO);
    }




    @Override
    public void analysisFail() {
        Integer adminId = getAdminId();
        List<PurchaseInvoiceAnalysis> purchaseInvoiceAnalyses = purchaseInvoiceAnalysisMapper.selectList(adminId, 1);
        if (CollectionUtils.isEmpty(purchaseInvoiceAnalyses)) {
            throw new DefaultServiceException("暂无数据");
        }
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();

        //设置单元格宽度
        sheet.setAutobreaks(true);
        sheet.setColumnWidth(0, 6000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 5000);
        sheet.setColumnWidth(3, 4000);
        sheet.setColumnWidth(4, 4000);
        sheet.setColumnWidth(5, 4000);
        sheet.setColumnWidth(6, 4000);
        sheet.setColumnWidth(7, 4000);
        sheet.setColumnWidth(8, 5000);
        sheet.setColumnWidth(9, 7000);
        sheet.setColumnWidth(10, 7000);
        sheet.setColumnWidth(11, 6000);

        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("解析失败原因");
        title.createCell(1).setCellValue("开票日期");
        title.createCell(2).setCellValue("发票字体颜色");
        title.createCell(3).setCellValue("发票代码");
        title.createCell(4).setCellValue("发票号码");
        title.createCell(5).setCellValue("不含税金额");
        title.createCell(6).setCellValue("税额");
        title.createCell(7).setCellValue("税率");
        title.createCell(8).setCellValue("含税金额");
        title.createCell(9).setCellValue("发票销售方名称");
        title.createCell(10).setCellValue("税号/身份证号");
        title.createCell(11).setCellValue("发票类型");
        int index = 1;
        for (PurchaseInvoiceAnalysis purchaseInvoiceAnalysis : purchaseInvoiceAnalyses) {
            Row row = sheet.createRow(index);
            row.createCell(0).setCellValue(purchaseInvoiceAnalysis.getFailReason());
            row.createCell(1).setCellValue(purchaseInvoiceAnalysis.getBillingDate());
            if (Objects.equals(purchaseInvoiceAnalysis.getInvoiceTypeFace(), PurchaseInvoiceAnalysisEnum.BLUE_INK_INVOICE.getId())) {
                row.createCell(2).setCellValue(PurchaseInvoiceAnalysisEnum.BLUE_INK_INVOICE.getStatus());
            }
            if (Objects.equals(purchaseInvoiceAnalysis.getInvoiceTypeFace(), PurchaseInvoiceAnalysisEnum.RED_INK_INVOICE.getId())) {
                row.createCell(2).setCellValue(PurchaseInvoiceAnalysisEnum.RED_INK_INVOICE.getStatus());
            }
            row.createCell(3).setCellValue(purchaseInvoiceAnalysis.getInvoiceCode());
            row.createCell(4).setCellValue(purchaseInvoiceAnalysis.getInvoiceNumber());
            row.createCell(5).setCellValue(String.valueOf(purchaseInvoiceAnalysis.getExcludingTax()));
            row.createCell(6).setCellValue(String.valueOf(purchaseInvoiceAnalysis.getTaxAmount()));
            row.createCell(7).setCellValue(purchaseInvoiceAnalysis.getTaxRate() + "%");
            row.createCell(8).setCellValue(String.valueOf(purchaseInvoiceAnalysis.getIncludedTax()));
            row.createCell(9).setCellValue(purchaseInvoiceAnalysis.getSupplierName());
            row.createCell(10).setCellValue(purchaseInvoiceAnalysis.getTaxNumber());
            if (Objects.equals(purchaseInvoiceAnalysis.getInvoiceForm(), PurchaseInvoiceAnalysisEnum.ELECTRONICS_TYPE.getId())) {
                if (Objects.equals(purchaseInvoiceAnalysis.getInvoiceType(), PurchaseInvoiceAnalysisEnum.VAT_SPECIAL_INVOICE.getId())) {
                    String status = PurchaseInvoiceAnalysisEnum.VAT_SPECIAL_INVOICE.getStatus();
                    row.createCell(11).setCellValue(PurchaseInvoiceAnalysisEnum.ELECTRONICS_TYPE.getStatus() + "-" + status);
                } else if (Objects.equals(purchaseInvoiceAnalysis.getInvoiceType(), PurchaseInvoiceAnalysisEnum.VAT_ORDINARY_INVOICE.getId())) {
                    String status = PurchaseInvoiceAnalysisEnum.VAT_ORDINARY_INVOICE.getStatus();
                    row.createCell(11).setCellValue(PurchaseInvoiceAnalysisEnum.ELECTRONICS_TYPE.getStatus() + "-" + status);
                }
            } else if (Objects.equals(purchaseInvoiceAnalysis.getInvoiceForm(), PurchaseInvoiceAnalysisEnum.PAPER_TYPE.getId())) {
                if (Objects.equals(purchaseInvoiceAnalysis.getInvoiceType(), PurchaseInvoiceAnalysisEnum.VAT_SPECIAL_INVOICE.getId())) {
                    String status = PurchaseInvoiceAnalysisEnum.VAT_SPECIAL_INVOICE.getStatus();
                    row.createCell(11).setCellValue(PurchaseInvoiceAnalysisEnum.PAPER_TYPE.getStatus() + "-" + status);
                } else if (Objects.equals(purchaseInvoiceAnalysis.getInvoiceType(), PurchaseInvoiceAnalysisEnum.VAT_ORDINARY_INVOICE.getId())) {
                    String status = PurchaseInvoiceAnalysisEnum.VAT_ORDINARY_INVOICE.getStatus();
                    row.createCell(11).setCellValue(PurchaseInvoiceAnalysisEnum.PAPER_TYPE.getStatus() + "-" + status);
                }
            }
            index++;

        }
        try {
            ExcelUtils.outputExcel(workbook, "批量导入发票-解析失败模版.xls", RequestHolder.getResponse());
        } catch (IOException e) {
            logger.error(Global.collectExceptionStackMsg(e));
            throw new DefaultServiceException("导出异常");
        }

    }

    @Override
    public AjaxResult check(Integer purchaseInvoiceId, Integer status) {
        //查询指定发票是否存在/有效
        int selectByPrimaryKey = purchaseInvoiceMapper.selectByKey(purchaseInvoiceId);
        PurchaseInvoiceVO purchaseInvoiceVO = new PurchaseInvoiceVO();
        if (selectByPrimaryKey == 0) {
            return AjaxResult.getErrorWithMsg("该发票不存在或者已经被删除");
        }
        if (Objects.equals(status, PurchaseInvoiceEnum.TO_BE_SUBMITTED_STATUS.getId())) {
            //（待提交状态）指定发票详情
            purchaseInvoiceVO = purchaseInvoiceMapper.selectDetailByFirst(purchaseInvoiceId, status);
            updateFinanceInvoicePartnerId(purchaseInvoiceVO);
            if (!ObjectUtils.isEmpty(purchaseInvoiceVO) && !ObjectUtils.isEmpty(purchaseInvoiceVO.getWalletsId())) {
                return AjaxResult.getOK("occupy");
            }
            //文件存储地址
            List<String> fileAddress = purchaseInvoiceMapper.fileAddress(purchaseInvoiceId, status);
            if (!CollectionUtils.isEmpty(fileAddress)) {
                purchaseInvoiceVO.setFileAdd(fileAddress);
            }
            return AjaxResult.getOK(purchaseInvoiceVO);
        }
        if (Objects.equals(status, PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId())) {
            //(待匹配）指定发票详情
            purchaseInvoiceVO = purchaseInvoiceMapper.selectDetailBySecond(purchaseInvoiceId, status);
            updateFinanceInvoicePartnerId(purchaseInvoiceVO);
            if (!ObjectUtils.isEmpty(purchaseInvoiceVO) && !ObjectUtils.isEmpty(purchaseInvoiceVO.getWalletsId())) {
                return AjaxResult.getOK("occupy");
            }
            List<String> fileAddress = purchaseInvoiceMapper.fileAddress(purchaseInvoiceId, status);
            if (!CollectionUtils.isEmpty(fileAddress)) {
                purchaseInvoiceVO.setFileAdd(fileAddress);
            }
            return AjaxResult.getOK(purchaseInvoiceVO);
        }
        if (Objects.equals(status, PurchaseInvoiceEnum.TO_BE_FILED_STATUS.getId())) {
            //票夹下的发票详情
            purchaseInvoiceVO = purchaseInvoiceMapper.selectDetailBySecond(purchaseInvoiceId, PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());
            updateFinanceInvoicePartnerId(purchaseInvoiceVO);
            List<String> fileAddress = purchaseInvoiceMapper.fileAddress(purchaseInvoiceId, PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());
            if (!CollectionUtils.isEmpty(fileAddress)) {
                purchaseInvoiceVO.setFileAdd(fileAddress);
            }
            return AjaxResult.getOK(purchaseInvoiceVO);
        }
        return AjaxResult.getErrorWithMsg("不存在该发票");
    }

    private void updateFinanceInvoicePartnerId(PurchaseInvoiceVO purchaseInvoiceVO) {
        List<FinanceInvoicePartner> financeInvoicePartners = financeInvoicePartnerMapper.selectBySupplierIdAndExpenseType(purchaseInvoiceVO.getInvoiceSupplierType(), Collections.singletonList(purchaseInvoiceVO.getSupplierId()));
        if (!CollectionUtils.isEmpty(financeInvoicePartners)) {
            purchaseInvoiceVO.setSupplierId(financeInvoicePartners.get(0).getId().intValue());
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public AjaxResult update(PurchaseInvoiceVO purchaseInvoiceVO) {
        String adminName = getAdminName();
        //查询指定发票是否存在/有效
        int selectByPrimaryKey = purchaseInvoiceMapper.selectByPrimaryKey(purchaseInvoiceVO.getId());
        if (selectByPrimaryKey < CommonNumbersEnum.ONE.getNumber()) {
            return AjaxResult.getErrorWithMsg("该发票不存在或者已经被删除或者不在待提交状态");
        }
        //提交匹配
        if (Objects.equals(InputInvoicePoolEnum.WriteTypeAction.SUBMIT.getValue(), purchaseInvoiceVO.getWriteType())) {
            String checkSubmit = checkSubmitNull(purchaseInvoiceVO);
            //上传参数为空
            if (!Objects.equals(checkSubmit, UPLOAD_PARAMETER_NOT_NULL)) {
                return AjaxResult.getErrorWithMsg(checkSubmit);
            }
            String check = checkSubmit(purchaseInvoiceVO);
            //不符合规范
            if (!Objects.equals(check, REVIEW_SPECIFICATION)) {
                return AjaxResult.getErrorWithMsg(check);
            }
            purchaseInvoiceVO.setInvoiceAttachment(ExistenceEnum.EXISTENCE.getId());
            FinanceInvoicePartner financeInvoicePartner = financeInvoicePartnerMapper.selectByPrimaryKey(Long.valueOf(purchaseInvoiceVO.getSupplierId()));
            purchaseInvoiceVO.setSupplierId(financeInvoicePartner.getSupplierId());
            purchaseInvoiceVO.setInvoiceSupplierType(financeInvoicePartner.getSupplierType());
            if(commonDynamicConfig.getInputInvoiceSwitch()){
                update2Matching(purchaseInvoiceVO);
            }else{
                purchaseInvoiceMapper.updateByPrimaryKeySelective(purchaseInvoiceVO);
                purchaseInvoiceFileMapper.delete(adminName, purchaseInvoiceVO.getId());
                addFileAddress(purchaseInvoiceVO);
                purchaseInvoiceLogMapper.updateState(purchaseInvoiceVO.getId(), adminName, purchaseInvoiceVO.getStatus());
                //符合规范，添加数据，先记录下待提交状态
                addPurchaseInvoiceLog(purchaseInvoiceVO.getId(), PurchaseInvoiceLogEnum.TO_BE_SUBMITTED_STATE.getId(), PurchaseInvoiceEnum.TO_BE_SUBMITTED_STATUS.getId());
                //记录可匹配状态（生效状态）
                addPurchaseInvoiceLog(purchaseInvoiceVO.getId(), PurchaseInvoiceLogEnum.IN_USE_STATE.getId(), PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());

            }
            return AjaxResult.getOK("提交成功");
        }
        //保存
        if (Objects.equals(InputInvoicePoolEnum.WriteTypeAction.SAVE.getValue(), purchaseInvoiceVO.getWriteType())) {
            String tips = checkSubmit(purchaseInvoiceVO);
            if (!Objects.equals(tips, REVIEW_SPECIFICATION)) {
                return AjaxResult.getErrorWithMsg(tips);
            }
            FinanceInvoicePartner financeInvoicePartner = financeInvoicePartnerMapper.selectByPrimaryKey(Long.valueOf(purchaseInvoiceVO.getSupplierId()));
            purchaseInvoiceVO.setSupplierId(financeInvoicePartner.getSupplierId());
            purchaseInvoiceVO.setInvoiceSupplierType(financeInvoicePartner.getSupplierType());
            //有附件
            if(commonDynamicConfig.getInputInvoiceSwitch()){
                updateDraft(purchaseInvoiceVO);
            }else{
                if (!CollectionUtils.isEmpty(purchaseInvoiceVO.getFileAdd())) {
                    purchaseInvoiceVO.setInvoiceAttachment(ExistenceEnum.EXISTENCE.getId());
                    purchaseInvoiceFileMapper.delete(adminName, purchaseInvoiceVO.getId());
                    addFileAddress(purchaseInvoiceVO);
                } else {
                    purchaseInvoiceFileMapper.delete(adminName, purchaseInvoiceVO.getId());
                    purchaseInvoiceVO.setInvoiceAttachment(ExistenceEnum.NON_EXISTENT.getId());
                }
                purchaseInvoiceMapper.updateByPrimaryKey(purchaseInvoiceVO);
                purchaseInvoiceLogMapper.updateState(purchaseInvoiceVO.getId(), adminName, purchaseInvoiceVO.getStatus());
                addPurchaseInvoiceLog(purchaseInvoiceVO.getId(), PurchaseInvoiceLogEnum.IN_USE_STATE.getId(), PurchaseInvoiceEnum.TO_BE_SUBMITTED_STATUS.getId());

            }
            return AjaxResult.getOK("保存成功");
        }
        //删除
        if (Objects.equals(InputInvoicePoolEnum.WriteTypeAction.DELETE.getValue(), purchaseInvoiceVO.getWriteType())) {
            if(commonDynamicConfig.getInputInvoiceSwitch()){
                fmsFacade.deleteInvoice(purchaseInvoiceVO.getId());
            }else{
                int selectFile = purchaseInvoiceFileMapper.selectFile(purchaseInvoiceVO.getId());
                if (selectFile > CommonNumbersEnum.ZERO.getNumber()) {
                    purchaseInvoiceFileMapper.delete(adminName, purchaseInvoiceVO.getId());
                }
                purchaseInvoiceMapper.delete(purchaseInvoiceVO.getId());
                purchaseInvoiceLogMapper.updateState(purchaseInvoiceVO.getId(), adminName, purchaseInvoiceVO.getStatus());
                addPurchaseInvoiceLog(purchaseInvoiceVO.getId(), PurchaseInvoiceLogEnum.TO_BE_SUBMITTED_STATE.getId(), PurchaseInvoiceEnum.TO_BE_SUBMITTED_STATUS.getId());
            }
            return AjaxResult.getOK("删除成功");

        }
        //修改
        if (Objects.equals(InputInvoicePoolEnum.WriteTypeAction.UPDATE.getValue(), purchaseInvoiceVO.getWriteType())) {
            //发票编码唯一性校验
            //如果是全电,发票代码置空
            if (Objects.equals(purchaseInvoiceVO.getInvoiceForm(), InputInvoiceEnum.InvoiceForm.ALL_ELECTRONIC.getValue())) {
                purchaseInvoiceVO.setInvoiceCode(null);
            }
            List<PurchaseInvoice> invoiceList = purchaseInvoiceMapper.selectInvoiceId(purchaseInvoiceVO.getInvoiceCode(), purchaseInvoiceVO.getInvoiceNumber());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(invoiceList)
                    && invoiceList.stream().anyMatch(data -> !Objects.equals(data.getId(), purchaseInvoiceVO.getId()))) {
                return AjaxResult.getErrorWithMsg("该发票编码已存在");

            }
            FinanceInvoicePartner financeInvoicePartner = financeInvoicePartnerMapper.selectByPrimaryKey(Long.valueOf(purchaseInvoiceVO.getSupplierId()));

            purchaseInvoiceVO.setSupplierId(financeInvoicePartner.getSupplierId());
            purchaseInvoiceVO.setInvoiceSupplierType(financeInvoicePartner.getSupplierType());

            if(commonDynamicConfig.getInputInvoiceSwitch()){
                fmsFacade.updateBindingInvoice(purchaseInvoiceVO);
            }else{
                purchaseInvoiceFileMapper.delete(adminName, purchaseInvoiceVO.getId());
                if (!CollectionUtils.isEmpty(purchaseInvoiceVO.getFileAdd())) {
                    addFileAddress(purchaseInvoiceVO);
                }
                purchaseInvoiceMapper.updateByPrimaryKeySelective(purchaseInvoiceVO);
                //如果是全电,发票代码置空
                if (Objects.equals(purchaseInvoiceVO.getInvoiceForm(), InputInvoiceEnum.InvoiceForm.ALL_ELECTRONIC.getValue())) {
                    purchaseInvoiceMapper.updateInvoiceEmptyByQuanDian(purchaseInvoiceVO.getId());
                }
                purchaseInvoiceLogMapper.updateState(purchaseInvoiceVO.getId(), adminName, purchaseInvoiceVO.getStatus());
                addLog(purchaseInvoiceVO.getId(), PurchaseInvoiceLogEnum.IN_USE_STATE.getId(), PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());
            }

            return AjaxResult.getOK("修改成功");
        }
        return AjaxResult.getErrorWithMsg("操作错误");
    }

    private void updateDraft(PurchaseInvoiceVO purchaseInvoiceVO) {
        if(Objects.equals(purchaseInvoiceVO.getInvoiceSupplierType(), InvoiceSupplierTypeEnum.SUPPLIER.getId())){
            SupplierReq supplierReq = supplierMapper.selectDetail(purchaseInvoiceVO.getSupplierId());
            if(supplierReq!=null){
                purchaseInvoiceVO.setPurchaser(supplierReq.getManager());
            }
        }
        fmsFacade.updateDraftInvoice(purchaseInvoiceVO);
    }

    private void update2Matching(PurchaseInvoiceVO purchaseInvoiceVO) {
        if(Objects.equals(purchaseInvoiceVO.getInvoiceSupplierType(), InvoiceSupplierTypeEnum.SUPPLIER.getId())){
            SupplierReq supplierReq = supplierMapper.selectDetail(purchaseInvoiceVO.getSupplierId());
            if(supplierReq!=null){
                purchaseInvoiceVO.setPurchaser(supplierReq.getManager());
            }
        }
        fmsFacade.updateDraftInvoiceToMatching(purchaseInvoiceVO);
    }

//    @Override
//    public void deleteInvoice(Integer id, String supplierName) {
//        PurchaseInvoiceLog purchaseInvoiceLog = purchaseInvoiceLogMapper.selectById(id);
//        int selectFile = purchaseInvoiceFileMapper.selectFile(id);
//        if (selectFile > CommonNumbersEnum.ZERO.getNumber()) {
//            purchaseInvoiceFileMapper.delete(supplierName, id);
//        }
//        purchaseInvoiceMapper.delete(id);
//        purchaseInvoiceLogMapper.updateState(id, supplierName, purchaseInvoiceLog.getStatus());
//        addPurchaseInvoiceLog(id, PurchaseInvoiceLogEnum.TO_BE_SUBMITTED_STATE.getId(), PurchaseInvoiceEnum.TO_BE_SUBMITTED_STATUS.getId());
//    }

    /**
     * 操作日志
     *
     * @param id
     * @param state
     * @param status
     */
    private void addLog(Integer id, Integer state, Integer status) {
        PurchaseInvoiceLog purchaseInvoiceLog = new PurchaseInvoiceLog();
        purchaseInvoiceLog.setPurchaseInvoiceId(id);
        purchaseInvoiceLog.setState(state);
        purchaseInvoiceLog.setStatus(status);
        purchaseInvoiceLog.setCreator(getAdminName());
        purchaseInvoiceLog.setUpdater(getAdminName());
        purchaseInvoiceLogMapper.insert(purchaseInvoiceLog);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public AjaxResult reviewPassed(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        return walletsCommonService.updateWallets(financePurchaseInvoiceWalletsInput.getId(), () -> {

            FinancePurchaseInvoiceWallets db = financePurchaseInvoiceWalletsMapper.selectByPrimaryKey(financePurchaseInvoiceWalletsInput.getId());
            if(db == null){
                return AjaxResult.getErrorWithMsg("票夹不存在");
            }
            if(!Objects.equals(db.getStatus(), PurchaseInvoiceEnum.TO_BE_FILED_STATUS.getId())){
                log.warn("状态不是待复核状态");
                return AjaxResult.getErrorWithMsg("状态不是待复核状态");
            }

        //发票确认实际抵扣税率、实际可抵扣税额
        if (!CollectionUtils.isEmpty(financePurchaseInvoiceWalletsInput.getPurchaseInvoice())) {
            for (PurchaseInvoice purchaseInvoice : financePurchaseInvoiceWalletsInput.getPurchaseInvoice()) {
                if (ObjectUtils.isEmpty(purchaseInvoice.getActualTaxRate()) && ObjectUtils.isEmpty(purchaseInvoice.getActualTaxAmount())) {
                    purchaseInvoice.setActualTaxAmount(BigDecimal.ZERO);
                    purchaseInvoice.setActualTaxRate(BigDecimal.ZERO);
                }
                if(commonDynamicConfig.getInputInvoiceSwitch()){
                    fmsFacade.updateActualTax(purchaseInvoice);
                }else{
                    purchaseInvoiceMapper.updateByActual(purchaseInvoice);
                }
            }
        }

            if (Objects.equals(financePurchaseInvoiceWalletsInput.getExpenseType(), FinanceExpenseTypeEnum.URBAN_DISTRIBUTION.getId())
                    || Objects.equals(financePurchaseInvoiceWalletsInput.getExpenseType(), FinanceExpenseTypeEnum.TRUNK_BUSINESS.getId())
                    || Objects.equals(financePurchaseInvoiceWalletsInput.getExpenseType(), FinanceExpenseTypeEnum.STORAGE_BMS.getId())
                    || Objects.equals(financePurchaseInvoiceWalletsInput.getExpenseType(), FinanceExpenseTypeEnum.PROXY_WAREHOUSE.getId())) {
                //找到最小的发票id
                Integer minInvoiceId = financePurchaseInvoiceWalletsInput.getPurchaseInvoice().stream().map(PurchaseInvoice::getId).min(Integer::compareTo).orElseThrow(() -> new DefaultServiceException("找不到最小的发票id"));
                //BMS打款单信息接口
                BmsPaymentDocumentVO bmsPaymentDocumentVO = bmsPaymentDocumentService.queryBmsPaymentDocument(minInvoiceId, financePurchaseInvoiceWalletsInput.getExpenseType());

                //生成付款单
                financePaymentOrderService.generatePaymentBill(bmsPaymentDocumentVO);

                //票夹待归档
                walletsStatus(financePurchaseInvoiceWalletsInput, PurchaseInvoiceEnum.WAITING_FOR_ARCHIVING.getId());

                return AjaxResult.getOK();
            }

            List<FinanceAccountStatementVO> financeAccountStatementList = financeAccountStatementMapper.selectByWalletsId(financePurchaseInvoiceWalletsInput.getId());

            //对账单
            FinanceAccountStatement financeAccountStatement = new FinanceAccountStatement();
            financeAccountStatement.setWalletsId(financePurchaseInvoiceWalletsInput.getId());
            //查询待复核阶段的对账单是否全部预付
            BigDecimal i = financeAccountStatementMapper.selectAdd(financePurchaseInvoiceWalletsInput.getId());
            //如果是待复核状态归档，而且有付款，对账单也由待复核归档变为待付款,并且生成付款单
            if (Objects.equals(financePurchaseInvoiceWalletsInput.getStatus(), PurchaseInvoiceEnum.TO_BE_FILED_STATUS.getId()) && i.compareTo(BigDecimal.ZERO) != 0) {
                //生成付款单
                List<FinanceAccountStatement> financeAccountStatementVOS = financeAccountStatementMapper.selectById(financePurchaseInvoiceWalletsInput.getId());
                for (FinanceAccountStatement f : financeAccountStatementVOS) {
                    financePaymentOrderService.generatePaymentDocument(f,db);
                }
                //付款审核中
                financeAccountStatement.setStatus(FinanceAccountStatementStatusEnum.PAYMENT_UNDER_REVIEW.ordinal());

                for (FinanceAccountStatement f : financeAccountStatementVOS) {
                    //更新对账单当前处理人
                    FinanceAccountStatement accountStatement = new FinanceAccountStatement();
                    accountStatement.setId(f.getId());
                    accountStatement.setCurrentProcessor("请到 钉钉-OA审批—我发起的 中查看");
                    financeAccountStatementMapper.updateBack(accountStatement);
                }

                //修改该票夹对账单的状态
                financeAccountStatementMapper.updateByPrimaryKeySelective(financeAccountStatement);
                //票夹待归档
                walletsStatus(financePurchaseInvoiceWalletsInput, PurchaseInvoiceEnum.WAITING_FOR_ARCHIVING.getId());
                //对账单审核情况
                for (FinanceAccountStatementVO financeAccountStatementVO : financeAccountStatementList) {
                    accountPaymentStatus(financeAccountStatementVO.getId());
                }

                return AjaxResult.getOK();
            }
            //如果全部预付，对账单也由待复核归档变为待付款,不生成付款单，核销预付金额
            if ((Objects.equals(financePurchaseInvoiceWalletsInput.getStatus(), PurchaseInvoiceEnum.TO_BE_FILED_STATUS.getId()) && Objects.equals(i.compareTo(BigDecimal.ZERO), 0))) {
                if(BooleanUtils.isTrue(dynamicConfig.getPmsStatementSwitch())){
                    newProcess(financePurchaseInvoiceWalletsInput, financeAccountStatementList);
                }else{
                    oldProcess(financePurchaseInvoiceWalletsInput, financeAccountStatementList, financeAccountStatement);
                }

            }


            if (Objects.equals(financePurchaseInvoiceWalletsInput.getStatus(), PurchaseInvoiceEnum.ARCHIVED_STATUS.getId())) {
                //票夹待归档/归档
                FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets = new FinancePurchaseInvoiceWalletsInput();
                financePurchaseInvoiceWallets.setId(financePurchaseInvoiceWalletsInput.getId());
                financePurchaseInvoiceWallets.setFileTime(financePurchaseInvoiceWalletsInput.getFileTime());
                financePurchaseInvoiceWallets.setRemakes(financePurchaseInvoiceWalletsInput.getRemakes());
                financePurchaseInvoiceWallets.setUpdater(getAdminName());
                financePurchaseInvoiceWallets.setUpdateTime(LocalDateTime.now());
                financePurchaseInvoiceWalletsMapper.updateByPrimaryKeySelective(financePurchaseInvoiceWallets);
            }
            return AjaxResult.getOK();
        });

    }

    private void newProcess(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput, List<FinanceAccountStatementVO> financeAccountStatementList) {
        //查询当前付款人
        SettlementConfig settlementConfig = settlementConfigMapper.selectByConfig();
        Admin select = adminMapper.select(settlementConfig.getPayer());

        List<FinanceAccountStatement> financeAccountStatementVOS = financeAccountStatementMapper.selectById(financePurchaseInvoiceWalletsInput.getId());

        BmsPaymentMsg bmsPaymentMsg = new BmsPaymentMsg();
        bmsPaymentMsg.setType(4);
        bmsPaymentMsg.setBatchIdList(financeAccountStatementVOS.stream().map(FinanceAccountStatement::getId).collect(Collectors.toList()));
        bmsPaymentMsg.setOperatorId(select.getAdminId());
        bmsPaymentMsg.setOperator(select.getRealname());
        mqProducer.send("fms-upload-payment-voucher", "account_advance_statement", JSON.toJSONString(bmsPaymentMsg));


        //票夹归档
        walletsStatus(financePurchaseInvoiceWalletsInput, PurchaseInvoiceEnum.ARCHIVED_STATUS.getId());
        //对账单审核情况
        for (FinanceAccountStatementVO financeAccountStatementVO : financeAccountStatementList) {
            accountPaymentStatus(financeAccountStatementVO.getId());
        }

        //srm账单改状态
        for (FinanceAccountStatement f : financeAccountStatementVOS) {
            //改变srm对账单状态至已完成
            JSONObject json = new JSONObject();
            json.put("id", f.getId());
            String producerMsg = json.toJSONString();
            //将处理结果集合发送至MQ
            MQData mqData = new MQData(MType.SRM_COMPLETED_ACCOUNT.name());
            mqData.setData(producerMsg);
            mqProducer.send(SrmMqConstant.Topic.TOPIC_PMS_SRM, null, JSON.toJSONString(mqData));
        }
    }

    private void oldProcess(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput, List<FinanceAccountStatementVO> financeAccountStatementList, FinanceAccountStatement financeAccountStatement) {
        financeAccountStatement.setStatus(FinanceAccountStatementStatusEnum.HAS_PAY.ordinal());
        List<FinanceAccountStatement> financeAccountStatementVOS = financeAccountStatementMapper.selectById(financePurchaseInvoiceWalletsInput.getId());
        for (FinanceAccountStatement f : financeAccountStatementVOS) {
            if (financeAccountStatementService.isHistoryAccountData(f.getId())) {
                modifyStatement(f.getId());
            } else {
                List<FinanceAccountVerification> financeAccountVerifications = financeAccountVerificationService.generateAccountVerificationListByAccountId(f.getId());
                List<FinanceAccountVerification> payList = financeAccountVerifications.stream().filter(x -> FinanceAccountVerificationTypeEnum.OUTPUT_PAY.getId().equals(x.getOutputType())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(payList)) {
                    throw new DefaultServiceException("对账单核销流水有付款流水，无法核销");
                }
                financeAccountVerificationService.insertBatch(financeAccountVerifications);
            }
        }
        //查询当前付款人
        SettlementConfig settlementConfig = settlementConfigMapper.selectByConfig();
        Admin select = adminMapper.select(settlementConfig.getPayer());
        //修改该票夹对账单的状态
        financeAccountStatement.setCurrentProcessor(select.getRealname());
        financeAccountStatementMapper.updateByPrimaryKeySelective(financeAccountStatement);
        //票夹归档
        walletsStatus(financePurchaseInvoiceWalletsInput, PurchaseInvoiceEnum.ARCHIVED_STATUS.getId());
        //对账单审核情况
        for (FinanceAccountStatementVO financeAccountStatementVO : financeAccountStatementList) {
            accountPaymentStatus(financeAccountStatementVO.getId());
        }

        //srm账单改状态
        for (FinanceAccountStatement f : financeAccountStatementVOS) {
            //改变srm对账单状态至已完成
            JSONObject json = new JSONObject();
            json.put("id", f.getId());
            String producerMsg = json.toJSONString();
            //将处理结果集合发送至MQ
            MQData mqData = new MQData(MType.SRM_COMPLETED_ACCOUNT.name());
            mqData.setData(producerMsg);
            mqProducer.send(SrmMqConstant.Topic.TOPIC_PMS_SRM, null, JSON.toJSONString(mqData));
        }
    }

    /**
     * 复核传消息给BMS
     *
     * @param id
     * @param key
     */
    private void messageTemplate(Integer id, String key) {

        logger.info("根据票夹查询发票信息..... id={}", id);
        MQData mqData = new MQData();
        mqData.setType(key);
        JSONObject msgJson = new JSONObject();
        msgJson.put("id", id);
        msgJson.put("reason", "财务复核发票驳回");
        String producerMsg = msgJson.toJSONString();
        mqData.setData(producerMsg);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST, null, JSON.toJSONString(mqData));
    }

    /**
     * 付款单下个审批人查询
     *
     * @param total
     * @param type
     * @param supplierName
     * @param processCode
     * @param deptId
     * @param userId
     * @return
     */
    private ProcessForecastRequest submitPayForm(BigDecimal total, String type, String supplierName, String processCode, Integer deptId, String userId) {
        List<ProcessForecastRequest.ProcessForecastRequestFormComponentValues> processForecastRequestFormComponentValues = new ArrayList<>(16);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesSeven = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("打款对象")
                .setValue(supplierName);
        processForecastRequestFormComponentValues.add(formComponentValuesSeven);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesOne = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("发起人")
                .setValue("发起人");
        processForecastRequestFormComponentValues.add(formComponentValuesOne);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesTwo = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("来源类型")
                .setValue(type);
        processForecastRequestFormComponentValues.add(formComponentValuesTwo);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesThree = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("结算方式")
                .setValue("结算方式");
        processForecastRequestFormComponentValues.add(formComponentValuesThree);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesFour = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("应付总额")
                .setValue(String.valueOf(total));
        processForecastRequestFormComponentValues.add(formComponentValuesFour);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesFive = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("来源单号")
                .setValue("查询");
        processForecastRequestFormComponentValues.add(formComponentValuesFive);
        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValuesSix = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                .setName("单据详情")
                .setValue("详情");
        processForecastRequestFormComponentValues.add(formComponentValuesSix);
        ProcessForecastRequest processForecastRequest = new ProcessForecastRequest()
                .setProcessCode(processCode)
                .setDeptId(deptId)
                .setUserId(userId)
                .setFormComponentValues(processForecastRequestFormComponentValues);
        return processForecastRequest;
    }

    /**
     * 修改采购发票票夹的状态
     *
     * @param financePurchaseInvoiceWalletsInput
     * @param status
     */
    private void walletsStatus(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput, Integer status) {
        //票夹待归档/归档
        FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets = new FinancePurchaseInvoiceWalletsInput();
        financePurchaseInvoiceWallets.setId(financePurchaseInvoiceWalletsInput.getId());
        financePurchaseInvoiceWallets.setFileTime(financePurchaseInvoiceWalletsInput.getFileTime());
        financePurchaseInvoiceWallets.setRemakes(financePurchaseInvoiceWalletsInput.getRemakes());
        financePurchaseInvoiceWallets.setUpdater(getAdminName());
        financePurchaseInvoiceWallets.setUpdateTime(LocalDateTime.now());
        financePurchaseInvoiceWallets.setStatus(status);
        financePurchaseInvoiceWallets.setInvoiceReview(getAdminName());
        financePurchaseInvoiceWallets.setInvoiceReviewTime(LocalDateTime.now());
        financePurchaseInvoiceWalletsMapper.updateByPrimaryKeySelective(financePurchaseInvoiceWallets);
    }


    /**
     * 修改采购对账单审核状态(复核人)
     *
     * @param id
     */
    private void accountPaymentStatus(Long id) {
        FinanceOperatorLog financeOperatorLog = new FinanceOperatorLog();
        financeOperatorLog.setAdditionalId(id);
        financeOperatorLog.setType(FinancePurchaseTypeEnum.STATEMENTS.ordinal());
        financeOperatorLog.setStatus(CommonNumbersEnum.ZERO.getNumber());
        financeOperatorLog.setPersonnelType(FinancePersonnelType.INVOICE_REVIEWER.ordinal());
        financeOperatorLog.setOperator(getAdminName());
        financeOperatorLog.setOperatorId(getAdminId());
        financeOperatorLog.setOperationResults(CommonNumbersEnum.ZERO.getNumber());
        financeOperatorLogMapper.insertSelective(financeOperatorLog);

    }

    /**
     * 全是核销票夹匹配通过，核销采购单
     *
     * @param additionalId
     */
    private void modifyStatement(Long additionalId) {

        FinanceAccountStatement select = financeAccountStatementMapper.select(additionalId);
        List<FinanceAccountStatementDetail> financeAccountStatementDetails = financeAccountStatementDetailMapper.selectByPrimaryKey(select.getId());
        //先根据采购单 分组 相同采购单一起处理
        Map<String, List<FinanceAccountStatementDetail>> collect = financeAccountStatementDetails.stream().collect(Collectors.groupingBy(FinanceAccountStatementDetail::getPurchaseNo));
        for (Map.Entry<String, List<FinanceAccountStatementDetail>> map : collect.entrySet()) {
            //已付款对账单，采购单供应商付款表数据修改
            PurchaseSupplierPayment purchaseSupplierPayment = new PurchaseSupplierPayment();
            purchaseSupplierPayment.setSupplierId(select.getSupplierId());
            purchaseSupplierPayment.setPurchaseNo(map.getKey());
            //调整后金额
            BigDecimal amount = BigDecimal.ZERO;
            //调整金额
            BigDecimal adjustAmount = BigDecimal.ZERO;
            for (FinanceAccountStatementDetail financeAccountStatementDetail : map.getValue()) {
                boolean purchaseOrSkipIn = (Objects.equals(financeAccountStatementDetail.getType(), StockTaskType.PURCHASE_IN.getId()) || Objects.equals(financeAccountStatementDetail.getType(), StockTaskType.SKIP_STORE_IN.getId()));
                //查询出供应商采购单下付款情况
                PurchaseSupplierPaymentVO paymentVO = purchaseSupplierPaymentMapper.selectOne(purchaseSupplierPayment);
                //如果付款情况为空，说明该采购单没有预付，需要生成，插入这次的付款情况
                if (ObjectUtils.isEmpty(paymentVO)) {
                    PurchaseSupplierPayment payment = new PurchaseSupplierPayment();
                    payment.setPurchaseNo(financeAccountStatementDetail.getPurchaseNo());
                    payment.setSupplierId(select.getSupplierId());
                    payment.setSupplierName(select.getSupplierName());
                    payment.setAdvanceAmount(BigDecimal.ZERO);
                    payment.setWriteOffAmount(BigDecimal.ZERO);
                    payment.setAdjustAmount(BigDecimal.ZERO);
                    //采购入库和越仓入库为正数，退货出库和越仓出库为负数
                    //负数时创建无付款金额 在付款信息中会扣减成本
                    payment.setPaymentAmount(BigDecimal.ZERO);
                    purchaseSupplierPaymentMapper.insert(payment);
                }
                //同一个采购单的调整金额和调整后总金额统计之后 计算相应要改变的数据
                adjustAmount = adjustAmount.add(financeAccountStatementDetail.getAdjustAmount());
                if (purchaseOrSkipIn) {
                    amount = amount.add(financeAccountStatementDetail.getAmount());
                } else if (Objects.equals(financeAccountStatementDetail.getType(), StockTaskType.PURCHASES_BACK.getId())) {
                    amount = amount.subtract(financeAccountStatementDetail.getAmount());
                }
            }
            PurchaseSupplierPaymentVO purchaseSupplierPaymentVO = purchaseSupplierPaymentMapper.selectOne(purchaseSupplierPayment);
            purchaseSupplierPayment.setId(purchaseSupplierPaymentVO.getId());
            purchaseSupplierPayment.setWriteOffAmount(amount.add(purchaseSupplierPaymentVO.getWriteOffAmount()));
            purchaseSupplierPayment.setAdjustAmount(adjustAmount.add(purchaseSupplierPaymentVO.getAdjustAmount()));
            purchaseSupplierPayment.setUpdateTime(LocalDateTime.now());
            purchaseSupplierPaymentMapper.update(purchaseSupplierPayment);
        }
    }

//        for (FinanceAccountStatementDetail financeAccountStatementDetail : financeAccountStatementDetails) {
//            //已付款对账单，采购单供应商付款表数据修改
//            PurchaseSupplierPayment purchaseSupplierPayment = new PurchaseSupplierPayment();
//            purchaseSupplierPayment.setSupplierId(select.getSupplierId());
//            purchaseSupplierPayment.setPurchaseNo(financeAccountStatementDetail.getPurchaseNo());
//            //查询出供应商采购单下付款情况
//            PurchaseSupplierPaymentVO purchaseSupplierPaymentVO = purchaseSupplierPaymentMapper.selectOne(purchaseSupplierPayment);
//            //如果付款情况为空，说明该采购单没有预付，需要生成，插入这次的付款情况
//            if (ObjectUtils.isEmpty(purchaseSupplierPaymentVO)) {
//                PurchaseSupplierPayment payment = new PurchaseSupplierPayment();
//                payment.setPurchaseNo(financeAccountStatementDetail.getPurchaseNo());
//                payment.setSupplierId(select.getSupplierId());
//                payment.setSupplierName(select.getSupplierName());
//                payment.setAdvanceAmount(BigDecimal.ZERO);
//                payment.setWriteOffAmount(BigDecimal.ZERO);
//                payment.setAdjustAmount(BigDecimal.ZERO);
//                payment.setPaymentAmount(financeAccountStatementDetail.getAmount());
//                purchaseSupplierPaymentMapper.insert(payment);
//                purchaseSupplierPaymentVO = purchaseSupplierPaymentMapper.selectOne(purchaseSupplierPayment);
//            }
//            //用流水表的预付总金额
//            BigDecimal advanceAmount = purchaseBindingPrepaymentService.queryAdvanceAmount(financeAccountStatementDetail.getPurchaseNo(), select.getSupplierId());
//            purchaseSupplierPaymentVO.setAdvanceAmount(advanceAmount);
//            purchaseSupplierPayment.setId(purchaseSupplierPaymentVO.getId());
//            //用预付金额与对账单调整后金额对比，判断核销金额
//            if (purchaseSupplierPaymentVO.getAdvanceAmount().subtract(purchaseSupplierPaymentVO.getWriteOffAmount()).subtract(financeAccountStatementDetail.getAmount()).compareTo(BigDecimal.ZERO) < CommonNumbersEnum.ZERO.getNumber()) {
//                //如果预付金额小于对账单调整后金额， 核销金额为预付金额，付款金额为账单调整后金额-预付金额
//                purchaseSupplierPayment.setWriteOffAmount(purchaseSupplierPaymentVO.getAdvanceAmount().subtract(purchaseSupplierPaymentVO.getWriteOffAmount()).add(purchaseSupplierPaymentVO.getWriteOffAmount()));
//                purchaseSupplierPayment.setPaymentAmount(financeAccountStatementDetail.getAmount().subtract(purchaseSupplierPaymentVO.getAdvanceAmount().subtract(purchaseSupplierPaymentVO.getWriteOffAmount())).add(purchaseSupplierPaymentVO.getPaymentAmount()));
//            } else if (Objects.equals(purchaseSupplierPaymentVO.getAdvanceAmount().subtract(purchaseSupplierPaymentVO.getWriteOffAmount()), CommonNumbersEnum.ZERO.getNumber())) {
//                //如果没有预付金额，就加入付款金额
//                purchaseSupplierPayment.setPaymentAmount(financeAccountStatementDetail.getAmount().add(purchaseSupplierPaymentVO.getPaymentAmount()));
//            } else {
//                //如果预付金额大于等于对账单调整后金额，不用付款，核销金额全部
//                purchaseSupplierPayment.setWriteOffAmount(financeAccountStatementDetail.getAmount().add(purchaseSupplierPaymentVO.getWriteOffAmount()));
//            }
//            purchaseSupplierPayment.setAdjustAmount(financeAccountStatementDetail.getAdjustAmount().add(purchaseSupplierPaymentVO.getAdjustAmount()));
//            purchaseSupplierPayment.setUpdateTime(LocalDateTime.now());
//            purchaseSupplierPaymentMapper.update(purchaseSupplierPayment);
//        }

//    }

    @Override
    public AjaxResult backTracking(PurchaseInvoiceQuery purchaseInvoiceQuery) {
        RLock lock = RedissonLockUtil.tryLock(String.format(INVOICE_LOCK_PREFIX, purchaseInvoiceQuery.getPurchaseInvoiceId()), 1);
        try {
            if(commonDynamicConfig.getInputInvoiceSwitch()){
                fmsFacade.updateMatching2Draft(purchaseInvoiceQuery.getPurchaseInvoiceId());
                return AjaxResult.getOK();
            }else{
                if (Objects.equals(purchaseInvoiceQuery.getStatus(), PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId())) {
                    PurchaseInvoiceVO purchaseInvoice = purchaseInvoiceMapper.selectDetailBySecond(purchaseInvoiceQuery.getPurchaseInvoiceId(), purchaseInvoiceQuery.getStatus());
                    if (!ObjectUtils.isEmpty(purchaseInvoice.getWalletsId())) {
                        return AjaxResult.getOK("occupy");
                    }
                    purchaseInvoiceLogMapper.toFlashBack(purchaseInvoiceQuery.getPurchaseInvoiceId(), getAdminName(), purchaseInvoiceQuery.getStatus(), PurchaseInvoiceLogEnum.TO_BE_SUBMITTED_STATE.getId());
                    addPurchaseInvoiceLog(purchaseInvoiceQuery.getPurchaseInvoiceId(), PurchaseInvoiceEnum.TO_BE_SUBMITTED_STATUS.getId(), PurchaseInvoiceLogEnum.IN_USE_STATE.getId());
                    return AjaxResult.getOK();
                }
            }
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return AjaxResult.getErrorWithMsg("回溯状态错错误");
    }

    @Override
    public AjaxResult selectBySupplier(Integer supplierId, String supplierName) {
        FinanceInvoicePartner financeInvoicePartnerList = financeInvoicePartnerMapper.selectSupplierId(supplierId);
        if (ObjectUtils.isEmpty(financeInvoicePartnerList)) {
            return AjaxResult.getErrorWithMsg("请选择供应商的发票一键匹配！");
        }
        //查询供应商信息
        Supplier supplier = supplierMapper.selectManager(supplierId);
        SupplierAccount supplierAccount = supplierMapper.selectByAccount(supplierId);
        if (ObjectUtils.isEmpty(supplier.getTaxNumber())) {
            return AjaxResult.getErrorWithMsg("请补充供应商税号等信息");
        }
        if (!Objects.equals(supplierName, supplier.getName())) {
            return AjaxResult.getErrorWithMsg("供应商名称不一致：页面：" + supplierName + "实际：" + supplier.getName());
        }
        PurchaseInvoiceQuery purchaseInvoiceQuery = new PurchaseInvoiceQuery();
        purchaseInvoiceQuery.setTaxNumber(supplier.getTaxNumber());
        //供应商待匹配发票信息（根据taxNumber字段）
        List<PurchaseInvoiceVO> purchaseInvoiceVOS = purchaseInvoiceMapper.selectByTaxNumber(purchaseInvoiceQuery);
        FinanceAccountStatementDBQuery financeAccountStatementQuery = new FinanceAccountStatementDBQuery();
        financeAccountStatementQuery.setTaxNumber(supplier.getTaxNumber());

        //供应上待匹配对账单信息（根据taxNumber字段)
        List<FinanceAccountStatementVO> financeAccountStatements = financeAccountStatementMapper.selectByTaxNumber(financeAccountStatementQuery);
        SupplierVO supplierVO = new SupplierVO();
        supplierVO.setManager(supplier.getManager());
        supplierVO.setSupplierType(supplier.getSupplierType());
        supplierVO.setTaxNumber(supplier.getTaxNumber().trim());
        if (!ObjectUtils.isEmpty(supplierAccount) && !ObjectUtils.isEmpty(supplierAccount.getAccountName())) {
            supplierVO.setSupplierName(supplierAccount.getAccountName());
        }
        supplierVO.setId(supplier.getId());
        supplierVO.setInvoiceNum(CollectionUtils.isEmpty(purchaseInvoiceVOS) ? CommonNumbersEnum.ZERO.getNumber() : purchaseInvoiceVOS.size());
        //待匹配的对账单数量
        supplierVO.setAccountStatementNum(CollectionUtils.isEmpty(financeAccountStatements) ? CommonNumbersEnum.ZERO.getNumber() : financeAccountStatements.size());
        return AjaxResult.getOK(supplierVO);
    }

    @Override
    public AjaxResult selectByInvoice(Integer pageIndex, Integer pageSize, PurchaseInvoiceQuery purchaseInvoiceQuery) {
        PageHelper.startPage(pageIndex, pageSize);
        List<PurchaseInvoiceVO> purchaseInvoiceVOS = purchaseInvoiceMapper.selectByTaxNumber(purchaseInvoiceQuery);
        for (PurchaseInvoiceVO purchaseInvoiceVO : purchaseInvoiceVOS) {
            List<String> fileAddress = purchaseInvoiceMapper.fileAddress(purchaseInvoiceVO.getId(), PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());
            if (ObjectUtils.isEmpty(fileAddress)) {
                purchaseInvoiceVO.setPhotoNum(CommonNumbersEnum.ZERO.getNumber());
                purchaseInvoiceVO.setFileNum(CommonNumbersEnum.ZERO.getNumber());
                continue;
            }
            Integer fileNum = 0;
            Integer photoNum = 0;
            List<String> photoList = new ArrayList<>();
            List<String> fileList = new ArrayList<>();
            for (String file : fileAddress) {
                String[] split = file.split("\\.");
                //如果后缀为pdf，则证明是pdf文件
                if (Objects.equals(split[CommonNumbersEnum.ONE.getNumber()], "pdf")) {
                    fileList.add(file);
                    fileNum++;
                } else {
                    photoList.add(file);
                    //否则为照片文件
                    photoNum++;
                }
            }
            purchaseInvoiceVO.setPhotoList(photoList);
            purchaseInvoiceVO.setFileList(fileList);
            purchaseInvoiceVO.setPhotoNum(photoNum);
            purchaseInvoiceVO.setFileNum(fileNum);
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(purchaseInvoiceVOS));
    }

    @Override
    public AjaxResult selectByBill(Integer pageIndex, Integer pageSize, FinanceAccountStatementQuery financeAccountStatementQuery) {
        PageHelper.startPage(pageIndex, pageSize);

        FinanceAccountStatementDBQuery dbQuery = new FinanceAccountStatementDBQuery();
        BeanUtils.copyProperties(financeAccountStatementQuery, dbQuery);
        dbQuery.setStartTime(DateUtil.beginOfDay(financeAccountStatementQuery.getStartTime()));
        dbQuery.setEndTime(DateUtil.endOfDay(financeAccountStatementQuery.getEndTime()));

        List<FinanceAccountStatementVO> financeAccountStatementVOS = financeAccountStatementMapper.selectByTaxNumber(dbQuery);
        for (FinanceAccountStatementVO financeAccountStatementVO : financeAccountStatementVOS) {
            int listSum = financeAccountStatementDetailMapper.select(financeAccountStatementVO.getId());
            financeAccountStatementVO.setWarehousingOrderNum(listSum);
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(financeAccountStatementVOS));
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public AjaxResult saveWalletsWithBill(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        // 对账单id
        List<Long> reconciliationIds = financePurchaseInvoiceWalletsInput.getFinanceAccountStatementIds();
        if (CollUtil.isNotEmpty(reconciliationIds)) {
            Long accountStatementId = reconciliationIds.get(0);
            InvoiceMatchCoordinationHandler.build().execute(accountStatementId);
        }
        if (ObjectUtils.isEmpty(financePurchaseInvoiceWalletsInput)) {
            return AjaxResult.getErrorWithMsg("没有匹配的对账单和采购发票");
        }
        if (CollectionUtils.isEmpty(financePurchaseInvoiceWalletsInput.getPurchaseInvoiceIds())) {
            return AjaxResult.getErrorWithMsg("没有匹配的采购发票");
        }
        if (CollectionUtils.isEmpty(financePurchaseInvoiceWalletsInput.getFinanceAccountStatementIds())) {
            return AjaxResult.getErrorWithMsg("没有匹配的对账单");
        }
        //避免页面长时间停留,校验发票是否被修改金额/发票代码/发票号码
        if (checkInvoiceChanged(financePurchaseInvoiceWalletsInput)) {
            return AjaxResult.getErrorWithMsg("发票已被修改，请重新选择");
        }
        Map<Long, FinanceAccountStatement> statementMap = Maps.newHashMap();
        for (Long id : financePurchaseInvoiceWalletsInput.getFinanceAccountStatementIds()) {
            FinanceAccountStatement financeAccountStatement = financeAccountStatementMapper.selectId(id);
            statementMap.put(id, financeAccountStatement);
        }
        StringBuilder stringBuilder = new StringBuilder();
        List<Long> list = new ArrayList<>();
        List<Integer> integerList = new ArrayList<>();
        //票夹编号：PJ20211206001,PJ是票夹的大写开头，********是年月日，最后3位001是当日编号。
        String day = BaseDateUtils.localDateTimeToStringSix(LocalDateTime.now());
        stringBuilder.append("PJ")
                .append(day);
        Integer num = financePurchaseInvoiceWalletsMapper.selectSum(stringBuilder.toString());
        String sequenceCode = StringUtils.strFormat(num.toString(), CommonNumbersEnum.THREE.getNumber());
        String walletsNo = stringBuilder.append(sequenceCode).toString();
        //新增票夹
        FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets = new FinancePurchaseInvoiceWallets();
        financePurchaseInvoiceWallets.setSupplierName(financePurchaseInvoiceWalletsInput.getSupplierName());
        financePurchaseInvoiceWallets.setSupplierId(financePurchaseInvoiceWalletsInput.getSupplierId());
        financePurchaseInvoiceWallets.setManager(financePurchaseInvoiceWalletsInput.getManager());
        financePurchaseInvoiceWallets.setTotalIncludedTax(financePurchaseInvoiceWalletsInput.getTotalIncludedTax());
        //票夹刚生成为待复核的状态，状态为有效
        financePurchaseInvoiceWallets.setStatus(CommonNumbersEnum.TWO.getNumber());
        financePurchaseInvoiceWallets.setDeleteStatus(CommonNumbersEnum.ZERO.getNumber());
        financePurchaseInvoiceWallets.setWalletsNo(walletsNo);
        financePurchaseInvoiceWallets.setInvoiceQuantity(financePurchaseInvoiceWalletsInput.getPurchaseInvoiceIds().size());
        financePurchaseInvoiceWallets.setCreator(getAdminName());
        financePurchaseInvoiceWallets.setCreateTime(LocalDateTime.now());

        financePurchaseInvoiceWallets.setCreatorRoleType(WalletEnum.CreatorRoleType.PURCHASE.name());

        BillFeature billFeature = new BillFeature();
        List<BillFeature.BillInfoFeature> infoFeatureList = Lists.newArrayList();
        billFeature.setWalletsEstimatePayInfoList(Lists.newArrayList());
        for (Long id : financePurchaseInvoiceWalletsInput.getFinanceAccountStatementIds()) {
            BillFeature.BillInfoFeature billInfoFeature = new BillFeature.BillInfoFeature();
            billInfoFeature.setId(id);
            FinanceAccountStatement financeAccountStatement = statementMap.get(id);
            billInfoFeature.setCreateUserName(financeAccountStatement == null ? null : financeAccountStatement.getCreator());

            pmsServiceClientFacade.queryStatementDetail(id).ifPresent(statementResp -> {
                if(CollUtil.isNotEmpty(statementResp.getEstimatePayRespList())){
                    statementResp.getEstimatePayRespList().forEach(estimatePayResp -> {
                        WalletsEstimatePayInfo walletsEstimatePayInfo = new WalletsEstimatePayInfo();
                        walletsEstimatePayInfo.setPayDate(estimatePayResp.getPayDate());
                        walletsEstimatePayInfo.setPayAmount(estimatePayResp.getPayAmount());
                        billFeature.getWalletsEstimatePayInfoList().add(walletsEstimatePayInfo);
                    });
                }
            });

            infoFeatureList.add(billInfoFeature);
        }
        billFeature.setBillInfoFeatureList(infoFeatureList);
        financePurchaseInvoiceWallets.setBillFeature(JSON.toJSONString(billFeature));

        financePurchaseInvoiceWalletsMapper.insertSelective(financePurchaseInvoiceWallets);
        StringBuilder message = new StringBuilder();
        //票夹账期账单金额
        BigDecimal accountingPeriodAmount = BigDecimal.ZERO;
        //新增票夹的id插入发票和对账单
        bindInvoiceToWallets(financePurchaseInvoiceWalletsInput.getPurchaseInvoiceIds(), financePurchaseInvoiceWallets.getId());

//        for (Integer id : financePurchaseInvoiceWalletsInput.getPurchaseInvoiceIds()) {
//            PurchaseInvoice purchaseInvoice = purchaseInvoiceMapper.selectWallets(id);
//            if (!ObjectUtils.isEmpty(purchaseInvoice) && ObjectUtils.isEmpty(purchaseInvoice.getWalletsId())) {
//                purchaseInvoice.setId(id);
//                purchaseInvoice.setWalletsId(financePurchaseInvoiceWallets.getId());
//                int update = purchaseInvoiceMapper.update(purchaseInvoice);
//                if (Objects.equals(update, CommonNumbersEnum.ZERO.getNumber())) {
//                    //该发票已经被票夹匹配
//                    message.append("发票" + purchaseInvoice.getInvoiceCode() + purchaseInvoice.getInvoiceNumber() + "已经被匹配。");
//                } else {
//                    //如果没被匹配，先存入数组，最后票夹生成不成功将数据的票夹id清空
//                    integerList.add(id);
//                }
//            } else {
//                message.append("发票" + purchaseInvoice.getInvoiceCode() + purchaseInvoice.getInvoiceNumber() + "已经被匹配。");
//            }
//        }
        for (Long id : financePurchaseInvoiceWalletsInput.getFinanceAccountStatementIds()) {
            FinanceAccountStatement financeAccountStatement = financeAccountStatementMapper.selectId(id);

            //如果调整后总额大于核销总额，说明有应付金额，是账期账单
            accountingPeriodAmount = accountingPeriodAmount.add(financeAccountStatement.getTotalBillAmount().subtract(financeAccountStatement.getWriteOffAmount()));
            if (!ObjectUtils.isEmpty(financeAccountStatement) && ObjectUtils.isEmpty(financeAccountStatement.getWalletsId())) {
                financeAccountStatement.setWalletsId(financePurchaseInvoiceWallets.getId());
                financeAccountStatement.setCurrentProcessor("财务");
                int update = financeAccountStatementMapper.update(financeAccountStatement);
                if (Objects.equals(update, CommonNumbersEnum.ZERO.getNumber())) {
                    //该对账单已经被票夹匹配
                    message.append("对账单" + financeAccountStatement.getId() + "已经被匹配。");
                } else {
                    //如果没被匹配，先存入数组，最后票夹生成不成功将数据的票夹id清空
                    list.add(id);
                }
            } else {
                message.append("对账单" + id + "已经被匹配。");
            }
        }
        if (!ObjectUtils.isEmpty(message)) {
            message.append("暂不可选择");
            throw new BizException(message.toString());
//            //将发票票夹数据清空
//            for (Integer integer : integerList) {
//                purchaseInvoiceMapper.updateById(integer);
//            }
//            //将对账单票夹数据清空
//            for (Long l : list) {
//                financeAccountStatementMapper.updateById(l);
//            }
//            //删除票夹
//            financePurchaseInvoiceWalletsMapper.update(financePurchaseInvoiceWallets.getId());
//            return AjaxResult.getErrorWithMsg(message.toString());
        }
        for (Long id : financePurchaseInvoiceWalletsInput.getFinanceAccountStatementIds()) {
            FinanceAccountStatement financeAccountStatement = financeAccountStatementMapper.selectId(id);

            //改变srm对账单状态至待审核
            JSONObject json = new JSONObject();
            json.put("id", id);
            String producerMsg = json.toJSONString();
            //将处理结果集合发送至MQ
            MQData mqData = new MQData(MType.SRM_UPDATE_WAIT_AUDIT_STATUS.name());
            mqData.setData(producerMsg);
            mqProducer.send(SrmMqConstant.Topic.TOPIC_PMS_SRM, null, JSON.toJSONString(mqData));

            FinanceOperatorLog financeOperatorLog = new FinanceOperatorLog();
            financeOperatorLog.setAdditionalId(financeAccountStatement.getId());
            financeOperatorLog.setType(FinancePurchaseTypeEnum.STATEMENTS.ordinal());
            financeOperatorLog.setStatus(CommonNumbersEnum.ZERO.getNumber());
            financeOperatorLog.setPersonnelType(FinancePersonnelType.INVOICE_TO_BE_MATCHED.ordinal());
            financeOperatorLog.setOperatorTime(LocalDateTime.now());
            financeOperatorLog.setOperationResults(CommonNumbersEnum.ZERO.getNumber());
            financeOperatorLog.setOperatorId(getAdminId());
            financeOperatorLog.setOperator(getAdminName());
            financeOperatorLogMapper.insertSelective(financeOperatorLog);

            if (Objects.equals(financeAccountStatement.getConfirmUser(), CommonNumbersEnum.ONE.getNumber())) {
                sendOfferMessage(financeAccountStatement, financeAccountStatement.getSupplierName() + "的账单已开票并提交审核", "待复核发票", null);
            }
        }
        //数据正常，且票夹账期账单金额大于0，给票夹赋值账期账单金额 和有无账期的状态
        financePurchaseInvoiceWallets.setAccountingPeriodAmount(accountingPeriodAmount);
        if (accountingPeriodAmount.compareTo(BigDecimal.ZERO) > CommonNumbersEnum.ZERO.getNumber()) {
            financePurchaseInvoiceWallets.setAccountingPeriodType(CommonNumbersEnum.ZERO.getNumber());
            financePurchaseInvoiceWalletsMapper.updateType(financePurchaseInvoiceWallets);
            return AjaxResult.getOK();
        }
        financePurchaseInvoiceWallets.setAccountingPeriodType(CommonNumbersEnum.ONE.getNumber());
        financePurchaseInvoiceWalletsMapper.updateType(financePurchaseInvoiceWallets);
        return AjaxResult.getOK();
    }

    private boolean checkInvoiceChanged(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        Map<Integer, PurchaseInvoiceVO> dbInvoiceMap = purchaseInvoiceMapper.selectByIdList(financePurchaseInvoiceWalletsInput.getPurchaseInvoiceIds()).stream().collect(Collectors.toMap(PurchaseInvoiceVO::getId, Function.identity(), (oldValue, newValue) -> oldValue));
        for (PurchaseInvoice purchaseInvoice : financePurchaseInvoiceWalletsInput.getPurchaseInvoice()) {
            //对比数据库中的发票和前端传参的发票，若有不同则报错提示发票已被修改
            PurchaseInvoiceVO dbInvoice = dbInvoiceMap.get(purchaseInvoice.getId());
            if (!Objects.equals(purchaseInvoice.getInvoiceCode(), dbInvoice.getInvoiceCode()) ||
                    !Objects.equals(purchaseInvoice.getInvoiceNumber(), dbInvoice.getInvoiceNumber()) ||
                    (purchaseInvoice.getIncludedTax().abs().compareTo(dbInvoice.getIncludedTax()) != 0) ||
                    (purchaseInvoice.getExcludingTax().abs().compareTo(dbInvoice.getExcludingTax()) != 0) ||
                    (purchaseInvoice.getTaxAmount().abs().compareTo(dbInvoice.getTaxAmount()) != 0)
            ) {
                log.warn("");
                return true;
            }
        }
        return false;
    }

    @Override
    public AjaxResult showWallets() {
        //票夹数量上限
        Config num = configMapper.selectOne(WALLETS_NUM);
        //票夹金额上限
        Config price = configMapper.selectOne(WALLETS_PRICE);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("num", Integer.valueOf(num.getValue()));
        jsonObject.put("price", Integer.valueOf(price.getValue()));
        return AjaxResult.getOK(jsonObject);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public AjaxResult updateWallets(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        configMapper.updateValue(WALLETS_NUM, financePurchaseInvoiceWalletsInput.getNum().toString());
        configMapper.updateValue(WALLETS_PRICE, financePurchaseInvoiceWalletsInput.getPrice().toString());
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult checkInvoiceWallets(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        StringBuilder message = new StringBuilder();
        //新增票夹的id插入发票和对账单
        for (Integer id : financePurchaseInvoiceWalletsInput.getPurchaseInvoiceIds()) {
            PurchaseInvoice purchaseInvoice = purchaseInvoiceMapper.selectWallets(id);
            if (Objects.nonNull(purchaseInvoice) && !ObjectUtils.isEmpty(purchaseInvoice.getWalletsId())) {
                //该发票已经被票夹匹配
                message.append("发票" + purchaseInvoice.getInvoiceCode() + purchaseInvoice.getInvoiceNumber() + "已经被匹配。");
            }
        }
        if (!ObjectUtils.isEmpty(message)) {
            message.append("暂不可选择");
            return AjaxResult.getError(message.toString());
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult checkWallets(Long id, Integer status, Integer expenseType) {

        //票夹信息
        FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets = financePurchaseInvoiceWalletsMapper.selectByPrimaryKey(id);
        if (Objects.isNull(financePurchaseInvoiceWallets)) {
            return AjaxResult.getErrorWithMsg("票夹不存在");
        }
        //发票信息
        List<PurchaseInvoiceVO> purchaseInvoiceVOS = purchaseInvoiceMapper.selectAll(financePurchaseInvoiceWallets.getId());
        for (PurchaseInvoiceVO purchaseInvoice : purchaseInvoiceVOS) {
            //文件存储地址
            List<String> fileAddress = purchaseInvoiceMapper.fileAddress(purchaseInvoice.getId(), CommonNumbersEnum.ONE.getNumber());
            if (!CollectionUtils.isEmpty(fileAddress)) {
                purchaseInvoice.setFileAdd(fileAddress);
            }
        }
        WallDetailOutput wallDetailOutput = new WallDetailOutput();
        wallDetailOutput.setId(financePurchaseInvoiceWallets.getId());
        wallDetailOutput.setWalletsNo(financePurchaseInvoiceWallets.getWalletsNo());
        wallDetailOutput.setInvoiceQuantity(financePurchaseInvoiceWallets.getInvoiceQuantity());
        wallDetailOutput.setCreatorRoleType(financePurchaseInvoiceWallets.getCreatorRoleType());
        if (Objects.nonNull(financePurchaseInvoiceWallets.getSupplierId())) {
            wallDetailOutput.setSupplierId(Long.valueOf(financePurchaseInvoiceWallets.getSupplierId()));
        }
        wallDetailOutput.setSupplierName(financePurchaseInvoiceWallets.getSupplierName());
        wallDetailOutput.setStatus(financePurchaseInvoiceWallets.getStatus());
        wallDetailOutput.setTotalIncludedTax(financePurchaseInvoiceWallets.getTotalIncludedTax());
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(financePurchaseInvoiceWallets.getBillFeature())) {
            BillFeature billFeature = JSON.parseObject(financePurchaseInvoiceWallets.getBillFeature(), BillFeature.class);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(billFeature.getBillInfoFeatureList())) {
                wallDetailOutput.setBillCreateUserName(billFeature.getBillInfoFeatureList().get(0).getCreateUserName());
            }
        }
        wallDetailOutput.setCreateTime(financePurchaseInvoiceWallets.getCreateTime());
        if(StringUtils.isNotBlank(financePurchaseInvoiceWallets.getBillFeature())){
            BillFeature billFeature = JSON.parseObject(financePurchaseInvoiceWallets.getBillFeature(), BillFeature.class);
            if(Objects.nonNull(billFeature)){
                if(CollUtil.isNotEmpty(billFeature.getWalletsEstimatePayInfoList())){
                    wallDetailOutput.setEstimatePayOutputList(
                            billFeature.getWalletsEstimatePayInfoList().stream().map(item -> {
                                WalletsEstimatePayOutput estimatePayOutput = new WalletsEstimatePayOutput();
                                estimatePayOutput.setPayDate(item.getPayDate());
                                estimatePayOutput.setAmount(item.getPayAmount());
                                return estimatePayOutput;
                            }).collect(Collectors.toList())
                    );
                }
            }
        }

        if (Objects.equals(expenseType, FinanceExpenseTypeEnum.URBAN_DISTRIBUTION.getId())
                || Objects.equals(expenseType, FinanceExpenseTypeEnum.TRUNK_BUSINESS.getId())
                || Objects.equals(expenseType, FinanceExpenseTypeEnum.STORAGE_BMS.getId())
                || Objects.equals(expenseType, FinanceExpenseTypeEnum.PROXY_WAREHOUSE.getId())) {
            //BMS打款单匹配票夹
            JSONObject jsonObject = new JSONObject();
            //找到最小的发票id
            Integer minInvoiceId = purchaseInvoiceVOS.stream().map(PurchaseInvoice::getId).min(Integer::compareTo).orElseThrow(() -> new DefaultServiceException("找不到最小的发票id"));
            //查询打款单信息 根据发票id
            BmsPaymentDocumentVO bmsPaymentDocumentVO = bmsPaymentDocumentService.queryBmsPaymentDocument(minInvoiceId,expenseType);
            String[] split = bmsPaymentDocumentVO.getSettleAccountUrl().split(",");
            List<String> list = new ArrayList<>(Arrays.asList(split));
            Admin admin = adminMapper.selectByPrimaryKey(bmsPaymentDocumentVO.getCreator());
            bmsPaymentDocumentVO.setCarrierCreator(admin.getRealname());
            bmsPaymentDocumentVO.setReconciliationProofUrlList(list);
            jsonObject.put("financePurchaseInvoiceWallets", financePurchaseInvoiceWallets);
            jsonObject.put("billList", bmsPaymentDocumentVO);
            jsonObject.put("purchaseInvoiceVOS", purchaseInvoiceVOS);
            jsonObject.put("wallDetailOutput", wallDetailOutput);
            return AjaxResult.getOK(jsonObject);
        }

        //流程进度
        FinancePurchaseInvoiceWalletsVO purchaseInvoiceWalletsVO = new FinancePurchaseInvoiceWalletsVO();
        PurchaseInvoiceVO purchaseInvoiceVO = purchaseInvoiceMapper.selectSubmissionTime(financePurchaseInvoiceWallets.getId());
        purchaseInvoiceWalletsVO.setSubmissionTime(purchaseInvoiceVO.getSubmissionTime());
        purchaseInvoiceWalletsVO.setSubmissionTimeMan(purchaseInvoiceVO.getSubmissionTimeMan());
        purchaseInvoiceWalletsVO.setMatchCompletionTime(financePurchaseInvoiceWallets.getCreateTime());
        purchaseInvoiceWalletsVO.setMatchCompletionTimeMan(financePurchaseInvoiceWallets.getCreator());
        if (Objects.equals(status, PurchaseInvoiceEnum.WAITING_FOR_ARCHIVING.getId())) {
            purchaseInvoiceWalletsVO.setReviewFilingTime(financePurchaseInvoiceWallets.getUpdateTime());
            purchaseInvoiceWalletsVO.setReviewFilingTimeMan(financePurchaseInvoiceWallets.getUpdater());
        }
        if (Objects.equals(status, PurchaseInvoiceEnum.ARCHIVED_STATUS.getId())) {
            purchaseInvoiceWalletsVO.setReviewFilingTime(financePurchaseInvoiceWallets.getInvoiceReviewTime());
            purchaseInvoiceWalletsVO.setReviewFilingTimeMan(financePurchaseInvoiceWallets.getInvoiceReview());
            purchaseInvoiceWalletsVO.setPaymentReview(financePurchaseInvoiceWallets.getUpdater());
            purchaseInvoiceWalletsVO.setPaymentReviewTime(financePurchaseInvoiceWallets.getUpdateTime());
        }


        JSONObject jsonObject = new JSONObject();
        jsonObject.put("financePurchaseInvoiceWallets", financePurchaseInvoiceWallets);
        jsonObject.put("purchaseInvoiceWalletsVO", purchaseInvoiceWalletsVO);
        jsonObject.put("purchaseInvoiceVOS", purchaseInvoiceVOS);
        jsonObject.put("wallDetailOutput", wallDetailOutput);
        return AjaxResult.getOK(jsonObject);
    }

    @Override
    public AjaxResult showWarehousingOrder(Integer pageIndex, Integer pageSize, StockTaskWalletsInput stockTaskWalletsInput) {
        PageHelper.startPage(pageIndex, pageSize);
        List<FinanceAccountStatementDetailVO> financeAccountStatementDetailVOS = financeAccountStatementDetailMapper.selectByWalletsId(stockTaskWalletsInput);
        for (FinanceAccountStatementDetailVO financeAccountStatementDetailVO : financeAccountStatementDetailVOS) {
            if (financeAccountStatementDetailVO.getTaxRateValue() == null) {
                throw new DefaultServiceException(financeAccountStatementDetailVO.getPdName() + "商品,没有维护税率");
            }
            //处理规格
            BigDecimal taxAmount = financeAccountStatementDetailVO.getPurchasePrice().divide((financeAccountStatementDetailVO.getTaxRateValue().add(BigDecimal.ONE)), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(financeAccountStatementDetailVO.getQuantity()));
            financeAccountStatementDetailVO.setTaxAmount(taxAmount);
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(financeAccountStatementDetailVOS));
    }

    @Override
    public AjaxResult show(StockTaskWalletsInput stockTaskWalletsInput) {
        List<FinanceAccountStatementDetailVO> financeAccountStatementDetailVOS = financeAccountStatementDetailMapper.selectMoney(stockTaskWalletsInput);
        BigDecimal num = BigDecimal.ZERO;
        for (FinanceAccountStatementDetailVO financeAccountStatementDetailVO : financeAccountStatementDetailVOS) {
            //入库增加，退货减少
            if (Objects.equals(financeAccountStatementDetailVO.getType(), StockTaskType.PURCHASE_IN.getId())
                    || Objects.equals(financeAccountStatementDetailVO.getType(), StockTaskType.SKIP_STORE_IN.getId())
                    || Objects.equals(financeAccountStatementDetailVO.getType(), StockTaskType.CROSS_WAREHOUSE_IN.getId())) {
                num = num.add(financeAccountStatementDetailVO.getExcludingTax());
            } else {
                num = num.subtract(financeAccountStatementDetailVO.getExcludingTax());
            }
        }
        JSONObject jsonObject = new JSONObject();
        //对账单数量
        jsonObject.put("total", financeAccountStatementDetailVOS.size());
        //入库总额
        jsonObject.put("num", num);
        return AjaxResult.getOK(jsonObject);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public AjaxResult backTrackingWallets(Long id, Integer expenseType, String remark) {
        return walletsCommonService.updateWallets(id,()->{
            FinancePurchaseInvoiceWallets db = financePurchaseInvoiceWalletsMapper.selectByPrimaryKey(id);
            if(db == null){
                throw new BizException("票夹不存在");
            }
            if(!Objects.equals(db.getStatus(), PurchaseInvoiceEnum.TO_BE_FILED_STATUS.getId())){
                log.warn("状态不是待复核状态");
                throw new BizException("状态不是待复核状态");
            }

            //票夹解散
            FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets = new FinancePurchaseInvoiceWalletsInput();
            financePurchaseInvoiceWallets.setUpdater(getAdminName());
            financePurchaseInvoiceWallets.setUpdateTime(LocalDateTime.now());
            financePurchaseInvoiceWallets.setId(id);
            financePurchaseInvoiceWalletsMapper.updateByPrimaryKey(financePurchaseInvoiceWallets);

            if (Objects.equals(expenseType, FinanceExpenseTypeEnum.URBAN_DISTRIBUTION.getId())
                    || Objects.equals(expenseType, FinanceExpenseTypeEnum.TRUNK_BUSINESS.getId())
                    || Objects.equals(expenseType, FinanceExpenseTypeEnum.STORAGE_BMS.getId())
                    || Objects.equals(expenseType, FinanceExpenseTypeEnum.PROXY_WAREHOUSE.getId())) {

                logger.info("根据票夹查询发票信息.....");
                //根据票夹查询发票信息
                List<PurchaseInvoiceVO> purchaseInvoiceVOSList = purchaseInvoiceMapper.selectAll(id);

                //BMS打款发消息 票夹被驳回
                //找到最小的发票id
                Integer minInvoiceId = purchaseInvoiceVOSList.stream().map(PurchaseInvoice::getId).min(Integer::compareTo).orElseThrow(() -> new DefaultServiceException("找不到最小的发票id"));

                messageTemplate(minInvoiceId, MType.BMS_PAYMENT_ORDER_FAIL.name());

            //发票返回可以匹配状态
            unbindWallets(id);

            return AjaxResult.getOK();
        }
        //发票返回可以匹配状态
        unbindWallets(id);

            //对账单返回可以匹配状态 当前状态处理人改变
            List<FinanceAccountStatementVO> financeAccountStatementVOSList = financeAccountStatementMapper.selectByWallets(id);
            for (FinanceAccountStatementVO financeAccountStatementVOList : financeAccountStatementVOSList) {
                financeAccountStatementMapper.updateByPrimaryKey(id, financeAccountStatementVOList.getSupplierName());
                if (Objects.equals(financeAccountStatementVOList.getConfirmUser(), CommonNumbersEnum.ONE.getNumber())) {
                    log.info("复核发票驳回，发送微信公众号信息到供应商，对账单id：" + financeAccountStatementVOList.getId());
                    messageWechatTemplate(MType.SRM_WECHAT_ADD_MESSAGE.name(), financeAccountStatementVOList);
                }
                FinanceAccountStatement financeAccountStatement = financeAccountStatementMapper.selectId(financeAccountStatementVOList.getId());
                //发送钉钉消息
                sendOfferMessage(financeAccountStatement, financeAccountStatement.getSupplierName() + "的账单发票复核被驳回，请及时联系供应商处理", "待开票", remark);
                log.info(financeAccountStatement.getSupplierName() + financeAccountStatementVOList.getId() + "的账单发票复核被驳回，请及时联系供应商处理");

                //改变srm对账单状态至待开票
                JSONObject json = new JSONObject();
                json.put("id", financeAccountStatementVOList.getId());
                String producerMsg = json.toJSONString();
                //将处理结果集合发送至MQ
                MQData mqData = new MQData(MType.SRM_UPDATE_INVOICE_TO_BE_MATCHED_STATUS.name());
                mqData.setData(producerMsg);
                mqProducer.send(SrmMqConstant.Topic.TOPIC_PMS_SRM, null, JSON.toJSONString(mqData));

                //对账单操作记录
                FinanceOperatorLog financeOperatorLog = new FinanceOperatorLog();
                financeOperatorLog.setAdditionalId(financeAccountStatement.getId());
                financeOperatorLog.setType(FinancePurchaseTypeEnum.STATEMENTS.ordinal());
                financeOperatorLog.setStatus(CommonNumbersEnum.ZERO.getNumber());
                financeOperatorLog.setPersonnelType(FinancePersonnelType.INVOICE_REVIEWER.ordinal());
                financeOperatorLog.setOperatorTime(LocalDateTime.now());
                financeOperatorLog.setOperationResults(CommonNumbersEnum.ONE.getNumber());
                financeOperatorLog.setOperatorId(getAdminId());
                financeOperatorLog.setOperator(getAdminName());
                financeOperatorLogMapper.insertSelective(financeOperatorLog);

            }

            return AjaxResult.getOK();
        });

    }

    private void unbindWallets(Long id) {
        if(commonDynamicConfig.getInputInvoiceSwitch()){
            fmsFacade.unbindInvoiceToWallets(id);
        }else{
            purchaseInvoiceMapper.updateByWalletsId(id);
        }
    }

    /**
     * 发送生成srm的对账单mq消息
     *
     * @param key
     * @param financeAccountStatement
     */
    private void messageWechatTemplate(String key, FinanceAccountStatementVO financeAccountStatement) {
        MQData mqData = new MQData();
        mqData.setType(key);
        JSONObject msgJson = new JSONObject();
        msgJson.put("financeAccountStatement", financeAccountStatement);
        String producerMsg = msgJson.toJSONString();
        mqData.setData(producerMsg);
        mqProducer.send(SrmMqConstant.Topic.TOPIC_PMS_SRM, null, JSON.toJSONString(mqData));
    }

    /**
     * 发送钉钉消息给采购
     *
     * @param financeAccountStatement
     * @param reason
     * @param state
     * @param remark
     */
    private void sendOfferMessage(FinanceAccountStatement financeAccountStatement, String reason, String state, String remark) {
        //查询所属销售的钉钉对应信息
        AdminAuthExtend creatorInfo = adminAuthExtendRepository.selectByAdminId(AdminAuthExtendEnum.Type.DING_TALK.ordinal(), financeAccountStatement.getCreatorAdminId());

        String title = "【采购对账通知】";
        StringBuilder text = new StringBuilder("##### " + title + "\n");
        text.append(reason).append("\n");
        text.append("> ###### 供应商：").append(financeAccountStatement.getSupplierName()).append("\n");
        text.append("> ###### 账单状态：").append(state).append("\n");
        text.append("> ###### 账单号：").append(financeAccountStatement.getId()).append("\n");
        text.append("> ###### 账单总额：").append(financeAccountStatement.getTotalBillAmount()).append("\n");
        text.append("> ###### 应付总额：").append(financeAccountStatement.getTotalBillAmount().subtract(financeAccountStatement.getWriteOffAmount())).append("\n");
        text.append("> ###### 核销金额：").append(financeAccountStatement.getWriteOffAmount()).append("\n");
        text.append("> ###### 创建时间：").append(financeAccountStatement.getCreateTime()).append("\n");
        if (StringUtils.isNotBlank(remark)) {
            text.append("> ###### 驳回原因：").append(remark).append("\n");
        }
//        if (Objects.nonNull(creatorInfo)) {
//            logger.info("【采购对账通知】钉钉Id{}" + creatorInfo.getUserId());
//            DingTalkMsgBO dingTalkMsgBO = new DingTalkMsgBO(DingTalkMsgTypeEnum.MARKDOWN.getType(), creatorInfo.getUserId(), title, text.toString());
//            dingTalkMsgSender.sendMessage(dingTalkMsgBO);
//        }


        DingTalkMsgReceiverIdBO dingTalkMsgReceiverIdBO = new DingTalkMsgReceiverIdBO();
        dingTalkMsgReceiverIdBO.setTitle(title);
        dingTalkMsgReceiverIdBO.setText(text.toString());
        dingTalkMsgReceiverIdBO.setMsgType(DingTalkMsgTypeEnum.MARKDOWN.getType());
        if (Objects.nonNull(creatorInfo)) {
            dingTalkMsgReceiverIdBO.setReceiverIdList(Lists.newArrayList(financeAccountStatement.getCreatorAdminId().longValue()));
            dingTalkMsgSender.sendMessageWithFeiShu(dingTalkMsgReceiverIdBO);
        }
    }

    /**
     * 微信公众号消息通知
     *
     * @param id
     * @param openId
     * @param pagePath
     * @param wechatDTO
     */
    private void sendTemplateMsg(Long id, String openId, String pagePath, WechatDTO wechatDTO) {
        log.info("对账单提醒，微信公众号通知供应商");
        log.info("对账单提醒内容" + wechatDTO.getFirst());
        //将审核结果，微信公众号通知供应商
        //构造通知json

        Map<String, Object> msg = new HashMap<>(4);
        msg.put("touser", openId);
        msg.put("template_id", TEMPLATE_ID);
        Config wechatTemplateJumpSwitch = configMapper.selectOne(WECHAT_TEMPLATE_JUMP_SWITCH);
        if (Objects.nonNull(wechatTemplateJumpSwitch) && StringUtils.isNotBlank(wechatTemplateJumpSwitch.getValue()) && wechatTemplateJumpSwitch.getValue().equals(WECHAT_TEMPLATE_JUMP_SWITCH_ON)) {
            //pagePath微信跳转链路 id为该链接id
            msg.put("miniprogram", WechatTemplateMiniProgramEntity.builder().appid(SystemLoginTypeEnum.SRM_WX.getAppId()).pagepath(pagePath + id).build());
        }
        Map<String, Object> data = new HashMap<>(16);
        data.put("first", WechatTemplateMsgDataEntity.builder().value(wechatDTO.getFirst()).build());
        data.put("keyword1", WechatTemplateMsgDataEntity.builder().value(wechatDTO.getKeywordOne()).build());
        data.put("keyword2", WechatTemplateMsgDataEntity.builder().value(wechatDTO.getKeywordTwo()).build());
        data.put("keyword3", WechatTemplateMsgDataEntity.builder().value(wechatDTO.getKeywordThree()).build());
        data.put("keyword4", WechatTemplateMsgDataEntity.builder().value(wechatDTO.getKeywordFour()).build());
        data.put("keyword5", WechatTemplateMsgDataEntity.builder().value(wechatDTO.getKeywordFive()).build());
        data.put("remark", WechatTemplateMsgDataEntity.builder().value(wechatDTO.getRemark()).build());

        msg.put("data", data);
        JSONObject json = new JSONObject(msg);
        //将处理结果集合发送至MQ
        MQData mqData = new MQData(MType.WECHAT_TEMPLATE.name());
        mqData.setData(json);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST, null, JSON.toJSONString(mqData));

    }

    @Override
    public AjaxResult showInvoice(Integer pageIndex, Integer pageSize, Long id) {
        PageHelper.startPage(pageIndex, pageSize);
        List<PurchaseInvoiceVO> purchaseInvoiceVOS = purchaseInvoiceMapper.selectByAdd(id);
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(purchaseInvoiceVOS));
    }

    @Override
    public AjaxResult statisticsWallets(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {
        List<FinancePurchaseInvoiceWalletsVO> financePurchaseInvoiceWalletsVOS = financePurchaseInvoiceWalletsMapper.totalAmountIncludingTax(financePurchaseInvoiceWalletsInput);
        BigDecimal totalAmountIncludingTax = BigDecimal.ZERO;
        for (FinancePurchaseInvoiceWalletsVO financePurchaseInvoiceWalletsVO : financePurchaseInvoiceWalletsVOS) {
            totalAmountIncludingTax = totalAmountIncludingTax.add(financePurchaseInvoiceWalletsVO.getTotalAmountIncludingTax());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("totalAmountIncludingTax", totalAmountIncludingTax);
        return AjaxResult.getOK(jsonObject);
    }

    @Override
    public AjaxResult downloadWallets(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {

        List<FinancePurchaseInvoiceWalletsVO> financePurchaseInvoiceWalletsVOS = financePurchaseInvoiceWalletsMapper.selectList(financePurchaseInvoiceWalletsInput);
        List<Long> ids = new ArrayList<>();
        for (FinancePurchaseInvoiceWalletsVO financePurchaseInvoiceWalletsVO : financePurchaseInvoiceWalletsVOS) {
            ids.add(financePurchaseInvoiceWalletsVO.getId());
        }
        //文件名
        String fileName = "已归档票夹" + DateUtils.localDateToString(financePurchaseInvoiceWalletsInput.getBillingDateStart(), DateUtils.SPECIFIC_DATE) +
                DateUtils.localDateToString(financePurchaseInvoiceWalletsInput.getBillingDateEnd(), DateUtils.SPECIFIC_DATE) + System.currentTimeMillis() + ".xls";

        insert(fileName);

        messageTemplate(ids, MType.ARCHIVED_DOWNLOADS.name(), fileName, getAdminId());

        return AjaxResult.getOK();
    }

    /**
     * 生产消息到mq
     *
     * @param ids
     * @param key
     * @param fileName
     * @param adminId
     */
    private void messageTemplate(List<Long> ids, String key, String fileName, Integer adminId) {
        MQData mqData = new MQData();
        mqData.setType(key);
        JSONObject msgJson = new JSONObject();
        msgJson.put("ids", ids);
        msgJson.put("fileName", fileName);
        msgJson.put("adminId", adminId);
        String producerMsg = msgJson.toJSONString();
        mqData.setData(producerMsg);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST, null, JSON.toJSONString(mqData));
    }

    /**
     * 下载中心插入正在下载
     *
     * @param fileName
     */
    private void insert(String fileName) {
        //下载中心留下下载中标记
        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
        fileDownloadRecord.setFileName(fileName);
        fileDownloadRecord.setStatus(CommonNumbersEnum.ZERO.getNumber());
        fileDownloadRecord.setAdminId(getAdminId());
        fileDownloadRecord.setType(CommonNumbersEnum.SIX.getNumber());
        fileDownloadRecord.setCreateTime(LocalDateTime.now());
        fileDownloadRecordMapper.insert(fileDownloadRecord);
    }

    @Override
    public void archivedDownloads(List<Long> ids, Integer adminId, String fileName) {

        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();

        //设置单元格宽度
        sheet.setAutobreaks(true);
        sheet.setColumnWidth(0, 5000);
        sheet.setColumnWidth(1, 5000);
        sheet.setColumnWidth(2, 5000);
        sheet.setColumnWidth(3, 5000);
        sheet.setColumnWidth(4, 6000);
//        sheet.setColumnWidth(5, 4000);
        sheet.setColumnWidth(5, 7000);
        sheet.setColumnWidth(6, 7000);


        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("票夹编号");
        title.createCell(1).setCellValue("发票张数");
        title.createCell(2).setCellValue("含税金额");
        title.createCell(3).setCellValue("可抵扣税额");
        title.createCell(4).setCellValue("发票销售方名称");
//        title.createCell(5).setCellValue("采购负责人");
        title.createCell(5).setCellValue("归档时间");
        title.createCell(6).setCellValue("归档备注");


        int index = 1;
        for (Long id : ids) {
            FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets = financePurchaseInvoiceWalletsMapper.selectByPrimaryKey(id);
            Row row = sheet.createRow(index);
            row.createCell(0).setCellValue(financePurchaseInvoiceWallets.getWalletsNo());
            row.createCell(1).setCellValue(financePurchaseInvoiceWallets.getInvoiceQuantity());
            row.createCell(2).setCellValue(financePurchaseInvoiceWallets.getTotalIncludedTax().toString());
            BigDecimal deductibleTax = calcInvoiceTax(id);
            row.createCell(3).setCellValue(ObjectUtils.isEmpty(deductibleTax) ? BigDecimal.ZERO.toString() : deductibleTax.toString());
            row.createCell(4).setCellValue(financePurchaseInvoiceWallets.getSupplierName());
//            row.createCell(5).setCellValue(financePurchaseInvoiceWallets.getManager());
            row.createCell(5).setCellValue(BaseDateUtils.localDateTimeToString(financePurchaseInvoiceWallets.getUpdateTime()));
            row.createCell(6).setCellValue(financePurchaseInvoiceWallets.getRemakes());
            index++;
        }

        sendFile(fileName, workbook);

    }

    /**
     * 上传文件到七牛云
     *
     * @param fileName
     * @param workbook
     */
    private void sendFile(String fileName, Workbook workbook) {

        //根据文件名获得token
        Map<String, String> data = UploadTokenFactory.createToken(fileName, QiNiuConstant.DEFAULT_EXPIRES);
        if (ObjectUtils.isEmpty(data)) {
            return;
        }

        //上传文件至七牛云
        AjaxResult result = qiNiuService.uploadFile(fileName, workbook);

        //上传成功或者失败修改状态
        update(result, fileName);

    }

    /**
     * 修改下载中心插入七牛云信息状态
     */
    private void update(AjaxResult result, String fileName) {

        //成功则修改状态为上传成功
        if (Objects.equals(result.getCode(), SUCCESS)) {
            FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
            fileDownloadRecord.setFileName(fileName);
            fileDownloadRecord.setStatus(CommonNumbersEnum.ONE.getNumber());
            fileDownloadRecordMapper.updateFileName(fileDownloadRecord);
            return;
        }
        //失败则修改状态为上传失败
        FileDownloadRecord fileDownloadRecord = new FileDownloadRecord();
        fileDownloadRecord.setFileName(fileName);
        fileDownloadRecord.setStatus(CommonNumbersEnum.TWO.getNumber());
        fileDownloadRecordMapper.updateFileName(fileDownloadRecord);

    }

    @Override
    public AjaxResult downloadShow(FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets) {

        //票夹信息
        FinancePurchaseInvoiceWallets wallets = financePurchaseInvoiceWalletsMapper.selectByPrimaryKey(financePurchaseInvoiceWallets.getId());
        Supplier supplier = supplierMapper.selectByPrimaryKey(wallets.getSupplierId());
        String status = Objects.equals(wallets.getStatus(), CommonNumbersEnum.TWO.getNumber()) ? "待归档" : "已归档";

        //文件名
        String fileName = supplier.getName() + wallets.getInvoiceQuantity() + "张采购发票匹配" + wallets.getTotalIncludedTax() + "元"
                + status + BaseDateUtils.localDateTimeToString(LocalDateTime.now()) + ".xls";

        insert(fileName);

        sendMessage(financePurchaseInvoiceWallets.getId(), MType.MATCHING_DETAILS_DOWNLOADS.name(), fileName, getAdminId());

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult allWalletsDetailedDownload(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput) {

        if (ObjectUtils.isEmpty(financePurchaseInvoiceWalletsInput.getExpenseType())) {
            return AjaxResult.getErrorWithMsg("请选择费用类型后再次下载！");
        }

        if (ObjectUtils.isEmpty(financePurchaseInvoiceWalletsInput.getUpdateTimeEnd()) || ObjectUtils.isEmpty(financePurchaseInvoiceWalletsInput.getUpdateTimeStart())) {
            return AjaxResult.getErrorWithMsg("下载文件匹配日期间隔必须在31天之内");
        }
        //校验，不能超过31天
        long differ = financePurchaseInvoiceWalletsInput.getUpdateTimeEnd().toEpochDay() - financePurchaseInvoiceWalletsInput.getUpdateTimeStart().toEpochDay();
        if (differ > 31) {
            return AjaxResult.getErrorWithMsg("下载文件匹配日期间隔必须在31天之内");
        }

        //文件名
        String fileName = "批量已归档发票明细" + BaseDateUtils.localDateTimeToString(LocalDateTime.now()) + System.currentTimeMillis() + ".zip";

        insert(fileName);

        sendMessageByAll(financePurchaseInvoiceWalletsInput, MType.MATCHING_DETAILS_ALL_DOWNLOADS.name(), fileName, getAdminId());

        return AjaxResult.getOK();
    }

    /**
     * 生产消息到mq
     *
     * @param financePurchaseInvoiceWalletsInput
     * @param key
     * @param fileName
     * @param adminId
     */
    private void sendMessageByAll(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput, String key, String fileName, Integer adminId) {
        MQData mqData = new MQData();
        mqData.setType(key);
        JSONObject msgJson = new JSONObject();
        msgJson.put("financePurchaseInvoiceWalletsInput", financePurchaseInvoiceWalletsInput);
        msgJson.put("fileName", fileName);
        msgJson.put("adminId", adminId);
        String producerMsg = msgJson.toJSONString();
        mqData.setData(producerMsg);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST, null, JSON.toJSONString(mqData));
    }

    /**
     * 生产消息到mq
     *
     * @param id
     * @param key
     * @param fileName
     * @param adminId
     */
    private void sendMessage(Long id, String key, String fileName, Integer adminId) {
        MQData mqData = new MQData();
        mqData.setType(key);
        JSONObject msgJson = new JSONObject();
        msgJson.put("id", id);
        msgJson.put("fileName", fileName);
        msgJson.put("adminId", adminId);
        String producerMsg = msgJson.toJSONString();
        mqData.setData(producerMsg);
        mqProducer.send(RocketMqMessageConstant.MALL_LIST, null, JSON.toJSONString(mqData));
    }

    @Override
    public void matchingDetailsDownloads(Long id, Integer adminId, String fileName) {

        //构建一个excel对象,这里注意type要是xls不能是xlsx,否则下面的写入后流会关闭,导致报错
        ExcelWriter excelWriter = EasyExcel.write().excelType(ExcelTypeEnum.XLS).build();

        archiveSummary(excelWriter, id);

        archiveSummaryDetail(excelWriter, id);

        archiveSummaryTask(excelWriter, id);

        Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();


//        //票夹信息
//        FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets = financePurchaseInvoiceWalletsMapper.selectByPrimaryKey(id);
//
//        Workbook workbook = new HSSFWorkbook();
//
//        //概要
//        Sheet sheet = workbook.createSheet("概要");
//        abstractDown(sheet, financePurchaseInvoiceWallets);
//
//        //发票信息
//        Sheet invoiceSheet = workbook.createSheet("发票信息");
//        invoiceInformation(invoiceSheet, financePurchaseInvoiceWallets.getId());
//
//        //入库单信息
//        Sheet warehousingOrderSheet = workbook.createSheet("入库单信息");
//        warehousingOrder(warehousingOrderSheet, financePurchaseInvoiceWallets.getId());

        sendFile(fileName, workbook);

    }

    @Override
    public void matchingDetailsAllDownloads(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput, Integer adminId, String fileName) {
        logger.info("批量下载票夹明细开始。。。。");
        //根据文件名获得token
        Map<String, String> data = UploadTokenFactory.createToken(fileName, QiNiuConstant.DEFAULT_EXPIRES);

        if (ObjectUtils.isEmpty(data)) {
            return;
        }

        try {
            logger.info("批量下载票夹明细开始。。。。");
            detailsAllDownloads(financePurchaseInvoiceWalletsInput, fileName);
        } catch (Exception e) {
            e.printStackTrace();
        }


        logger.info("批量下载票夹明细结束。。。。");
    }

    /**
     * 组装上传数据
     *
     * @param financePurchaseInvoiceWalletsInput
     * @param fileName
     * @throws IOException
     */
    private void detailsAllDownloads(FinancePurchaseInvoiceWalletsInput financePurchaseInvoiceWalletsInput, String fileName) throws IOException {

        //构造一个带指定 Region 对象的配置类
        Configuration configuration = new Configuration(Region.region0());
        //...其他参数参考类注释
        UploadManager uploadManager = new UploadManager(configuration);
        //...生成上传凭证，然后准备上传
        String accessKey = QiNiuConstant.ACCESS_KEY;
        String secretKey = QiNiuConstant.SECRET_KEY;
        String bucket = QiNiuConstant.DEFAULT_BUCKET;

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] bytes = null;
        ZipOutputStream zos = new ZipOutputStream(byteArrayOutputStream);

        //上传文件至七牛云
        AjaxResult result = null;

        //已归档发票数据
        List<FinancePurchaseInvoiceWalletsVO> financePurchaseInvoiceWalletsVOS = financePurchaseInvoiceWalletsMapper.selectList(financePurchaseInvoiceWalletsInput);

        //压缩文件
        try {

            for (FinancePurchaseInvoiceWalletsVO financePurchaseInvoiceWalletsVO : financePurchaseInvoiceWalletsVOS) {

                //文件名
                String name = financePurchaseInvoiceWalletsVO.getSupplierName() + financePurchaseInvoiceWalletsVO.getInvoiceQuantity() + "张采购发票匹配" +
                        financePurchaseInvoiceWalletsVO.getTotalIncludedTax() + "元已归档" + BaseDateUtils.localDateTimeToString(LocalDateTime.now()) + Global.WHITE_SPACE + StringUtils.getRandomNumber(5) + ".xls";

                //构建一个excel对象,这里注意type要是xls不能是xlsx,否则下面的写入后流会关闭,导致报错
                ExcelWriter excelWriter = EasyExcel.write().excelType(ExcelTypeEnum.XLS).build();
                FinanceExpenseTypeEnum financeExpenseTypeEnum = FinanceExpenseTypeEnum.getFinanceExpenseTypeEnum(financePurchaseInvoiceWalletsVO.getExpenseType());

                if (Objects.equals(financeExpenseTypeEnum, FinanceExpenseTypeEnum.PURCHASE)) {

                    archiveSummary(excelWriter, financePurchaseInvoiceWalletsVO.getId());

                    archiveSummaryDetail(excelWriter, financePurchaseInvoiceWalletsVO.getId());

                    archiveSummaryTask(excelWriter, financePurchaseInvoiceWalletsVO.getId());
                } else if (Objects.equals(financeExpenseTypeEnum, FinanceExpenseTypeEnum.URBAN_DISTRIBUTION)) {
                    //构建一个sheet页
                    WriteSheet bmsSheet = EasyExcel.writerSheet("城配信息").build();
                    WriteTable writeTableBms = EasyExcel.writerTable(0).head(ArchiveSummaryBmsDetailExcel.class).needHead(Boolean.TRUE).build();
                    //组装excel数据
                    archiveSummaryBms(excelWriter, bmsSheet, writeTableBms, financePurchaseInvoiceWalletsVO,financeExpenseTypeEnum);
                } else if (Objects.equals(financeExpenseTypeEnum, FinanceExpenseTypeEnum.TRUNK_BUSINESS)) {
                    //构建一个sheet页
                    WriteSheet bmsSheet = EasyExcel.writerSheet("干线信息").build();
                    WriteTable writeTableBms = EasyExcel.writerTable(0).head(ArchiveSummaryBmsDetailExcel.class).needHead(Boolean.TRUE).build();
                    //组装excel数据
                    archiveSummaryBms(excelWriter, bmsSheet, writeTableBms, financePurchaseInvoiceWalletsVO,financeExpenseTypeEnum);
                }else if (Objects.equals(financeExpenseTypeEnum, FinanceExpenseTypeEnum.STORAGE_BMS)) {
                    //构建一个sheet页
                    WriteSheet bmsSheet = EasyExcel.writerSheet("仓储信息").build();
                    WriteTable writeTableBms = EasyExcel.writerTable(0).head(ArchiveSummaryBmsDetailExcel.class).needHead(Boolean.TRUE).build();
                    //组装excel数据
                    archiveSummaryBms(excelWriter, bmsSheet, writeTableBms, financePurchaseInvoiceWalletsVO,financeExpenseTypeEnum);
                }
                else if (Objects.equals(financeExpenseTypeEnum, FinanceExpenseTypeEnum.PROXY_WAREHOUSE)) {
                    //构建一个sheet页
                    WriteSheet bmsSheet = EasyExcel.writerSheet("代销入仓信息").build();
                    WriteTable writeTableBms = EasyExcel.writerTable(0).head(ArchiveSummaryBmsDetailExcel.class).needHead(Boolean.TRUE).build();
                    //组装excel数据
                    archiveSummaryBms(excelWriter, bmsSheet, writeTableBms, financePurchaseInvoiceWalletsVO,financeExpenseTypeEnum);
                }

                Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();

                ZipEntry zipEntry = new ZipEntry(name);
                zos.putNextEntry(zipEntry);
                workbook.write(zos);
                zos.closeEntry();
            }

        } catch (IOException e) {
            logger.info("七牛云上传失败:{}", e.getMessage());
        } finally {
            zos.close();
            byteArrayOutputStream.close();
        }
        //上传文件信息
        bytes = byteArrayOutputStream.toByteArray();
        if (bytes.length < 1) {
            return;
        }
        Auth auth = Auth.create(accessKey, secretKey);
        String upToken = auth.uploadToken(bucket);
        Response response = null;
        try {
            response = uploadManager.put(bytes, fileName, upToken);
            DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
        } catch (QiniuException e) {
            logger.info("七牛云上传失败QiNiuException:{}", e.getMessage());
            result = AjaxResult.getError();
        }
        if (ObjectUtils.isEmpty(result)) {
            result = AjaxResult.getOK();
        }

        //上传成功或者失败修改状态
        update(result, fileName);

    }

    /**
     * 概要sheet
     *
     * @param excelWriter
     * @param walletsId
     */
    private void archiveSummary(ExcelWriter excelWriter, Long walletsId) {
        //构建一个sheet页
        WriteSheet firstSheet = EasyExcel.writerSheet("概要").build();
        WriteTable writeTableOne = EasyExcel.writerTable(0).head(ArchiveSummaryExcel.class).needHead(Boolean.FALSE).build();

        FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets = financePurchaseInvoiceWalletsMapper.selectByPrimaryKey(walletsId);

        List<ArchiveSummaryExcel> archiveSummaryExcels = new ArrayList<>(16);

        ArchiveSummaryExcel first = new ArchiveSummaryExcel();
        //供应商信息
        SupplierReq supplierReq = supplierMapper.selectDetail(financePurchaseInvoiceWallets.getSupplierId());
        String type = SupplierTypeEnum.getNameById(supplierReq.getSupplierType());
        first.setFirstColumn("供应商性质");
        first.setTwoColumn(type);
        archiveSummaryExcels.add(first);

        ArchiveSummaryExcel two = new ArchiveSummaryExcel();
        two.setFirstColumn("发票销售方");
        two.setTwoColumn(ObjectUtils.isEmpty(financePurchaseInvoiceWallets.getSupplierName()) ? "" : financePurchaseInvoiceWallets.getSupplierName());
        archiveSummaryExcels.add(two);

        ArchiveSummaryExcel three = new ArchiveSummaryExcel();
        three.setFirstColumn("税号");
        three.setTwoColumn(ObjectUtils.isEmpty(supplierReq.getTaxNumber()) ? "" : supplierReq.getTaxNumber());
        archiveSummaryExcels.add(three);

        ArchiveSummaryExcel four = new ArchiveSummaryExcel();
        four.setFirstColumn("");
        four.setTwoColumn("");
        archiveSummaryExcels.add(four);

        ArchiveSummaryExcel five = new ArchiveSummaryExcel();
        five.setFirstColumn("发票张数");
        five.setTwoColumn(ObjectUtils.isEmpty(financePurchaseInvoiceWallets.getInvoiceQuantity()) ? "" : String.valueOf(financePurchaseInvoiceWallets.getInvoiceQuantity()));
        archiveSummaryExcels.add(five);

        PurchaseInvoiceVO purchaseInvoiceVO = purchaseInvoiceMapper.selectData(financePurchaseInvoiceWallets.getId());
        PurchaseInvoiceVO redInvoiceVO = purchaseInvoiceMapper.selectRedInvoiceData(financePurchaseInvoiceWallets.getId());

        BigDecimal blueTotalTaxInclude = BigDecimal.ZERO;
        BigDecimal blueTotalActualDeductibleTax = BigDecimal.ZERO;
        BigDecimal blueTotalAmountExcludingTax = BigDecimal.ZERO;
        if (Objects.nonNull(purchaseInvoiceVO)) {
            blueTotalTaxInclude = purchaseInvoiceVO.getTotalTaxIncluded();
            blueTotalActualDeductibleTax = purchaseInvoiceVO.getTotalActualDeductibleTax();
            blueTotalAmountExcludingTax = purchaseInvoiceVO.getTotalAmountExcludingTax();
        }

        BigDecimal redTotalTaxInclude = BigDecimal.ZERO;
        BigDecimal redTotalActualDeductibleTax = BigDecimal.ZERO;
        BigDecimal redTotalAmountExcludingTax = BigDecimal.ZERO;
        if (Objects.nonNull(redInvoiceVO)) {
            redTotalTaxInclude = redInvoiceVO.getTotalTaxIncluded();
            redTotalActualDeductibleTax = redInvoiceVO.getTotalActualDeductibleTax();
            redTotalAmountExcludingTax = redInvoiceVO.getTotalAmountExcludingTax();
        }

        if (!ObjectUtils.isEmpty(purchaseInvoiceVO)) {
            ArchiveSummaryExcel six = new ArchiveSummaryExcel();
            six.setFirstColumn("含税总额");
            six.setTwoColumn(blueTotalTaxInclude.subtract(redTotalTaxInclude).toString());
            archiveSummaryExcels.add(six);

            ArchiveSummaryExcel seven = new ArchiveSummaryExcel();
            seven.setFirstColumn("实际可抵扣税额总额");
            seven.setTwoColumn(blueTotalActualDeductibleTax.subtract(redTotalActualDeductibleTax).toString());
            archiveSummaryExcels.add(seven);

            ArchiveSummaryExcel eight = new ArchiveSummaryExcel();
            eight.setFirstColumn("不含税总额");
            eight.setTwoColumn(blueTotalAmountExcludingTax.subtract(redTotalAmountExcludingTax).toString());
            archiveSummaryExcels.add(eight);
            archiveSummaryExcels.add(four);
        }

        List<FinanceAccountStatementVO> financeAccountStatementVOS = financeAccountStatementMapper.selectByWalletsId(financePurchaseInvoiceWallets.getId());
        BigDecimal total = financeAccountStatementVOS.stream().map(FinanceAccountStatementVO::getTotalBillAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        ArchiveSummaryExcel nine = new ArchiveSummaryExcel();
        nine.setFirstColumn("对账单数量");
        nine.setTwoColumn(String.valueOf(financeAccountStatementVOS.size()));
        archiveSummaryExcels.add(nine);

        ArchiveSummaryExcel ten = new ArchiveSummaryExcel();
        ten.setFirstColumn("对账单总金额");
        ten.setTwoColumn(total.toString());
        archiveSummaryExcels.add(ten);

        excelWriter.write(archiveSummaryExcels, firstSheet, writeTableOne);
    }

    /**
     * 发票信息sheet
     *
     * @param excelWriter
     * @param walletsId
     */
    private void archiveSummaryDetail(ExcelWriter excelWriter, Long walletsId) {
        //构建一个sheet页
        WriteSheet twoSheet = EasyExcel.writerSheet("发票信息").build();
        WriteTable writeTableTwo = EasyExcel.writerTable(1).head(ArchiveSummaryInvoiceExcel.class).needHead(Boolean.TRUE).build();

        List<ArchiveSummaryInvoiceExcel> archiveSummaryInvoiceExcels = new ArrayList<>(16);

        List<PurchaseInvoiceVO> purchaseInvoiceVOS = purchaseInvoiceMapper.selectAll(walletsId);

        for (PurchaseInvoiceVO purchaseInvoiceVO : purchaseInvoiceVOS) {
            Integer invoiceTypeFace = purchaseInvoiceVO.getInvoiceTypeFace();

            ArchiveSummaryInvoiceExcel archiveSummaryInvoiceExcel = new ArchiveSummaryInvoiceExcel();
            archiveSummaryInvoiceExcel.setBillingDate(DateUtils.localDateToString(purchaseInvoiceVO.getBillingDate(), DateUtils.SPECIFIC_DATE));
            archiveSummaryInvoiceExcel.setSupplierType(SupplierTypeEnum.getNameById(purchaseInvoiceVO.getSupplierType()));
            archiveSummaryInvoiceExcel.setSupplierName(ObjectUtils.isEmpty(purchaseInvoiceVO.getSupplierName()) ? "" : purchaseInvoiceVO.getSupplierName());
            archiveSummaryInvoiceExcel.setTaxNumber(ObjectUtils.isEmpty(purchaseInvoiceVO.getTaxNumber()) ? "" : purchaseInvoiceVO.getTaxNumber());
            archiveSummaryInvoiceExcel.setInvoiceCode(ObjectUtils.isEmpty(purchaseInvoiceVO.getInvoiceCode()) ? "" : purchaseInvoiceVO.getInvoiceCode());
            archiveSummaryInvoiceExcel.setInvoiceNumber(ObjectUtils.isEmpty(purchaseInvoiceVO.getInvoiceNumber()) ? "" : purchaseInvoiceVO.getInvoiceNumber());
            archiveSummaryInvoiceExcel.setExcludingTax(negative(purchaseInvoiceVO.getInvoiceTypeFace(), purchaseInvoiceVO.getExcludingTax()));
            archiveSummaryInvoiceExcel.setTaxAmount(negative(purchaseInvoiceVO.getInvoiceTypeFace(), purchaseInvoiceVO.getTaxAmount()));
            archiveSummaryInvoiceExcel.setIncludedTax(negative(purchaseInvoiceVO.getInvoiceTypeFace(), purchaseInvoiceVO.getIncludedTax()));
            archiveSummaryInvoiceExcel.setTaxRate(ObjectUtils.isEmpty(purchaseInvoiceVO.getTaxRate()) ? "" : purchaseInvoiceVO.getTaxRate().toString() + "%");
            archiveSummaryInvoiceExcel.setActualTaxRate(!ObjectUtils.isEmpty(purchaseInvoiceVO.getActualTaxRate()) ? purchaseInvoiceVO.getActualTaxRate().toString() + "%" : "");
            archiveSummaryInvoiceExcel.setActualTaxAmount(negative(purchaseInvoiceVO.getInvoiceTypeFace(), purchaseInvoiceVO.getActualTaxAmount()));

            archiveSummaryInvoiceExcels.add(archiveSummaryInvoiceExcel);
        }

        excelWriter.write(archiveSummaryInvoiceExcels, twoSheet, writeTableTwo);

    }

    private static String negative(Integer invoiceTypeFace, BigDecimal number) {
        if (Objects.isNull(number)) {
            return "";
        }
        //红票展示负数
        if (Objects.equals(InvoiceEnum.InvoiceTypeFaceEnum.RED.getCode(), invoiceTypeFace)) {
            return "-" + number.toString();
        } else {
            return number.toString();
        }
    }

    /**
     * 入库单信息sheet
     *
     * @param excelWriter
     * @param walletsId
     */
    private void archiveSummaryTask(ExcelWriter excelWriter, Long walletsId) {
        //构建一个sheet页
        WriteSheet threeSheet = EasyExcel.writerSheet("入库单信息").build();
        WriteTable writeTableThree = EasyExcel.writerTable(2).head(FinanceAccountDetailExcelVO.class).needHead(Boolean.TRUE).build();
//        List<ArchiveSummaryTaskExcel> archiveSummaryTaskExcels = new ArrayList<>(16);
//
//        StockTaskWalletsInput stockTaskWalletsInput = new StockTaskWalletsInput();
//        stockTaskWalletsInput.setWalletsId(walletsId);
//        List<FinanceAccountStatementDetailVO> financeAccountStatementDetailVOS = financeAccountStatementDetailMapper.selectByWalletsId(stockTaskWalletsInput);
//
//        for (FinanceAccountStatementDetailVO financeAccountStatementDetailVO : financeAccountStatementDetailVOS) {
//            boolean inStore = Objects.equals(financeAccountStatementDetailVO.getType(), CommonNumbersEnum.ELEVEN.getNumber()) || Objects.equals(financeAccountStatementDetailVO.getType(), StockTaskType.SKIP_STORE_IN.getId());
//            ArchiveSummaryTaskExcel archiveSummaryTaskExcel = new ArchiveSummaryTaskExcel();
//            archiveSummaryTaskExcel.setOutTime(BaseDateUtils.localDateTimeToString(financeAccountStatementDetailVO.getAddTime()));
//            archiveSummaryTaskExcel.setType(inStore ? "入库" : "出库");
//            archiveSummaryTaskExcel.setTaskNo(ObjectUtils.isEmpty(financeAccountStatementDetailVO.getStockTaskProcessId()) ? "" : String.valueOf(financeAccountStatementDetailVO.getStockTaskProcessId()));
//            archiveSummaryTaskExcel.setPurchaseNo(ObjectUtils.isEmpty(financeAccountStatementDetailVO.getPurchaseNo()) ? "" : financeAccountStatementDetailVO.getPurchaseNo());
//            archiveSummaryTaskExcel.setBill(ObjectUtils.isEmpty(financeAccountStatementDetailVO.getFinanceAccountStatementId()) ? "" : String.valueOf(financeAccountStatementDetailVO.getFinanceAccountStatementId()));
//            archiveSummaryTaskExcel.setSku(ObjectUtils.isEmpty(financeAccountStatementDetailVO.getSku()) ? "" : financeAccountStatementDetailVO.getSku());
//            archiveSummaryTaskExcel.setPdName(ObjectUtils.isEmpty(financeAccountStatementDetailVO.getPdName()) ? "" : financeAccountStatementDetailVO.getPdName());
//            archiveSummaryTaskExcel.setTaxRateCode(ObjectUtils.isEmpty(financeAccountStatementDetailVO.getTaxRateCode()) ? "" : financeAccountStatementDetailVO.getTaxRateCode());
//            archiveSummaryTaskExcel.setTaxRate(ObjectUtils.isEmpty(financeAccountStatementDetailVO.getTaxRateValue()) ? "" : financeAccountStatementDetailVO.getTaxRateValue().toString());
//            archiveSummaryTaskExcel.setUnit(ObjectUtils.isEmpty(financeAccountStatementDetailVO.getWeight()) ? "" : financeAccountStatementDetailVO.getWeight());
//            archiveSummaryTaskExcel.setQuantity(inStore ? financeAccountStatementDetailVO.getQuantity().toString() : "-" + financeAccountStatementDetailVO.getQuantity());
//            BigDecimal price = financeAccountStatementDetailVO.getAmount().divide(BigDecimal.valueOf(financeAccountStatementDetailVO.getQuantity()), 2, RoundingMode.HALF_UP);
//            archiveSummaryTaskExcel.setPrice(price.toString());
//            archiveSummaryTaskExcel.setPurchasePrice(ObjectUtils.isEmpty(financeAccountStatementDetailVO.getPurchasePrice()) ? "" : financeAccountStatementDetailVO.getPurchasePrice().toString());
//            archiveSummaryTaskExcel.setAmount((inStore ? "" : "-") + financeAccountStatementDetailVO.getExcludingTax().setScale(2, RoundingMode.HALF_UP).toString());
//            //不含税金额 = (单价/(1+税率))*数量
//            if (!ObjectUtils.isEmpty(financeAccountStatementDetailVO.getTaxRateValue())) {
//                BigDecimal amount = price.divide((financeAccountStatementDetailVO.getTaxRateValue().add(BigDecimal.ONE)), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(financeAccountStatementDetailVO.getQuantity()));
//                archiveSummaryTaskExcel.setExcludingTax((inStore ? "" : "-") + amount.setScale(2, RoundingMode.HALF_UP).toString());
//                BigDecimal purchaseAmount = financeAccountStatementDetailVO.getPurchasePrice().divide((financeAccountStatementDetailVO.getTaxRateValue().add(BigDecimal.ONE)), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(financeAccountStatementDetailVO.getQuantity()));
//                archiveSummaryTaskExcel.setPurchaseExcludingTax((inStore ? "" : "-") + purchaseAmount.setScale(2, RoundingMode.HALF_UP).toString());
//            }
//            archiveSummaryTaskExcel.setAdjustAmount(financeAccountStatementDetailVO.getAmount().subtract(financeAccountStatementDetailVO.getExcludingTax()).setScale(2, RoundingMode.HALF_UP).toString());
//            archiveSummaryTaskExcel.setAdjustPrice((inStore ? "" : "-") + financeAccountStatementDetailVO.getAmount().setScale(2, RoundingMode.HALF_UP).toString());
//
//            archiveSummaryTaskExcels.add(archiveSummaryTaskExcel);
//        }
        FinanceAccountDetailInput input = new FinanceAccountDetailInput();
        input.setWalletsId(walletsId);
        List<FinanceAccountDetailExcelVO> financeAccountDetailExcelVOS = financeAccountStatementService.doBuilderAccountExportData(input);

        excelWriter.write(financeAccountDetailExcelVOS, threeSheet, writeTableThree);

    }

    /**
     * 城配数据
     *
     * @param excelWriter
     * @param firstSheet
     * @param writeTableOne
     * @param financePurchaseInvoiceWalletsVO
     * @param financeExpenseTypeEnum
     */
    private void archiveSummaryBms(ExcelWriter excelWriter, WriteSheet firstSheet, WriteTable writeTableOne, FinancePurchaseInvoiceWalletsVO financePurchaseInvoiceWalletsVO, FinanceExpenseTypeEnum financeExpenseTypeEnum) {

        //查询票夹对应BMS的打款单信息

        //查询发票对应的票夹信息
        List<PurchaseInvoiceVO> purchaseInvoiceVOS = purchaseInvoiceMapper.selectAll(financePurchaseInvoiceWalletsVO.getId());

        List<ArchiveSummaryBmsDetailExcel> archiveSummaryTaskExcels = new ArrayList<>(16);

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(purchaseInvoiceVOS)) {
            purchaseInvoiceVOS.stream().filter(Objects::nonNull).forEach(data -> {
                ArchiveSummaryBmsDetailExcel archiveSummaryBmsDetailExcel = new ArchiveSummaryBmsDetailExcel();

                archiveSummaryBmsDetailExcel.setInvoiceNumber(data.getInvoiceNumber());
                archiveSummaryBmsDetailExcel.setSupplier(data.getSupplierName());
                if(Objects.nonNull(financeExpenseTypeEnum)){
                    archiveSummaryBmsDetailExcel.setExpenseType(financeExpenseTypeEnum.getStatus());
                }
                if (Objects.nonNull(data.getExcludingTax())) {
                    archiveSummaryBmsDetailExcel.setExcludingTax(negative(data.getInvoiceTypeFace(), data.getExcludingTax()));
                }
                if (Objects.nonNull(data.getTaxRate())) {
                    archiveSummaryBmsDetailExcel.setTaxRate(String.valueOf(data.getTaxRate()));
                }
                if (Objects.nonNull(data.getTaxAmount())) {
                    archiveSummaryBmsDetailExcel.setTaxAmount(negative(data.getInvoiceTypeFace(), data.getTaxAmount()));
                }
                if (Objects.nonNull(data.getIncludedTax())) {
                    archiveSummaryBmsDetailExcel.setIncludedTax(negative(data.getInvoiceTypeFace(), data.getIncludedTax()));
                }

                archiveSummaryTaskExcels.add(archiveSummaryBmsDetailExcel);
            });
        }

//        ArchiveSummaryBmsDetailExcel archiveSummaryBmsDetailExcel = new ArchiveSummaryBmsDetailExcel();
//        archiveSummaryBmsDetailExcel.setInvoiceNumber(purchaseInvoiceVOS.get(0).getInvoiceNumber());
//        archiveSummaryBmsDetailExcel.setSupplier(purchaseInvoiceVOS.get(0).getSupplierName());
//        archiveSummaryBmsDetailExcel.setExpenseType(FinanceExpenseTypeEnum.URBAN_DISTRIBUTION.getStatus());
//        archiveSummaryBmsDetailExcel.setExcludingTax(String.valueOf(purchaseInvoiceVOS.get(0).getExcludingTax()));
//        archiveSummaryBmsDetailExcel.setTaxRate(String.valueOf(purchaseInvoiceVOS.get(0).getTaxRate()));
//        archiveSummaryBmsDetailExcel.setTaxAmount(String.valueOf(purchaseInvoiceVOS.get(0).getTaxAmount()));
//        archiveSummaryBmsDetailExcel.setIncludedTax(String.valueOf(purchaseInvoiceVOS.get(0).getIncludedTax()));
//        archiveSummaryTaskExcels.add(archiveSummaryBmsDetailExcel);
//
        excelWriter.write(archiveSummaryTaskExcels, firstSheet, writeTableOne);

    }

    private void exportPurchaseWallets(ExcelWriter excelWriter) {

    }

    private void abstractDown(Sheet sheet, FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets) {

        sheet.setColumnWidth(0, 8000);
        sheet.setColumnWidth(1, 10000);
        //供应商信息
        SupplierReq supplierReq = supplierMapper.selectDetail(financePurchaseInvoiceWallets.getSupplierId());
        Row firstLine = sheet.createRow(0);
        firstLine.createCell(0).setCellValue("供应商性质");
        firstLine.createCell(1).setCellValue(SupplierTypeEnum.getNameById(supplierReq.getSupplierType()));
        Row twoLine = sheet.createRow(1);
        twoLine.createCell(0).setCellValue("发票销售方");
        twoLine.createCell(1).setCellValue(financePurchaseInvoiceWallets.getSupplierName());
        Row threeLine = sheet.createRow(2);
        threeLine.createCell(0).setCellValue("税号");
        threeLine.createCell(1).setCellValue(supplierReq.getTaxNumber());

        Row fourLine = sheet.createRow(4);
        fourLine.createCell(0).setCellValue("发票张数");
        fourLine.createCell(1).setCellValue(financePurchaseInvoiceWallets.getInvoiceQuantity());

        PurchaseInvoiceVO purchaseInvoiceVO = purchaseInvoiceMapper.selectData(financePurchaseInvoiceWallets.getId());

        Row fiveLine = sheet.createRow(5);
        fiveLine.createCell(0).setCellValue("含税总额");
        fiveLine.createCell(1).setCellValue(purchaseInvoiceVO.getTotalTaxIncluded().toString());
        Row sevenLine = sheet.createRow(6);
        sevenLine.createCell(0).setCellValue("实际可抵扣税额总额");
        sevenLine.createCell(1).setCellValue(purchaseInvoiceVO.getTotalActualDeductibleTax().toString());
        Row eightLine = sheet.createRow(7);
        eightLine.createCell(0).setCellValue("不含税总额");
        eightLine.createCell(1).setCellValue(purchaseInvoiceVO.getTotalAmountExcludingTax().toString());

        List<FinanceAccountStatementVO> financeAccountStatementVOS = financeAccountStatementMapper.selectByWalletsId(financePurchaseInvoiceWallets.getId());
        BigDecimal total = financeAccountStatementVOS.stream().map(FinanceAccountStatementVO::getTotalBillAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        Row nineLine = sheet.createRow(9);
        nineLine.createCell(0).setCellValue("对账单数量");
        nineLine.createCell(1).setCellValue(financeAccountStatementVOS.size());
        Row tenLine = sheet.createRow(10);
        tenLine.createCell(0).setCellValue("对账单总金额");
        tenLine.createCell(1).setCellValue(total.toString());

    }

    private void invoiceInformation(Sheet sheet, Long id) {

        sheet.setColumnWidth(0, 7000);
        sheet.setColumnWidth(1, 4500);
        sheet.setColumnWidth(2, 6000);
        sheet.setColumnWidth(3, 8000);
        sheet.setColumnWidth(4, 7000);
        sheet.setColumnWidth(5, 7000);
        sheet.setColumnWidth(6, 5000);
        sheet.setColumnWidth(7, 5000);
        sheet.setColumnWidth(8, 5000);
        sheet.setColumnWidth(9, 5000);
        sheet.setColumnWidth(10, 7000);
        sheet.setColumnWidth(11, 6000);

        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("开票日期");
        title.createCell(1).setCellValue("供应商性质");
        title.createCell(2).setCellValue("发票销售方");
        title.createCell(3).setCellValue("税号/身份证号");
        title.createCell(4).setCellValue("发票代码");
        title.createCell(5).setCellValue("发票号码");
        title.createCell(6).setCellValue("不含税金额");
        title.createCell(7).setCellValue("税额");
        title.createCell(8).setCellValue("含税金额");
        title.createCell(9).setCellValue("票面平均税率");
        title.createCell(10).setCellValue("实际可抵扣税率");
        title.createCell(11).setCellValue("实际可抵扣税额");

        List<PurchaseInvoiceVO> purchaseInvoiceVOS = purchaseInvoiceMapper.selectAll(id);

        int index = 1;
        for (PurchaseInvoiceVO purchaseInvoiceVO : purchaseInvoiceVOS) {
            Row row = sheet.createRow(index);
            row.createCell(0).setCellValue(DateUtils.localDateToString(purchaseInvoiceVO.getBillingDate(), DateUtils.SPECIFIC_DATE));
            row.createCell(1).setCellValue(SupplierTypeEnum.getNameById(purchaseInvoiceVO.getSupplierType()));
            row.createCell(2).setCellValue(purchaseInvoiceVO.getSupplierName());
            row.createCell(3).setCellValue(purchaseInvoiceVO.getTaxNumber());
            row.createCell(4).setCellValue(purchaseInvoiceVO.getInvoiceCode());
            row.createCell(5).setCellValue(purchaseInvoiceVO.getInvoiceNumber());
            row.createCell(6).setCellValue(purchaseInvoiceVO.getExcludingTax().toString());
            row.createCell(7).setCellValue(purchaseInvoiceVO.getTaxAmount().toString());
            row.createCell(8).setCellValue(purchaseInvoiceVO.getIncludedTax().toString());
            row.createCell(9).setCellValue(purchaseInvoiceVO.getTaxRate().toString() + "%");
            row.createCell(10).setCellValue(!ObjectUtils.isEmpty(purchaseInvoiceVO.getActualTaxRate()) ? purchaseInvoiceVO.getActualTaxRate().toString() + "%" : "");
            row.createCell(11).setCellValue(!ObjectUtils.isEmpty(purchaseInvoiceVO.getActualTaxAmount()) ? purchaseInvoiceVO.getActualTaxAmount().toString() : "");
            index++;
        }

    }

    private void warehousingOrder(Sheet sheet, Long id) {

        sheet.setColumnWidth(0, 6000);
        sheet.setColumnWidth(1, 5000);
        sheet.setColumnWidth(2, 5000);
        sheet.setColumnWidth(3, 5000);
        sheet.setColumnWidth(4, 6000);
        sheet.setColumnWidth(5, 7000);
        sheet.setColumnWidth(6, 7000);
        sheet.setColumnWidth(7, 5000);
        sheet.setColumnWidth(8, 5000);
        sheet.setColumnWidth(9, 5000);
        sheet.setColumnWidth(10, 5000);
        sheet.setColumnWidth(11, 7000);
        sheet.setColumnWidth(12, 6000);
        sheet.setColumnWidth(13, 6000);
        sheet.setColumnWidth(14, 6000);
        sheet.setColumnWidth(15, 6000);

        Row title = sheet.createRow(0);
        title.createCell(0).setCellValue("出入库时间");
        title.createCell(1).setCellValue("出入库类型");
        title.createCell(2).setCellValue("入库/退货单号");
        title.createCell(3).setCellValue("所属采购单号");
        title.createCell(4).setCellValue("所属对账单");
        title.createCell(5).setCellValue("sku");
        title.createCell(6).setCellValue("商品名称");
        title.createCell(7).setCellValue("税收分类编码");
        title.createCell(8).setCellValue("税率");
        title.createCell(9).setCellValue("规格");
        title.createCell(10).setCellValue("出入库数量");
        title.createCell(11).setCellValue("单价");
        title.createCell(12).setCellValue("采购单单价");
        title.createCell(13).setCellValue("出入库总额");
        title.createCell(14).setCellValue("不含税金额");
        title.createCell(15).setCellValue("采购单不含税金额");
        title.createCell(16).setCellValue("调整总额");
        title.createCell(17).setCellValue("调整后金额");

        StockTaskWalletsInput stockTaskWalletsInput = new StockTaskWalletsInput();
        stockTaskWalletsInput.setWalletsId(id);
        List<FinanceAccountStatementDetailVO> financeAccountStatementDetailVOS = financeAccountStatementDetailMapper.selectByWalletsId(stockTaskWalletsInput);

        int index = 1;
        for (FinanceAccountStatementDetailVO financeAccountStatementDetailVO : financeAccountStatementDetailVOS) {
            boolean inStore = Objects.equals(financeAccountStatementDetailVO.getType(), CommonNumbersEnum.ELEVEN.getNumber()) || Objects.equals(financeAccountStatementDetailVO.getType(), StockTaskType.SKIP_STORE_IN.getId());
            Row row = sheet.createRow(index);
            row.createCell(0).setCellValue(BaseDateUtils.localDateTimeToString(financeAccountStatementDetailVO.getAddTime()));
            row.createCell(1).setCellValue(inStore ? "入库" : "出库");
            row.createCell(2).setCellValue(financeAccountStatementDetailVO.getStockTaskProcessId());
            row.createCell(3).setCellValue(financeAccountStatementDetailVO.getPurchaseNo());
            row.createCell(4).setCellValue(financeAccountStatementDetailVO.getFinanceAccountStatementId());
            row.createCell(5).setCellValue(financeAccountStatementDetailVO.getSku());
            row.createCell(6).setCellValue(financeAccountStatementDetailVO.getPdName());
            row.createCell(7).setCellValue(financeAccountStatementDetailVO.getTaxRateCode());
            row.createCell(8).setCellValue(financeAccountStatementDetailVO.getTaxRateValue().toString());
            row.createCell(9).setCellValue(financeAccountStatementDetailVO.getWeight());
            row.createCell(10).setCellValue(inStore ? financeAccountStatementDetailVO.getQuantity().toString() : "-" + financeAccountStatementDetailVO.getQuantity());
            BigDecimal price = financeAccountStatementDetailVO.getAmount().divide(BigDecimal.valueOf(financeAccountStatementDetailVO.getQuantity()), 2, RoundingMode.HALF_UP);
            row.createCell(11).setCellValue(price.toString());
            row.createCell(12).setCellValue(financeAccountStatementDetailVO.getPurchasePrice().toString());
            row.createCell(13).setCellValue((inStore ? "" : "-") + financeAccountStatementDetailVO.getExcludingTax().setScale(2, RoundingMode.HALF_UP).toString());
            //不含税金额 = (单价/(1+税率))*数量
            BigDecimal amount = price.divide((financeAccountStatementDetailVO.getTaxRateValue().add(BigDecimal.ONE)), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(financeAccountStatementDetailVO.getQuantity()));
            row.createCell(14).setCellValue((inStore ? "" : "-") + amount.setScale(2, RoundingMode.HALF_UP).toString());
            BigDecimal purchaseAmount = financeAccountStatementDetailVO.getPurchasePrice().divide((financeAccountStatementDetailVO.getTaxRateValue().add(BigDecimal.ONE)), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(financeAccountStatementDetailVO.getQuantity()));
            row.createCell(15).setCellValue((inStore ? "" : "-") + purchaseAmount.setScale(2, RoundingMode.HALF_UP).toString());
            row.createCell(16).setCellValue(financeAccountStatementDetailVO.getAmount().subtract(financeAccountStatementDetailVO.getExcludingTax()).setScale(2, RoundingMode.HALF_UP).toString());
            row.createCell(17).setCellValue((inStore ? "" : "-") + financeAccountStatementDetailVO.getAmount().setScale(2, RoundingMode.HALF_UP).toString());
            index++;
        }


    }

    @Override
    public AjaxResult autoMatchInvoice(Integer purchaseInvoiceId) {
        PurchaseInvoiceVO purchaseInvoiceVO = purchaseInvoiceMapper.select(purchaseInvoiceId);

        List<String> fileAddress = purchaseInvoiceMapper.fileAddress(purchaseInvoiceVO.getId(), PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());
        if (ObjectUtils.isEmpty(fileAddress)) {
            purchaseInvoiceVO.setPhotoNum(CommonNumbersEnum.ZERO.getNumber());
            purchaseInvoiceVO.setFileNum(CommonNumbersEnum.ZERO.getNumber());
            return AjaxResult.getOK(purchaseInvoiceVO);
        }
        Integer fileNum = 0;
        Integer photoNum = 0;
        List<String> photoList = new ArrayList<>();
        List<String> fileList = new ArrayList<>();
        for (String file : fileAddress) {
            String[] split = file.split("\\.");
            //如果后缀为pdf，则证明是pdf文件
            if (Objects.equals(split[CommonNumbersEnum.ONE.getNumber()], "pdf")) {
                fileList.add(file);
                fileNum++;
            } else {
                photoList.add(file);
                //否则为照片文件
                photoNum++;
            }
        }
        purchaseInvoiceVO.setPhotoList(photoList);
        purchaseInvoiceVO.setFileList(fileList);
        purchaseInvoiceVO.setPhotoNum(photoNum);
        purchaseInvoiceVO.setFileNum(fileNum);
        return AjaxResult.getOK(purchaseInvoiceVO);
    }



    @Override
    public AjaxResult<List<InvoiceSupplierVO>> invoiceSellerMessage(PurchaseInvoiceQuery purchaseInvoiceQuery) {
        if (Objects.isNull(purchaseInvoiceQuery) || StringUtils.isBlank(purchaseInvoiceQuery.getSupplierName())) {
            return AjaxResult.getOK();
        }

        String supplierName = purchaseInvoiceQuery.getSupplierName();
        List<InvoiceSupplierVO> invoiceSupplierVOList = new ArrayList<>(16);

        // 根据供应商名查询供应商列表
        List<SupplierReq> supplierVOS = supplierMapper.selectReqByName(supplierName);
        invoiceSupplierVOList.addAll(buildInvoiceSupplierVOList(supplierVOS, FinanceExpenseTypeEnum.PURCHASE.getId()));

        // 根据承运商名查询承运商列表
        List<CarrierVo> carrierVoList = carrierService.selectByCarrierName(supplierName);
        invoiceSupplierVOList.addAll(buildInvoiceSupplierVOList(carrierVoList, FinanceExpenseTypeEnum.URBAN_DISTRIBUTION.getId()));

        if (CollectionUtils.isEmpty(invoiceSupplierVOList)) {
            return AjaxResult.getOK();
        }

        // 查询 finance_invoice_partner 表的主键 ID，填充后返回给前端
        Map<Integer, List<InvoiceSupplierVO>> invoiceSupplierMap = invoiceSupplierVOList.stream()
                .collect(Collectors.groupingBy(InvoiceSupplierVO::getExpenseType));
        invoiceSupplierMap.forEach((expenseType, supplierList) -> {
            Map<Integer, Long> primaryKeyMap = financeInvoicePartnerMapper.selectBySupplierIdAndExpenseType(expenseType, supplierList.stream().map(InvoiceSupplierVO::getSourceId).collect(Collectors.toList()))
                    .stream().collect(Collectors.toMap(FinanceInvoicePartner::getSupplierId, FinanceInvoicePartner::getId, (oldOne, newOne) -> newOne));
            supplierList.forEach(invoiceSupplierVO -> {
                Long primaryKey = primaryKeyMap.get(invoiceSupplierVO.getSourceId());
                if (Objects.nonNull(primaryKey)) {
                    invoiceSupplierVO.setId(primaryKey.intValue());
                }
            });
        });
        return AjaxResult.getOK(invoiceSupplierVOList);
    }

    @Override
    public List<PurchaseInvoiceVO> selectSrmInvoice(Integer pageIndex, Integer pageSize, PurchaseInvoiceQuery purchaseInvoiceQuery) {
        PageHelper.startPage(pageIndex, pageSize);
        List<PurchaseInvoiceVO> purchaseInvoiceVOS = purchaseInvoiceMapper.selectByTaxNumber(purchaseInvoiceQuery);
        for (PurchaseInvoiceVO purchaseInvoiceVO : purchaseInvoiceVOS) {
            List<String> fileAddress = purchaseInvoiceMapper.fileAddress(purchaseInvoiceVO.getId(), PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());
            purchaseInvoiceVO.setFileAdd(fileAddress);
        }
        return purchaseInvoiceVOS;
    }

    @Override
    public List<PurchaseInvoiceVO> selectSrmInvoiceByWalletsId(Integer pageIndex, Integer pageSize, Long walletsId) {
        PageHelper.startPage(pageIndex, pageSize);
        List<PurchaseInvoiceVO> purchaseInvoiceVOS = purchaseInvoiceMapper.selectAll(walletsId);
        for (PurchaseInvoiceVO purchaseInvoiceVO : purchaseInvoiceVOS) {
            List<String> fileAddress = purchaseInvoiceMapper.fileAddress(purchaseInvoiceVO.getId(), PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());
            purchaseInvoiceVO.setFileAdd(fileAddress);
        }
        return purchaseInvoiceVOS;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void matchBmsInvoice(List<Integer> invoiceIdList, BmsPaymentDocumentVO bmsPaymentDocumentVO) {
        logger.info("BMS生成票夹");
        //根据打款单id查询打款单信息
        if (CollectionUtils.isEmpty(invoiceIdList)) {
            throw new DefaultServiceException("BMS生成票夹 发票id列表为空，结算打款单编号" + bmsPaymentDocumentVO.getPaymentNo());
        }
        List<PurchaseInvoiceVO> purchaseInvoiceVO = purchaseInvoiceMapper.selectByIdList(invoiceIdList);
        BigDecimal invoiceTotal = purchaseInvoiceVO.stream().map(PurchaseInvoiceVO::getIncludedTax).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (invoiceTotal.compareTo(bmsPaymentDocumentVO.getPaymentAmount()) != 0) {
            logger.info("BMS打款单号:" + bmsPaymentDocumentVO.getPaymentNo() + ",付款金额和发票不符合，无法匹配。");
            return;
        }
        StringBuilder stringBuilder = new StringBuilder();
        //票夹编号：PJ20211206001,PJ是票夹的大写开头，********是年月日，最后3位001是当日编号。
        String day = BaseDateUtils.localDateTimeToStringSix(LocalDateTime.now());
        stringBuilder.append("PJ")
                .append(day);
        int num = financePurchaseInvoiceWalletsMapper.selectSum(stringBuilder.toString());
        String sequenceCode = StringUtils.strFormat(Integer.toString(num), CommonNumbersEnum.THREE.getNumber());
        String walletsNo = stringBuilder.append(sequenceCode).toString();
        logger.info("BMS生成票夹编号" + walletsNo);
        //新增票夹
        FinancePurchaseInvoiceWallets financePurchaseInvoiceWallets = new FinancePurchaseInvoiceWallets();
        financePurchaseInvoiceWallets.setSupplierName(bmsPaymentDocumentVO.getCarrierName());
        financePurchaseInvoiceWallets.setSupplierId(bmsPaymentDocumentVO.getCarrierId());
        financePurchaseInvoiceWallets.setTotalIncludedTax(bmsPaymentDocumentVO.getPaymentAmount());
        //票夹刚生成为待复核的状态，状态为有效
        financePurchaseInvoiceWallets.setStatus(CommonNumbersEnum.TWO.getNumber());
        financePurchaseInvoiceWallets.setDeleteStatus(CommonNumbersEnum.ZERO.getNumber());
        financePurchaseInvoiceWallets.setWalletsNo(walletsNo);
        financePurchaseInvoiceWallets.setInvoiceQuantity(invoiceIdList.size());
        Admin admin = adminMapper.selectByPrimaryKey(bmsPaymentDocumentVO.getCreator());
        financePurchaseInvoiceWallets.setCreator(admin.getRealname());
        financePurchaseInvoiceWallets.setCreateTime(LocalDateTime.now());
        financePurchaseInvoiceWallets.setCreatorAdminId(bmsPaymentDocumentVO.getCreator());
        log.info("bms生成票夹，bms打款单json:{}, 业务类型:{}", JSON.toJSONString(bmsPaymentDocumentVO), bmsPaymentDocumentVO.getBusinessType());
        financePurchaseInvoiceWallets.setExpenseType(Objects.equals(QuotationEnum.BusinessType.DELIVERY_BUSINESS.name(), bmsPaymentDocumentVO.getBusinessType()) ? FinanceExpenseTypeEnum.URBAN_DISTRIBUTION.getId() : FinanceExpenseTypeEnum.TRUNK_BUSINESS.getId());
        financePurchaseInvoiceWalletsMapper.insertSelective(financePurchaseInvoiceWallets);
        //批量修改发票的walletsId

        bindInvoiceToWallets(invoiceIdList, financePurchaseInvoiceWallets.getId());
    }

    private void bindInvoiceToWallets(List<Integer> invoiceIdList, Long walletsId) {
        if (commonDynamicConfig.getInputInvoiceSwitch()) {
           fmsFacade.bindInvoiceToWallets(invoiceIdList, walletsId);
        }else{
            invoiceIdList.forEach(i -> {
                PurchaseInvoice purchaseInvoice = new PurchaseInvoice();
                purchaseInvoice.setId(i);
                purchaseInvoice.setWalletsId(walletsId);
                purchaseInvoiceMapper.update(purchaseInvoice);
            });
        }
    }

    @Override
    public List<PurchaseInvoiceVO> invoiceBms(String invoiceSearchKey, String taxNumber) {
        PurchaseInvoiceQuery purchaseInvoiceQuery = new PurchaseInvoiceQuery();
        purchaseInvoiceQuery.setTaxNumber(taxNumber);
        purchaseInvoiceQuery.setInvoiceSearchKey(invoiceSearchKey);
        List<PurchaseInvoiceVO> purchaseInvoiceVOS = purchaseInvoiceMapper.selectList(purchaseInvoiceQuery);
        purchaseInvoiceVOS.forEach(invoice -> {
            List<String> fileAddress = purchaseInvoiceMapper.fileAddress(invoice.getPurchaseInvoiceId(), PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());
            List<String> photoList = new ArrayList<>();
            for (String file : fileAddress) {
                String[] split = file.split("\\.");
                //如果后缀为pdf，则证明是pdf文件
                if (!Objects.equals(split[CommonNumbersEnum.ONE.getNumber()], "pdf")) {
                    photoList.add(file);
                }
            }
            invoice.setPhotoList(photoList);
        });
        return purchaseInvoiceVOS;
    }

    @Override
    public PurchaseInvoiceVO queryByInvoiceId(Integer id) {
        PurchaseInvoiceVO invoiceVO = purchaseInvoiceMapper.queryByInvoiceId(id);
        List<String> fileAddress = purchaseInvoiceMapper.fileAddress(invoiceVO.getId(), PurchaseInvoiceEnum.CAN_MATCH_STATUS.getId());
        List<String> photoList = new ArrayList<>();
        for (String file : fileAddress) {
            String[] split = file.split("\\.");
            //如果后缀为pdf，则证明是pdf文件
            if (!Objects.equals(split[CommonNumbersEnum.ONE.getNumber()], "pdf")) {
                photoList.add(file);
            }
        }
        invoiceVO.setPhotoList(photoList);
        return invoiceVO;
    }


    /**
     * 根据供应商列表或者承运商列表生成对应的 InvoiceSupplierVO 列表
     *
     * @param suppliers   供应商列表或者承运商列表
     * @param expenseType 工单类型
     * @return 生成的 InvoiceSupplierVO 列表
     */
    private List<InvoiceSupplierVO> buildInvoiceSupplierVOList(List<?> suppliers, Integer expenseType) {
        if (CollectionUtils.isEmpty(suppliers)) {
            return Collections.emptyList();
        }
        return suppliers.stream()
                .map(supplier -> {
                    InvoiceSupplierVO invoiceSupplier = new InvoiceSupplierVO(expenseType);
                    if (supplier instanceof SupplierReq) {
                        SupplierReq supplierReq = (SupplierReq) supplier;
                        invoiceSupplier.setTaxNumber(supplierReq.getTaxNumber());
                        invoiceSupplier.setCorporateName(supplierReq.getName());
                        invoiceSupplier.setSourceId(supplierReq.getId());
                        invoiceSupplier.setName(supplierReq.getName());
                        invoiceSupplier.setSupplierType(supplierReq.getSupplierType());
                        invoiceSupplier.setInvoiceSupplierType(InvoiceSupplierTypeEnum.SUPPLIER.getId());
                    } else if (supplier instanceof CarrierVo) {
                        CarrierVo carrierVo = (CarrierVo) supplier;
                        invoiceSupplier.setTaxNumber(carrierVo.getTaxNo());
                        invoiceSupplier.setCorporateName(carrierVo.getCarrierName());
                        invoiceSupplier.setSourceId(carrierVo.getId().intValue());
                        invoiceSupplier.setName(carrierVo.getCarrierName());
                        invoiceSupplier.setSupplierType(SupplierTypeEnum.ENTERPRISE_FACTORY.getId());
                        invoiceSupplier.setInvoiceSupplierType(InvoiceSupplierTypeEnum.CARRIER.getId());

                    }
                    return invoiceSupplier;
                })
                .collect(Collectors.toList());
    }
}
