package net.summerfarm.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.redis.TimingOrderLock;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.downloadCenter.bean.FileDownloadRecordDTO;
import net.summerfarm.enums.*;
import net.summerfarm.facade.ofc.OfcQueryFacade;
import net.summerfarm.facade.tms.TmsDeliveryRuleFacade;
import net.summerfarm.facade.tms.input.DeliveryRuleQueryInput;
import net.summerfarm.facade.wms.AreaStoreFacade;
import net.summerfarm.facade.wms.dto.AreaStoreLockReq;
import net.summerfarm.facade.wms.dto.AreaStoreQueryReq;
import net.summerfarm.facade.wms.dto.AreaStoreQueryRes;
import net.summerfarm.facade.wms.dto.AreaStoreUnLockReq;
import net.summerfarm.facade.wms.dto.OrderLockSkuDetailReqDTO;
import net.summerfarm.facade.wms.dto.OrderUnLockSkuDetailReqDTO;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.DTO.NotifyDeliveryFeeDTO;
import net.summerfarm.model.DTO.UpdateDeliveryDateDto;
import net.summerfarm.model.DTO.UpdateDeliveryPlanDTO;
import net.summerfarm.model.DTO.ValidateLimitSpuDTO;
import net.summerfarm.model.bo.SetDeliveryPlanResult;
import net.summerfarm.model.bo.UpdateDeliveryPlanResultBO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.DeliveryDateQueryInput;
import net.summerfarm.model.vo.*;
import net.summerfarm.ofc.client.enums.OfcOrderSourceEnum;
import net.summerfarm.ofc.client.resp.DeliveryDateQueryResp;
import net.summerfarm.service.*;
import net.summerfarm.task.AsyncTaskService;
import net.summerfarm.task.MailUtil;
import net.summerfarm.warehouse.mapper.WarehouseInventoryMappingMapper;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.summerfarm.warehouse.model.vo.WarehouseInventoryMappingVO;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.oss.enums.OSSExpiredLabelEnum;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class DeliveryPlanServiceImpl extends BaseService implements DeliveryPlanService {

    public static final String BATCH_SET_DELIVERY_PLAN_RESULT_FILE_NAME = "批量设置配送计划-处理结果-%d%d.xls";

    public static final String SHORT_TERM_SPU_CONFIG = "SHORT_TERM_SPU_CONFIG";

    private static String DELIVERY_FEE_MODIFY = "#### 【配送费调整提醒】\n> ###### 以下配送地址超区需调整配送费，请及时处理"
            + "\n> ###### 客户编号：{0}\n> ###### 客户名称：{1}\n> ###### 配送地址：{2}\n> ###### 发起建议配送费：{3}元\n> ###### 运营服务区域：{4}\n> ###### 发起人：{5}";

    private static String DELIVERY_FEE_MODIFY_KEY = "deliveryFeeModifyRobotUrl";
    @Lazy
    @Resource
    private AreaStoreService areaStoreService;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Lazy
    @Resource
    private PurchasesConfigService purchasesConfigService;
    @Resource
    private MailUtil mailUtil;
    @Resource
    private AdminMapper adminMapper;
    @Lazy
    @Resource
    private MsgAdminService msgAdminService;
    @Lazy
    @Resource
    private QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    private TimingOrderLock timingOrderLock;
    @Resource
    private PCMaxThresholdMapper pcMaxThresholdMapper;
    @Resource
    private DingTalkService dingTalkService;
    @Resource
    private AreaStoreMapper areaStoreMapper;
    @Resource
    private WarehouseInventoryService warehouseInventoryService;
    @Resource
    SampleApplyService sampleApplyService;
    @Resource
    AreaMapper areaMapper;
    @Resource
    MerchantMapper merchantMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private WarehouseInventoryMappingMapper warehouseInventoryMappingMapper;
    @Lazy
    @Resource
    private AsyncTaskService asyncTaskService;
    @Resource
    private FenceService fenceService;
    @Resource
    private QiNiuService qiNiuService;
    @Resource
    private FileDownloadRecordMapper fileDownloadRecordMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private FileDownloadRecordService fileDownloadRecordService;
    private DeliveryPlanService selfService;

    @Lazy
    @Resource
    private OrderService orderService;
    @Resource
    private TmsDeliveryRuleFacade tmsDeliveryRuleFacade;
    @Resource
    DeliverPlanRemarkSnapshotService deliverPlanRemarkSnapshotService;
    @Resource
    private OfcQueryFacade ofcQueryFacade;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private AreaStoreFacade areaStoreFacade;
    @Resource
    private TimingOrderMapper timingOrderMapper;

    @PostConstruct
    private void setSelf() {
        selfService = getContext().getBean(DeliveryPlanService.class);
    }

    /**
     * 明日配送的省心送订单(如果冻结未加上,则推迟配送日)
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void tomorrowOrderLock(DeliveryPlanVO deliveryPlanVO, Integer areaNo, MsgTemplate template, List<Admin> toAdmins,List<DeliveryPlanVO> mgs) {
        //省心送订单需要扣库存。。。  剩下的2状态应该都是预售 支付前已经扣减过虚拟库存和新增过冻结库存
        if (Objects.equals(OrderTypeEnum.TIMING.getId(), deliveryPlanVO.getType())) {
            Integer quantity = queryStoreQuantity(deliveryPlanVO.getContactId(), deliveryPlanVO.getSku());
            if(quantity < deliveryPlanVO.getQuantity()){
                //数量不足
                delayDeliveryTime(deliveryPlanVO, toAdmins, template,mgs);
                return;
            }
            // 再次查询，防止脏读
            DeliveryPlan deliveryPlan = deliveryPlanMapper.selectById(deliveryPlanVO.getId());
            if (!(deliveryPlan.getStatus().intValue() == OrderStatusEnum.WAIT_DELIVERY.getId())) {
                // 已经冻结了或者取消了配送计划
                return;
            }
            //校验库存信息
            /*Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
            try {
                //1.减虚拟库存
                areaStoreService.updateOnlineStockByStoreNo(true, -deliveryPlanVO.getQuantity(), deliveryPlanVO.getSku(), areaNo,
                        SaleStockChangeTypeEnum.TIMING_PLAN_CREATE, deliveryPlanVO.getOrderNo(),recordMap, NumberUtils.INTEGER_ZERO);
            } catch (Exception e) { // 虚拟库存不够

                // 推迟订单
                delayDeliveryTime(deliveryPlanVO, toAdmins, template,mgs);
                return;
            }
            //2.增加锁定库存
            areaStoreService.updateLockStockByStoreNo(deliveryPlanVO.getQuantity(), deliveryPlanVO.getSku(), areaNo,
                    SaleStockChangeTypeEnum.TIMING_PLAN_CREATE, deliveryPlanVO.getOrderNo(), recordMap);
            quantityChangeRecordService.insertRecord(recordMap);*/

            try {
                //占用库存
                AreaStoreLockReq storeLockReq = new AreaStoreLockReq();
                storeLockReq.setOrderNo(deliveryPlanVO.getOrderNo());
                storeLockReq.setOrderSubNo(String.valueOf(deliveryPlanVO.getId()));
                storeLockReq.setOperatorNo(deliveryPlanVO.getOrderNo());
                storeLockReq.setIdempotentNo(deliveryPlanVO.getOrderNo() + deliveryPlanVO.getId());
                storeLockReq.setOrderType(SaleStockChangeTypeEnum.TIMING_PLAN_CREATE.getTypeName());
                storeLockReq.setContactId(deliveryPlanVO.getContactId());
                storeLockReq.setMerchantId(Long.valueOf(deliveryPlanVO.getmId()));
                storeLockReq.setSource(SourceEnum.XM_MALL_TIMING.getValue());
                storeLockReq.setOperatorName("系统默认（定时任务）");
                OrderLockSkuDetailReqDTO orderLockSkuDetailReqDTO = new OrderLockSkuDetailReqDTO();
                orderLockSkuDetailReqDTO.setSkuCode(deliveryPlanVO.getSku());
                orderLockSkuDetailReqDTO.setOccupyQuantity(deliveryPlanVO.getQuantity());
                storeLockReq.setOrderLockSkuDetailReqDTOS(Collections.singletonList(orderLockSkuDetailReqDTO));
                areaStoreFacade.storeLock(storeLockReq);
            } catch (Exception e) {
                // 推迟订单
                delayDeliveryTime(deliveryPlanVO, toAdmins, template,mgs);
                return;
            }

            //采购预警通知
            purchasesConfigService.msgArrival(areaNo, deliveryPlanVO.getSku());
        }
        //3.将订单修改

        DeliveryPlan update = new DeliveryPlan();
        update.setId(deliveryPlanVO.getId());
        update.setStatus((short) 3);
        logger.info("订单状态:" + JSON.toJSONString(update));
        deliveryPlanMapper.updateById(update);
    }

    public void delayDeliveryTime(DeliveryPlanVO deliveryPlanVO, List<Admin> toAdmins, MsgTemplate template,List<DeliveryPlanVO> mgs) {
        if(deliveryPlanVO == null){
            return;
        }
        //老的配送日期
        LocalDate oldDeliveryTime = deliveryPlanVO.getDeliveryTime();

        //获取配送日期 并且根据配送日期获取待配送的计划再累加
        Integer mId = deliveryPlanVO.getmId();
        Integer areaNo = deliveryPlanVO.getAreaNo();
        //Area area = areaMapper.selectByAreaNo(areaNo);
        Merchant merchant = merchantMapper.selectByMId(mId.longValue());

        //获取省心送订单的配送结束日期
        TimingOrder timingOrder = timingOrderMapper.selectByOrderNo(deliveryPlanVO.getOrderNo());

        DeliveryDateQueryResp dateQueryResp = ofcQueryFacade.queryDeliveryDate(LocalDateTime.of(oldDeliveryTime, LocalTime.now()),merchant.getmId(),deliveryPlanVO.getContactId(),
                oldDeliveryTime,timingOrder.getDeliveryEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),OfcOrderSourceEnum.XM_MALL_TIMING,Collections.singletonList(deliveryPlanVO.getSku()));
        for (LocalDate deliveryDate : dateQueryResp.getDeliveryTimes()) {
            //排除当前配送日期
            if (deliveryDate.isBefore(oldDeliveryTime) || Objects.equals(deliveryDate, oldDeliveryTime)) {
                continue;
            }
            DeliveryPlan selectDelivery = new DeliveryPlan();
            selectDelivery.setDeliveryTime(deliveryDate);
            selectDelivery.setContactId(deliveryPlanVO.getContactId());
            selectDelivery.setOrderNo(deliveryPlanVO.getOrderNo());
            DeliveryPlan selectDeliveryPlan = deliveryPlanMapper.selectOne(selectDelivery);
            if (selectDeliveryPlan == null) {
                //订单推迟
                DeliveryPlan update = new DeliveryPlan();
                update.setId(deliveryPlanVO.getId());
                update.setDeliveryTime(deliveryDate);
                update.setPutOffTime(LocalDate.now());
                deliveryPlanMapper.updateById(update);
                break;
            } else if (selectDeliveryPlan.getStatus().intValue() == OrderStatusEnum.WAIT_DELIVERY.getId()){
                //获取当前省心送订单最晚配送计划的时间,更新
                DeliveryPlan updateDeliveryPlan = new DeliveryPlan();
                updateDeliveryPlan.setId(deliveryPlanVO.getId());
                updateDeliveryPlan.setStatus((short) 11);
                deliveryPlanMapper.updateById(updateDeliveryPlan);
                //订单加上去
                DeliveryPlan update = new DeliveryPlan();
                update.setId(selectDeliveryPlan.getId());
                update.setQuantity(deliveryPlanVO.getQuantity() + selectDeliveryPlan.getQuantity());
                update.setPutOffTime(LocalDate.now());
                deliveryPlanMapper.updateById(update);
                break;
            }
        }
        mgs.add(deliveryPlanVO);
        /*LocalDate deliveryDate = tmsDeliveryRuleFacade.queryCloudDeliveryDate(DeliveryRuleQueryInput.builder()
                .source(SourceEnum.XM_MALL_TIMING)
                .orderTime(LocalDateTime.of(deliveryPlanVO.getDeliveryTime(), LocalTime.now()))
                .merchantId(merchant.getmId())
                .contactId(deliveryPlanVO.getContactId())
                .build());
        //LocalDate deliveryDate = sampleApplyService.getDeliveryDate(area, merchant,LocalDateTime.now(),deliveryPlanVO.getContactId());
        DeliveryPlan selectDelivery = new DeliveryPlan();
        selectDelivery.setDeliveryTime(deliveryDate);
        selectDelivery.setContactId(deliveryPlanVO.getContactId());
        selectDelivery.setOrderNo(deliveryPlanVO.getOrderNo());
        DeliveryPlan selectDeliveryPlan = deliveryPlanMapper.selectOne(selectDelivery);
        if (selectDeliveryPlan == null) {
            //订单推迟
            DeliveryPlan update = new DeliveryPlan();
            update.setId(deliveryPlanVO.getId());
            update.setDeliveryTime(deliveryDate);
            update.setPutOffTime(LocalDate.now());
            deliveryPlanMapper.updateById(update);
        } else {
            //获取当前省心送订单最晚配送计划的时间,更新
            DeliveryPlan updateDeliveryPlan = new DeliveryPlan();
            updateDeliveryPlan.setId(deliveryPlanVO.getId());
            updateDeliveryPlan.setStatus((short) 11);
            deliveryPlanMapper.updateById(updateDeliveryPlan);
            //订单加上去
            DeliveryPlan update = new DeliveryPlan();
            update.setId(selectDeliveryPlan.getId());
            update.setQuantity(deliveryPlanVO.getQuantity() + selectDeliveryPlan.getQuantity());
            update.setPutOffTime(LocalDate.now());
            deliveryPlanMapper.updateById(update);
        }
        mgs.add(deliveryPlanVO);*/
    }


    /**
     * 后天配送的省心送订单加冻结 (不成功也不会推迟)
     */
    @Override
    public void theDayAfterTomorrowLock(DeliveryPlanVO deliveryPlanVO, Integer areaNo,List<DeliveryPlanVO> msg,MsgTemplate msgTemplate) {
        //省心送订单需要扣库存。。。  剩下的2状态应该都是预售 支付前已经扣减过虚拟库存和新增过冻结库存
        logger.info("theDayAfterTomorrowLock={},areaNo={}",deliveryPlanVO.getOrderNo(),areaNo);

        if (Objects.equals(OrderTypeEnum.TIMING.getId(), deliveryPlanVO.getType())) {

            Integer quantity = queryStoreQuantity(deliveryPlanVO.getContactId(), deliveryPlanVO.getSku());
            // 再次查询，防止脏读
            DeliveryPlan deliveryPlan = deliveryPlanMapper.selectById(deliveryPlanVO.getId());
            if (!(deliveryPlan.getStatus().intValue() == OrderStatusEnum.WAIT_DELIVERY.getId())) {
                // 已经冻结了或者取消了配送计划
                return;
            }
            if(quantity < deliveryPlanVO.getQuantity()){
                //数量不足
                msg.add(deliveryPlanVO);
                return;
            }

            try {
                //占用库存
                AreaStoreLockReq storeLockReq = new AreaStoreLockReq();
                storeLockReq.setOrderNo(deliveryPlanVO.getOrderNo());
                storeLockReq.setOrderSubNo(String.valueOf(deliveryPlanVO.getId()));
                storeLockReq.setOperatorNo(deliveryPlanVO.getOrderNo());
                storeLockReq.setIdempotentNo(deliveryPlanVO.getOrderNo() + deliveryPlanVO.getId());
                storeLockReq.setOrderType(SaleStockChangeTypeEnum.TIMING_PLAN_CREATE.getTypeName());
                storeLockReq.setContactId(deliveryPlanVO.getContactId());
                storeLockReq.setMerchantId(Long.valueOf(deliveryPlanVO.getmId()));
                storeLockReq.setSource(SourceEnum.XM_MALL_TIMING.getValue());
                storeLockReq.setOperatorName("系统默认（定时任务）");
                OrderLockSkuDetailReqDTO orderLockSkuDetailReqDTO = new OrderLockSkuDetailReqDTO();
                orderLockSkuDetailReqDTO.setSkuCode(deliveryPlanVO.getSku());
                orderLockSkuDetailReqDTO.setOccupyQuantity(deliveryPlanVO.getQuantity());
                storeLockReq.setOrderLockSkuDetailReqDTOS(Collections.singletonList(orderLockSkuDetailReqDTO));
                areaStoreFacade.storeLock(storeLockReq);
            } catch (Exception e) {
                //冻结不足
                msg.add(deliveryPlanVO);
                return;
            }

            /*Map<String,QuantityChangeRecord> recordMap = new HashMap<>();
            try {
                //1.减虚拟库存
                areaStoreService.updateOnlineStockByStoreNo(true, -deliveryPlanVO.getQuantity(), deliveryPlanVO.getSku(),areaNo,
                        SaleStockChangeTypeEnum.TIMING_PLAN_CREATE, deliveryPlanVO.getOrderNo(), recordMap,NumberUtils.INTEGER_ZERO);

            } catch (Exception e) {
                //冻结不足
                msg.add(deliveryPlanVO);
                return;
            }
            areaStoreService.updateLockStockByStoreNo(deliveryPlanVO.getQuantity(), deliveryPlanVO.getSku(), areaNo,
                    SaleStockChangeTypeEnum.TIMING_PLAN_CREATE, deliveryPlanVO.getOrderNo(), recordMap);
            quantityChangeRecordService.insertRecord(recordMap);*/
            //采购预警通知
            purchasesConfigService.msgArrival(areaNo, deliveryPlanVO.getSku());
        }
        //3.将订单修改
        DeliveryPlan update = new DeliveryPlan();
        update.setId(deliveryPlanVO.getId());
        update.setStatus((short) 3);
        logger.info("订单状态:" + JSON.toJSONString(update));
        deliveryPlanMapper.updateById(update);

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    public void timingOrderLock(DeliveryPlanVO deliveryPlanVO, Integer areaNo) {
        //Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        try {
            // 查询可用的虚拟库存数量
            Integer quantity = queryStoreQuantity(deliveryPlanVO.getContactId(), deliveryPlanVO.getSku());
            if(quantity < deliveryPlanVO.getQuantity()){
                return;
            }
            logger.info("timingOrderLock={},areaNo={}",deliveryPlanVO.getOrderNo(),areaNo);
            PCMaxThreshold pcMaxThreshold = pcMaxThresholdMapper.selectOneBySoreNo(areaNo, deliveryPlanVO.getSku());
            if(pcMaxThreshold != null){
                if(deliveryPlanVO.getQuantity() > pcMaxThreshold.getThreshold()){
                    //发送大单信息
                    dingTalkService.sendBigOrder(deliveryPlanVO.getOrderNo(), deliveryPlanVO.getSku(),String.valueOf(deliveryPlanVO.getId()));
                }
            }
        } catch (Exception e) {
            logger.info("省心送冻结大单消息发送失败 err={}",e.getMessage());
        }
        if (Objects.equals(OrderTypeEnum.TIMING.getId(), deliveryPlanVO.getType())) {
            /*//1.减虚拟库存
            areaStoreService.updateOnlineStockByStoreNo(true, -deliveryPlanVO.getQuantity(), deliveryPlanVO.getSku(), areaNo,
                    SaleStockChangeTypeEnum.TIMING_PLAN_CREATE, deliveryPlanVO.getOrderNo(), recordMap,NumberUtils.INTEGER_ZERO);
            //2.增加锁定库存
            areaStoreService.updateLockStockByStoreNo(deliveryPlanVO.getQuantity(), deliveryPlanVO.getSku(), areaNo,
                    SaleStockChangeTypeEnum.TIMING_PLAN_CREATE, deliveryPlanVO.getOrderNo(), recordMap);*/
            AreaStoreLockReq storeLockReq = new AreaStoreLockReq();
            storeLockReq.setOrderNo(deliveryPlanVO.getOrderNo());
            storeLockReq.setOrderSubNo(String.valueOf(deliveryPlanVO.getId()));
            storeLockReq.setOperatorNo(deliveryPlanVO.getOrderNo());
            storeLockReq.setIdempotentNo(deliveryPlanVO.getOrderNo() + deliveryPlanVO.getId());
            storeLockReq.setOrderType(SaleStockChangeTypeEnum.TIMING_PLAN_CREATE.getTypeName());
            storeLockReq.setContactId(deliveryPlanVO.getContactId());
            storeLockReq.setMerchantId(Long.valueOf(deliveryPlanVO.getmId()));
            storeLockReq.setSource(SourceEnum.XM_MALL_TIMING.getValue());
            storeLockReq.setOperatorName(getAdminName());
            OrderLockSkuDetailReqDTO orderLockSkuDetailReqDTO = new OrderLockSkuDetailReqDTO();
            orderLockSkuDetailReqDTO.setSkuCode(deliveryPlanVO.getSku());
            orderLockSkuDetailReqDTO.setOccupyQuantity(deliveryPlanVO.getQuantity());
            storeLockReq.setOrderLockSkuDetailReqDTOS(Collections.singletonList(orderLockSkuDetailReqDTO));
            areaStoreFacade.storeLock(storeLockReq);
        }
        //quantityChangeRecordService.insertRecord(recordMap);

        //3.将订单修改

        DeliveryPlan update = new DeliveryPlan();
        update.setId(deliveryPlanVO.getId());
        update.setStatus((short) 3);
        logger.info("订单状态:" + JSON.toJSONString(update));
        deliveryPlanMapper.updateById(update);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    public void timingOrderUnLock(DeliveryPlanVO deliveryPlanVO) {
        logger.info("timingOrderUnLock sku={},storeNo ={},oldDate={},newDate={}",deliveryPlanVO.getSku(),deliveryPlanVO.getStoreNo());

        int hour = LocalDateTime.now().getHour();
        LocalDate now = LocalDate.now();
        if(hour >= 22){
            now = now.plusDays(1);
        }
        //不解冻明天配送的省心送订单配送计划
        if(deliveryPlanVO.getDeliveryTime() !=null && Objects.equals(now,deliveryPlanVO.getDeliveryTime())){
            return;
        }
        //Map<String, QuantityChangeRecord> recordMap = new HashMap<>(16);
        if (Objects.equals(OrderTypeEnum.TIMING.getId(), deliveryPlanVO.getType())) {
            /*//1.加虚拟库存
            areaStoreService.updateOnlineStockByStoreNo(true, deliveryPlanVO.getQuantity(), deliveryPlanVO.getSku(), deliveryPlanVO.getStoreNo(),
                    SaleStockChangeTypeEnum.TIMING_PLAN_DEL, deliveryPlanVO.getOrderNo(), recordMap,NumberUtils.INTEGER_ZERO);
            //2.减锁定库存
            areaStoreService.updateLockStockByStoreNo(-deliveryPlanVO.getQuantity(), deliveryPlanVO.getSku(), deliveryPlanVO.getStoreNo(),
                    SaleStockChangeTypeEnum.TIMING_PLAN_DEL, deliveryPlanVO.getOrderNo(), recordMap);*/
            AreaStoreUnLockReq areaStoreUnLockReq = new AreaStoreUnLockReq();
            areaStoreUnLockReq.setContactId(deliveryPlanVO.getContactId());
            areaStoreUnLockReq.setOrderType(SaleStockChangeTypeEnum.TIMING_PLAN_DEL.getTypeName());
            areaStoreUnLockReq.setOrderNo(deliveryPlanVO.getOrderNo());
            areaStoreUnLockReq.setOrderSubNo(String.valueOf(deliveryPlanVO.getId()));
            areaStoreUnLockReq.setIdempotentNo(deliveryPlanVO.getOrderNo() + deliveryPlanVO.getId());
            areaStoreUnLockReq.setOperatorNo(deliveryPlanVO.getOrderNo());
            areaStoreUnLockReq.setMerchantId(Long.valueOf(deliveryPlanVO.getmId()));
            areaStoreUnLockReq.setSource(SourceEnum.XM_MALL_TIMING.getValue());
            areaStoreUnLockReq.setOperatorName(getAdminName());
            OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
            orderUnLockSkuDetailReqDTO.setSkuCode(deliveryPlanVO.getSku());
            orderUnLockSkuDetailReqDTO.setReleaseQuantity(deliveryPlanVO.getQuantity());
            areaStoreUnLockReq.setOrderReleaseSkuDetailReqDTOS(Collections.singletonList(orderUnLockSkuDetailReqDTO));
            areaStoreFacade.storeUnLock(areaStoreUnLockReq);
        }
        //quantityChangeRecordService.insertRecord(recordMap);

        //更改配送计划状态为待配送
        DeliveryPlan update = new DeliveryPlan();
        update.setId(deliveryPlanVO.getId());
        update.setStatus((short) 2);
        logger.info("订单状态:" + JSON.toJSONString(update));
        deliveryPlanMapper.updateById(update);
    }



    @Override
    public void  timingOrderLockQuantity(String sku, Integer storeNo, LocalDate oldDate ,LocalDate newDate,String type) {
        logger.info("timingOrderLockQuantity sku={},storeNo ={},oldDate={},newDate={}",sku,storeNo,oldDate,newDate);
        //获取省心送操作配送计划锁
        Long lockId = timingOrderLock.nextId();
        if (!timingOrderLock.tryLock("timingOrderLock",lockId,15L,TimeUnit.SECONDS)) {
            logger.error("修改配送计划任务尝试锁定超时");
            if (Global.isProduct()) {
                mailUtil.sendMail("库存增加修改省心送订单","修改省心送配送计划尝试锁定超时");
                return;
            }
        }
        try {
            //校验当前仓库是否使用其他仓库存
            //中心仓调动，更改使用中心仓的数据
            List<DeliveryPlanVO> deliveryPlanVOS = new ArrayList<>();
            //消息类型
            Boolean msg = null;
            //校验当前sku 是否使用中心仓的库存
            LocalDate unLock = null;

            //增加冻结库存
            if (oldDate.isBefore(newDate)) {
                msg = true;
                unLock = oldDate;

                //优先校验库存信息
                //查询本仓和使用本仓库存的省心送配送计划
                List<DeliveryPlanVO> naturePlan = deliveryPlanMapper.getSomeTimeUnLockPlan(oldDate, newDate, sku, null, storeNo, 2);
                List<DeliveryPlanVO> someTimeUnLockPlan = deliveryPlanMapper.getSomeTimeUnLockPlan(oldDate, newDate, sku, storeNo, null,2);
                if(!CollectionUtils.isEmpty(naturePlan)){
                    deliveryPlanVOS.addAll(naturePlan);
                }
                if(!CollectionUtils.isEmpty(someTimeUnLockPlan)){
                    deliveryPlanVOS.addAll(someTimeUnLockPlan);
                }
                //解冻结库存
            } else {
                msg = false;
                unLock = newDate;

                List<DeliveryPlanVO> naturePlan = deliveryPlanMapper.getSomeTimeUnLockPlan(newDate, oldDate, sku, null, storeNo, 3);
                List<DeliveryPlanVO> someTimeUnLockPlan = deliveryPlanMapper.getSomeTimeUnLockPlan(newDate, oldDate, sku, storeNo, null, 3);
                if(!CollectionUtils.isEmpty(naturePlan)){
                    deliveryPlanVOS.addAll(naturePlan);
                }
                if(!CollectionUtils.isEmpty(someTimeUnLockPlan)){
                    deliveryPlanVOS.addAll(someTimeUnLockPlan);
                }

            }

            for (DeliveryPlanVO deliveryPlanVO : deliveryPlanVOS) {
                if (msg) {
                    timingOrderLock(deliveryPlanVO, deliveryPlanVO.getStoreNo());
                } else {
                    timingOrderUnLock(deliveryPlanVO);
                }
            }
            //库存切换或者解冻库存 ，重新冻结库存
            List<DeliveryPlanVO> deliveryPlanVOList = new ArrayList<>();

            if(!msg || Objects.equals(type,SaleStockChangeTypeEnum.TRUST_STORE_NO_CHANGE.getTypeName())){
                //获取相关配送计划
                LocalDate now = LocalDate.now();

                List<DeliveryPlanVO> naturePlan = deliveryPlanMapper.getSomeTimeUnLockPlan(now, unLock, sku, null, storeNo, 2);
                if(!CollectionUtils.isEmpty(naturePlan)){
                    deliveryPlanVOS.addAll(naturePlan);
                }
                List<DeliveryPlanVO> someTimeUnLockPlan = deliveryPlanMapper.getSomeTimeUnLockPlan(now, unLock, sku, storeNo, null, 2);
                if(!CollectionUtils.isEmpty(someTimeUnLockPlan)){
                    deliveryPlanVOS.addAll(someTimeUnLockPlan);
                }

            }
            for (DeliveryPlanVO deliveryPlanVO : deliveryPlanVOList) {
                //冻结库存
                timingOrderLock(deliveryPlanVO, deliveryPlanVO.getStoreNo());

            }
        } finally {
            timingOrderLock.unLock("timingOrderLock",lockId);
        }

    }

    @Override
    public AjaxResult selectWait(Long contactId) {
        return AjaxResult.getOK(deliveryPlanMapper.selectWaitByContact(contactId));
    }

    /**
     * 省心送可用库存逻辑与单店可用库存逻辑相同 虚拟库存 - 大客户剩余预留库存数量
     * 大客户剩余预留库存数量 = 当预留使用数量小于预留上限 - 预留下限时 为预留上限 - 预留使用数量
     * 否则为 预留库存下限
     * 查询可用库存信息
     */
    private Integer queryStoreQuantity(Long contactId ,String sku){


        /*WarehouseInventoryMapping mapping = warehouseInventoryService.selectByUniqueIndex(storeNo, sku);
        Integer warehouseNo = mapping.getWarehouseNo();
        AreaStore select = areaStoreMapper.selectWithOutDataPermission(new AreaStore(warehouseNo, sku));

        Integer onlineQuantity = select.getOnlineQuantity();
        Integer reserveMaxQuantity = select.getReserveMaxQuantity();
        Integer reserveMinQuantity = select.getReserveMinQuantity();
        Integer reserveUseQuantity = select.getReserveUseQuantity();
        if(reserveMaxQuantity != null){
            Integer reserveQuantity = reserveMaxQuantity - reserveMinQuantity;
            onlineQuantity = reserveUseQuantity > reserveQuantity ? onlineQuantity - reserveMinQuantity : onlineQuantity - (reserveMaxQuantity - reserveUseQuantity);
        }*/

        //库存查询新模型
        AreaStoreQueryReq areaStoreQueryReq = new AreaStoreQueryReq();
        areaStoreQueryReq.setContactId(contactId);
        areaStoreQueryReq.setSource(SourceEnum.XM_MALL_TIMING.getValue());
        areaStoreQueryReq.setSkuCodeList(Collections.singletonList(sku));
        Map<String, AreaStoreQueryRes> storeQueryResMap = areaStoreFacade.getInfo(areaStoreQueryReq);
        if (CollectionUtils.isEmpty(storeQueryResMap) || Objects.isNull(storeQueryResMap.get(sku))) {
            logger.info("tomorrowOrderLock[]storeQueryResMap[]empty[]areaStoreQueryReq:{}", JSON.toJSONString(areaStoreQueryReq));
            return 0;
        }
        AreaStoreQueryRes areaStoreQueryRes = storeQueryResMap.get(sku);
        return areaStoreQueryRes.getOnlineQuantity();
    }

    @Transactional
    @Override
    public AjaxResult batchUpdateDeliveryDate(UpdateDeliveryDateDto updateDeliveryDateDto) {
        LocalDate newDeliveryDate = updateDeliveryDateDto.getNewDeliveryDate();
        LocalDate nowDeliveryDate = updateDeliveryDateDto.getNowDeliveryDate();
        //1、围栏 2、城配仓
        Integer type = updateDeliveryDateDto.getType();
        if(type == 1 && CollectionUtils.isEmpty(updateDeliveryDateDto.getFenceAddressList())){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT,"围栏地址不能为空");
        }
        if(type == 2 && updateDeliveryDateDto.getStoreNo() == null){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT,"城配仓不能为空");
        }

        //按照正常计划数据集合
        ArrayList<BatchUpdateDeliveryDateVo> isPlanDeliveryDateList = new ArrayList<>();
        //按照日期顺延数据集合
        ArrayList<BatchUpdateDeliveryDateVo> notPlanDeliveryDateList = new ArrayList<>();
        //需要取消的省心送的数据集合
        ArrayList<DeliveryPlanHeartVo> cancelHeartList = new ArrayList<>();

        //查询订单现配送时间所有的配送计划
        List<BatchUpdateDeliveryDateVo> batchUpdateDeliveryDateVos = deliveryPlanMapper.selectByUpdateDeliveryDateDto(updateDeliveryDateDto);
        //筛选周期配送的客户，放到正常计划、按照日期顺延集合中，更新时间
        cycleSendUser(batchUpdateDeliveryDateVos,isPlanDeliveryDateList,notPlanDeliveryDateList,newDeliveryDate,nowDeliveryDate);
        //取消中间的省心送配送计划,释放库存
        cancelHeartPlan(isPlanDeliveryDateList,notPlanDeliveryDateList,nowDeliveryDate,newDeliveryDate,cancelHeartList);
        //信息发送
        asyncTaskService.sendBatchUpdateDeliveryDateMesaageAndWeChat(isPlanDeliveryDateList,notPlanDeliveryDateList,cancelHeartList);
        return AjaxResult.getOK();
    }

    private void cancelHeartPlan(ArrayList<BatchUpdateDeliveryDateVo> isPlanDeliveryDateList, ArrayList<BatchUpdateDeliveryDateVo> notPlanDeliveryDateList,
                                 LocalDate nowDeliveryDate, LocalDate newDeliveryDate, ArrayList<DeliveryPlanHeartVo> cancelHeartList) {
        ArrayList<Integer> dpIdList = new ArrayList<>();
        dpIdList.addAll(isPlanDeliveryDateList.stream().map(BatchUpdateDeliveryDateVo::getId).collect(Collectors.toList()));
        dpIdList.addAll(notPlanDeliveryDateList.stream().map(BatchUpdateDeliveryDateVo::getId).collect(Collectors.toList()));

        //非周期配送客户省心送订单的取消
        if(!CollectionUtils.isEmpty(isPlanDeliveryDateList)){
            List<Integer> contactIdList = isPlanDeliveryDateList.stream().map(BatchUpdateDeliveryDateVo::getContactId).collect(Collectors.toList());
            cancelHeartOrderBetweenDay(contactIdList,nowDeliveryDate,newDeliveryDate,cancelHeartList,dpIdList);
        }

        //周期配送客户省心送订单的取消
        notPlanDeliveryDateList.stream().forEach(notPlanDeliveryDate ->{
            cancelHeartOrderBetweenDay(Arrays.asList(notPlanDeliveryDate.getContactId()),nowDeliveryDate,notPlanDeliveryDate.getNewDeliveryTime(),cancelHeartList,dpIdList);
        });

        List<Integer> ids = cancelHeartList.stream().map(DeliveryPlanHeartVo::getId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(ids)){
            deliveryPlanMapper.cancelBatchUpdate(ids,OrderStatusEnum.CANCEL.getId());
        }
    }

    /**
     * 释放库存
     * @param deliveryPlan
     */
    private void freedStoreByOrderNo(DeliveryPlanHeartVo deliveryPlan) {
        //省心送只会有一个sku
        List<OrderItem> orderItems = orderItemMapper.queryItemList(deliveryPlan.getOrderNo());
        for (OrderItem orderItem : orderItems) {
            //Integer quantity = orderItem.getAmount();
            Integer quantity = deliveryPlan.getQuantity();
            String sku = orderItem.getSku();
            String orderNo = orderItem.getOrderNo();

            AreaStoreUnLockReq areaStoreUnLockReq = new AreaStoreUnLockReq();
            areaStoreUnLockReq.setContactId(deliveryPlan.getContactId());
            areaStoreUnLockReq.setOrderType(SaleStockChangeTypeEnum.TIMING_PLAN_DEL.getTypeName());
            areaStoreUnLockReq.setOrderNo(orderNo);
            areaStoreUnLockReq.setOrderSubNo(String.valueOf(deliveryPlan.getId()));
            areaStoreUnLockReq.setIdempotentNo(orderNo + deliveryPlan.getId());
            areaStoreUnLockReq.setOperatorNo(orderNo);
            areaStoreUnLockReq.setMerchantId(deliveryPlan.getMId());
            areaStoreUnLockReq.setSource(SourceEnum.XM_MALL_TIMING.getValue());
            areaStoreUnLockReq.setOperatorName(getAdminName());
            OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
            orderUnLockSkuDetailReqDTO.setSkuCode(sku);
            orderUnLockSkuDetailReqDTO.setReleaseQuantity(quantity);
            areaStoreUnLockReq.setOrderReleaseSkuDetailReqDTOS(Collections.singletonList(orderUnLockSkuDetailReqDTO));
            areaStoreFacade.storeUnLock(areaStoreUnLockReq);
            /*Integer orderStoreNo = deliveryPlan.getOrderStoreNo();
            Map<String,QuantityChangeRecord> recordMap = new HashMap<>();
            WarehouseInventoryMappingVO warehouseInventoryMappingVO = warehouseInventoryMappingMapper.selectVoByUniqueIndex(orderStoreNo, sku);
            //加虚拟 减冻结
            areaStoreService.updateOnlineStockByStoreNo(true, quantity, sku, orderStoreNo, SaleStockChangeTypeEnum.CANCEL_ORDER, orderNo, recordMap, NumberUtils.INTEGER_ZERO);
            areaStoreService.updateLockStockByWarehouseNo(-quantity, sku, orderStoreNo,warehouseInventoryMappingVO.getWarehouseNo(), SaleStockChangeTypeEnum.CANCEL_ORDER, orderNo, recordMap);
            quantityChangeRecordService.insertRecord(recordMap);*/
        }
    }

    /**
     * 筛选周期配送的客户，放到正常计划、按照日期顺延集合中
     * @param batchUpdateDeliveryDateVos
     * @param isPlanDeliveryDateList
     * @param notPlanDeliveryDateList
     * @param newDeliveryDate
     * @param nowDeliveryDate
     */
    private void cycleSendUser(List<BatchUpdateDeliveryDateVo> batchUpdateDeliveryDateVos, ArrayList<BatchUpdateDeliveryDateVo> isPlanDeliveryDateList,
                               ArrayList<BatchUpdateDeliveryDateVo> notPlanDeliveryDateList, LocalDate newDeliveryDate,
                               LocalDate nowDeliveryDate) {
        for (BatchUpdateDeliveryDateVo batchUpdateDeliveryDateVo : batchUpdateDeliveryDateVos) {
            batchUpdateDeliveryDateVo.setNewDeliveryTime(newDeliveryDate);
            Integer contactId = batchUpdateDeliveryDateVo.getContactId();
            Contact contact = contactMapper.selectByPrimaryKey(Long.parseLong(String.valueOf(contactId)));
            //查询配送sku信息
            List<String> skuList = null;
            List<OrderItem> itemList = orderItemMapper.querySkuDetailItemList(batchUpdateDeliveryDateVo.getOrderNo());
            if (!CollectionUtils.isEmpty(itemList)){
                skuList = itemList.stream().map(OrderItem::getSku).collect(Collectors.toList());
            }
            LocalDateTime payTime = paymentMapper.selectPayTimeByOrderNo(batchUpdateDeliveryDateVo.getOrderNo());
            /*List<LocalDate> deliveryDates = tmsDeliveryRuleFacade.queryBetweenCloudDeliveryDate(DeliveryRuleQueryInput.builder()
                    .source(SourceEnum.XM_MALL_TIMING)
                    .orderTime(newDeliveryDate.atStartOfDay())
                    .merchantId(contact.getmId())
                    .contactId(contact.getContactId())
                    .queryBeginDate(LocalDateTime.now().toLocalDate())
                    .queryEndDate(LocalDateTime.now().toLocalDate().plusMonths(2))
                    .build());*/
            DeliveryDateQueryResp dateQueryResp = ofcQueryFacade.queryDeliveryDate(null != payTime ? payTime : LocalDateTime.now(),contact.getmId(),contact.getContactId(),LocalDate.now(),LocalDate.now().plusMonths(2), OfcOrderSourceEnum.XM_MALL,skuList);


            //按照设定日期
            if(dateQueryResp.getDeliveryTimes().contains(newDeliveryDate)){
                batchUpdateDeliveryDateVo.setNewDeliveryTime(newDeliveryDate);
                notPlanDeliveryDateList.add(batchUpdateDeliveryDateVo);
            }else{
                //按照新日期
                for (LocalDate deliveryDate : dateQueryResp.getDeliveryTimes()) {
                    if(deliveryDate.isAfter(newDeliveryDate)){
                        batchUpdateDeliveryDateVo.setNewDeliveryTime(deliveryDate);
                        break;
                    }
                }
                notPlanDeliveryDateList.add(batchUpdateDeliveryDateVo);
            }
        }
        List<DeliveryPlan> deliveryPlanList = new ArrayList<>();
        for (BatchUpdateDeliveryDateVo batchUpdateDeliveryDateVo : batchUpdateDeliveryDateVos) {
            deliveryPlanMapper.updateDelivery(batchUpdateDeliveryDateVo);
            DeliveryPlan deliveryPlan = new DeliveryPlan();
            deliveryPlan.setOrderNo(batchUpdateDeliveryDateVo.getOrderNo());
            deliveryPlan.setId(batchUpdateDeliveryDateVo.getId());
            deliveryPlanList.add(deliveryPlan);
        }
        if (!CollectionUtils.isEmpty(deliveryPlanList)){
            //插入操作人
            orderService.addDeliveryPlanExtend(deliveryPlanList,deliveryPlanExtendType.UPDATE.getType());
        }

    }

    @Transactional
    @Override
    public void cancelHeartOrderBetweenDay(List<Integer> contactIdList, LocalDate nowDeliveryDate, LocalDate newDeliveryDate,
                                           ArrayList<DeliveryPlanHeartVo> cancelHeartList, ArrayList<Integer> dpIdList) {
        List<DeliveryPlanHeartVo> deliveryPlanList = deliveryPlanMapper.queryBetweenDateByIds(contactIdList,nowDeliveryDate,newDeliveryDate,dpIdList);
        deliveryPlanList.stream().forEach(deliveryPlan ->{
            //取消中间的省心送配送计划
            if(deliveryPlan.getStatus().intValue() == OrderStatusEnum.DELIVERING.getId()
                    || deliveryPlan.getStatus().intValue() == OrderStatusEnum.RECEIVED.getId()){
                //释放库存
                freedStoreByOrderNo(deliveryPlan);
            }
            deliveryPlan.setStatus(new Short(OrderStatusEnum.CANCEL.getId()+""));
            cancelHeartList.add(deliveryPlan);
        });
    }

    @Override
    public AjaxResult batchSetDeliveryPlan(UpdateDeliveryPlanDTO updateDeliveryPlanDTO) {
        //参数校验
        LocalDate deliveryDate = updateDeliveryPlanDTO.getDeliveryDate();
        LocalDate now = LocalDate.now();
        if (deliveryDate.isBefore(now) || now.isEqual(deliveryDate)){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT,"选择的日期需大于当前日期");
        }
        Set<String> orderNos = updateDeliveryPlanDTO.getOrderNos();
        List<TimingOrderVO> timingOrders = ordersMapper.selectTimingOrderInfos(orderNos);
        //校验是否为指定的SPU
        AjaxResult result = validateLimitSpu(timingOrders, orderNos);
        if (!result.isSuccess()){
            return result;
        }
        //按下单时间排序
        timingOrders.sort(Comparator.comparing(TimingOrderVO::getOrderTime));

        //查询订单相关的配送计划
        List<DeliveryRecordVO> deliveryRecords = deliveryPlanMapper.selectDeliveryRecords(orderNos);
        Map<String, DeliveryRecordVO> deliveryRecordMap = deliveryRecords.stream().filter(e -> StrUtil.isNotBlank(e.getDeliveryTimeStr()))
                .collect(Collectors.toMap(DeliveryRecordVO::getOrderNo, e -> e));
        //查询客户地址信息
        Set<Long> midSet = timingOrders.stream().map(TimingOrderVO::getMId).collect(Collectors.toSet());
        List<Contact> contacts = contactMapper.selectContacts(midSet);
        Map<Long, List<Contact>> contactMap = contacts.stream().collect(Collectors.groupingBy(Contact::getmId));

        List<SetDeliveryPlanResult> results = batchHandelTimingOrders(deliveryDate, timingOrders, deliveryRecordMap, contactMap);
        logger.info("批量设置配送计划（省心送）处理结果：{}", JSON.toJSONString(results));

        String fileName = String.format(BATCH_SET_DELIVERY_PLAN_RESULT_FILE_NAME, System.currentTimeMillis(), getAdminId());
        UpdateDeliveryPlanResultBO updateDeliveryPlanResultBO = new UpdateDeliveryPlanResultBO("", results);
        FileDownloadRecordDTO fileDownloadRecordDTO = FileDownloadRecordDTO.builder().fileName(fileName).bizEnum(FileDownloadRecordEnum.DELIVERY_PLAN)
                .uploadObj(updateDeliveryPlanResultBO).ossExpiredLabelEnum(OSSExpiredLabelEnum.THREE_DAY).operatorId(getAdminId()).build();
        fileDownloadRecordService.startUpload(fileDownloadRecordDTO);
        return AjaxResult.getOK();
    }

    /**
     * 批量设置配送计划（省心送）
     * @param deliveryDate 配送日期
     * @param timingOrders 省心送订单
     * @param deliveryRecordMap 订单编号-配送记录映射
     * @param contactMap 商户ID-联系人信息集合映射
     * @return 设置配送计划结果集合
     */
    private List<SetDeliveryPlanResult> batchHandelTimingOrders(LocalDate deliveryDate, List<TimingOrderVO> timingOrders, Map<String, DeliveryRecordVO> deliveryRecordMap, Map<Long, List<Contact>> contactMap) {
        //查找相关订单设置配送计划并根据匹配情况输出设置结果集合
        List<SetDeliveryPlanResult> results = new ArrayList<>();
        //判断订单相关信息并进行处理
        for (TimingOrderVO timingOrder : timingOrders) {
            SetDeliveryPlanResult setDeliveryPlanResult = new SetDeliveryPlanResult(timingOrder.getOrderNo(),
                    timingOrder.getMerchantName(),timingOrder.getPhone(),timingOrder.getOrderTime());
            //判断订单客户是否多地址
            List<Contact> contactList = contactMap.get(timingOrder.getMId());
            if (contactList == null || contactList.isEmpty()){
                setDeliveryPlanResult.handle(BatchSetDeliveryPlanEnum.ADDRESS_EXCEPTION);
                results.add(setDeliveryPlanResult);
                continue;
            }
            if (contactList.size() > 1){
                setDeliveryPlanResult.handle(BatchSetDeliveryPlanEnum.TOO_MANY_DELIVERY_ADDRESS);
                results.add(setDeliveryPlanResult);
                continue;
            }
            Contact contact = contactList.get(0);

            setDeliveryPlanResult.setDeliveryAddress(getCompleteAddress(contact));
            //查询该订单在指定配送时间的下一个有效配送日
            Long mId = timingOrder.getMId();
            Merchant merchant = merchantMapper.selectByMId(mId);
            //LocalDate nextDeliveryDate = sampleApplyService.getDeliveryDate(new Area(), merchant, deliveryDate.atStartOfDay(),contact.getContactId());
            LocalDate nextDeliveryDate = tmsDeliveryRuleFacade.queryCloudDeliveryDate(DeliveryRuleQueryInput.builder()
                    .source(SourceEnum.XM_MALL_TIMING)
                    .orderTime(deliveryDate.atStartOfDay())
                    .merchantId(merchant.getmId())
                    .contactId(contact.getContactId())
                    .city(contact.getCity())
                    .area(contact.getArea())
                    .poi(contact.getPoiNote())
                    .build());

            DeliveryRecordVO deliveryRecordVO = deliveryRecordMap.get(timingOrder.getOrderNo());
            if (deliveryRecordVO == null){
                //无配送计划
                setDeliveryPlanResult.setDeliveryAmount(timingOrder.getAmount());
            }else {
                //有配送计划
                //判断未配送数量是否为0
                int waitDeliveryAmount = timingOrder.getAmount() - deliveryRecordVO.getSumQuantity();
                if (waitDeliveryAmount <= 0){
                    setDeliveryPlanResult.handle(BatchSetDeliveryPlanEnum.WAIT_DELIVERY_AMOUNT_INSUFFICIENT);
                    results.add(setDeliveryPlanResult);
                    continue;
                }
                setDeliveryPlanResult.setDeliveryAmount(waitDeliveryAmount);
                //判断订单是否有重复的配送计划
                if (deliveryRecordVO.getDeliveryTimeStr().contains(nextDeliveryDate.toString())){
                    setDeliveryPlanResult.handle(BatchSetDeliveryPlanEnum.EXISTED_DELIVERY_PLAN);
                    results.add(setDeliveryPlanResult);
                    continue;
                }
            }
            setDeliveryPlanResult.setDeliveryDate(nextDeliveryDate);
            //判断配送日是否在7天内，7天外可直接设置配送计划
            DeliveryPlan plan = new DeliveryPlan();
            plan.setOrderNo(timingOrder.getOrderNo());
            plan.setStatus((short)OrderStatusEnum.WAIT_DELIVERY.getId());
            plan.setDeliveryTime(nextDeliveryDate);
            plan.setQuantity(setDeliveryPlanResult.getDeliveryAmount());
            plan.setContactId(contact.getContactId());
            plan.setOrderStoreNo(contact.getStoreNo());
            plan.setAdminId(getAdminId());

            LocalDate dateOfAfterSevenDay = LocalDate.now().plusDays(7);
            if (nextDeliveryDate.isAfter(dateOfAfterSevenDay)){
                deliveryPlanMapper.insert(plan);
                deliverPlanRemarkSnapshotService.addDeliveryPlan(contact, plan);
                //插入操作人
                orderService.addDeliveryPlanExtend(Collections.singletonList(plan),deliveryPlanExtendType.INSERT.getType());
                setDeliveryPlanResult.handle(BatchSetDeliveryPlanEnum.SUCCESS);
                results.add(setDeliveryPlanResult);
                continue;
            }
            //虚拟库存需要根据sku和城配仓去拿
            //在7天判断虚拟库存是否足够，不够直接记录原因
            Integer onlineQuantity = queryStoreQuantity(contact.getContactId(), timingOrder.getSku());
            if (onlineQuantity < setDeliveryPlanResult.getDeliveryAmount()){
                setDeliveryPlanResult.handle(BatchSetDeliveryPlanEnum.STOCK_INSUFFICIENT);
                results.add(setDeliveryPlanResult);
                continue;
            }
            //虚拟库存足够的话扣减库存并设置配送计划并绑定城配仓
            if (!selfService.reduceStoreAndSetDeliveryPlan(timingOrder, setDeliveryPlanResult, contact, plan)) {
                setDeliveryPlanResult.handle(BatchSetDeliveryPlanEnum.STOCK_INSUFFICIENT);
                results.add(setDeliveryPlanResult);
                continue;
            }
            //设置配送计划成功
            setDeliveryPlanResult.handle(BatchSetDeliveryPlanEnum.SUCCESS);
            results.add(setDeliveryPlanResult);
        }
        return results;
    }

    /**
     * 获取完整的地址信息
     * @param contact 联系人实体
     * @return 完整地址信息
     */
    private String getCompleteAddress(Contact contact) {
        if (contact == null){
            return "";
        }
        String completeAddress = contact.getProvince() + contact.getCity() + contact.getArea() + contact.getAddress() + contact.getHouseNumber();
        if (StrUtil.isBlank(completeAddress)){
            return "";
        }
        return completeAddress.replaceAll(" ", "").replaceAll("NULL", "").replaceAll("null", "");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean reduceStoreAndSetDeliveryPlan(TimingOrderVO timingOrder, SetDeliveryPlanResult setDeliveryPlanResult, Contact contact, DeliveryPlan plan) {
        //校验库存信息
        /*Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        try {
            //1.减虚拟库存
            areaStoreService.updateOnlineStockByStoreNo(true, -setDeliveryPlanResult.getDeliveryAmount(), timingOrder.getSku(), contact.getStoreNo(),
                    SaleStockChangeTypeEnum.TIMING_PLAN_CREATE, timingOrder.getOrderNo(), recordMap, NumberUtils.INTEGER_ZERO);
        } catch (DefaultServiceException e) {
            //虚拟库存不够
            return false;
        }
        //2.增加锁定库存
        areaStoreService.updateLockStockByStoreNo(setDeliveryPlanResult.getDeliveryAmount(), timingOrder.getSku(), contact.getStoreNo(),
                SaleStockChangeTypeEnum.TIMING_PLAN_CREATE, timingOrder.getOrderNo(), recordMap);*/

        //3.冻结库存成功设置为待收货
        plan.setStatus((short)OrderStatusEnum.DELIVERING.getId());
        deliveryPlanMapper.insert(plan);
        try {
            AreaStoreLockReq storeLockReq = new AreaStoreLockReq();
            storeLockReq.setOrderNo(timingOrder.getOrderNo());
            storeLockReq.setOrderSubNo(String.valueOf(plan.getId()));
            //storeLockReq.setOrderSubNo(timingOrder.getOrderNo());
            storeLockReq.setOperatorNo(timingOrder.getOrderNo());
            storeLockReq.setIdempotentNo(timingOrder.getOrderNo() + plan.getId());
            storeLockReq.setOrderType(SaleStockChangeTypeEnum.TIMING_PLAN_CREATE.getTypeName());
            storeLockReq.setContactId(contact.getContactId());
            storeLockReq.setMerchantId(contact.getmId());
            storeLockReq.setSource(SourceEnum.XM_MALL_TIMING.getValue());
            storeLockReq.setOperatorName(getAdminName());
            OrderLockSkuDetailReqDTO orderLockSkuDetailReqDTO = new OrderLockSkuDetailReqDTO();
            orderLockSkuDetailReqDTO.setSkuCode(timingOrder.getSku());
            orderLockSkuDetailReqDTO.setOccupyQuantity(setDeliveryPlanResult.getDeliveryAmount());
            storeLockReq.setOrderLockSkuDetailReqDTOS(Collections.singletonList(orderLockSkuDetailReqDTO));
            areaStoreFacade.storeLock(storeLockReq);
        } catch (Exception e) {
            //防止出现脏数据--历史逻辑是占用完库存再新增一条配送计划
            deliveryPlanMapper.deleteById(plan.getId());
            return false;
        }

        deliverPlanRemarkSnapshotService.addDeliveryPlan(contact, plan);
        //插入操作人
        orderService.addDeliveryPlanExtend(Collections.singletonList(plan),deliveryPlanExtendType.INSERT.getType());
        //quantityChangeRecordService.insertRecord(recordMap);
        return true;
    }

    @Override
    public boolean notifyModifyDeliveryFee(NotifyDeliveryFeeDTO notifyDeliveryFeeDTO) {
        Contact contact = contactMapper.selectByPrimaryKey(notifyDeliveryFeeDTO.getContactId());
        if (contact == null) {
            logger.error("【配送费调整提醒】联系人信息不存在！contactId:{}", notifyDeliveryFeeDTO.getContactId());
            return false;
        }
        Long mId = contact.getmId();
        Merchant merchant = merchantMapper.selectByMId(mId);
        if (merchant == null) {
            logger.error("【配送费调整提醒】商户信息不存在！mId:{}, contactId:{}", mId, notifyDeliveryFeeDTO.getContactId());
            return false;
        }
        Area area = areaMapper.selectByAreaNo(merchant.getAreaNo());
        if (area == null) {
            logger.error("【配送费调整提醒】运营服务区域不存在！areaNo:{}, contactId:{}", merchant.getAreaNo(), notifyDeliveryFeeDTO.getContactId());
            return false;
        }
        StringBuilder sb = new StringBuilder(contact.getProvince());
        sb.append(contact.getCity()).append(contact.getArea()).append(contact.getAddress()).
                append(contact.getHouseNumber() == null ? "" : contact.getHouseNumber());
        String content = MessageFormat.format(DELIVERY_FEE_MODIFY, String.valueOf(mId), merchant.getMname(),
                sb.toString(), notifyDeliveryFeeDTO.getDeliveryFee(), area.getAreaName(), getAdminName());
        Map<String, String> msg = new HashMap<>();
        msg.put("title", "【配送费调整提醒】");
        msg.put("text", content);
        Config config = configMapper.selectOne(DELIVERY_FEE_MODIFY_KEY);
        DingTalkRobotUtil.sendMarkDownMsg(config.getValue(), () -> msg, null);
        return true;
    }

    @Override
    public CommonResult queryCouldDeliveryDate(DeliveryDateQueryInput deliveryDateQueryInput) {
        Orders orders = ordersMapper.selectByOrderNo(deliveryDateQueryInput.getOrderNo());
        if (orders == null) {
            throw new BizException("订单不存在");
        }

        List<String> skuList = null;
        List<OrderItem> orderItemList =  orderItemMapper.querySkuDetailItemList(deliveryDateQueryInput.getOrderNo());

        if (!CollectionUtils.isEmpty(orderItemList)){
            skuList = orderItemList.stream().map(OrderItem::getSku).collect(Collectors.toList());
        }
        DeliveryDateQueryResp dateQueryResp = ofcQueryFacade.queryDeliveryDate(LocalDateTime.now(),deliveryDateQueryInput.getMerchantId(),
                deliveryDateQueryInput.getContactId(),deliveryDateQueryInput.getQueryBeginDate(),deliveryDateQueryInput.getQueryEndDate(),
                TmsDistOrderTypeSourceEnum.getOfcSourceEnumByOrderType(orders.getType()), skuList);

        return CommonResult.ok(dateQueryResp.getDeliveryTimes());
    }

    @Override
    public Workbook handleResultToExcel(UpdateDeliveryPlanResultBO updateDeliveryPlanResultBO) {
        String[] titleName = {"订单编号", "客户名称", "客户手机号","下单时间","配送时间","配送数量","配送地址","处理结果","原因"};
        Workbook workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet();
        //设置单元格宽度
        sheet.setAutobreaks(true);
        sheet.setColumnWidth(0, 5000);
        sheet.setColumnWidth(1, 5000);
        sheet.setColumnWidth(2, 5000);
        sheet.setColumnWidth(3, 5000);
        sheet.setColumnWidth(4, 5000);
        sheet.setColumnWidth(5, 5000);
        sheet.setColumnWidth(6, 12000);
        sheet.setColumnWidth(7, 5000);
        sheet.setColumnWidth(8, 7000);
        //设置标题行
        Row title = sheet.createRow(NumberUtils.INTEGER_ZERO);
        for (int i = 0; i < titleName.length; i++) {
            Cell cell = title.createCell(i);
            cell.setCellValue(titleName[i]);
        }
        //填充内容行
        List<SetDeliveryPlanResult> results = updateDeliveryPlanResultBO.getResults();
        for (int i = 0; i < results.size(); i++) {
            SetDeliveryPlanResult result = results.get(i);
            Row nextRow = sheet.createRow(i + 1);
            nextRow.createCell(0).setCellValue(result.getOrderNo());
            nextRow.createCell(1).setCellValue(result.getMerchantName());
            nextRow.createCell(2).setCellValue(result.getPhone());
            nextRow.createCell(3).setCellValue(Objects.isNull(result.getOrderTime())?"": BaseDateUtils.localDateTimeToString(result.getOrderTime()));
            nextRow.createCell(4).setCellValue(Objects.isNull(result.getDeliveryDate())?"":result.getDeliveryDate().toString());
            nextRow.createCell(5).setCellValue(Objects.isNull(result.getDeliveryAmount())?"":result.getDeliveryAmount().toString());
            nextRow.createCell(6).setCellValue(Objects.isNull(result.getDeliveryAddress())?"":result.getDeliveryAddress());
            nextRow.createCell(7).setCellValue(result.getResult());
            nextRow.createCell(8).setCellValue(Objects.isNull(result.getReason())?"":result.getReason());
        }
        return workbook;
    }

    @Override
    public AjaxResult validateLimitSpu(ValidateLimitSpuDTO validateLimitSpuDTO) {
        //查询省心送订单信息
        Set<String> orderNos = validateLimitSpuDTO.getOrderNos();
        List<TimingOrderVO> heartOrders = ordersMapper.selectTimingOrderInfos(orderNos);
        return validateLimitSpu(heartOrders, orderNos);
    }

    /**
     * 校验限定SPU（省心送）
     * @param timingOrders 省心送订单集合
     * @param orderNos 订单编号集合
     * @return 校验结果
     */
    private AjaxResult validateLimitSpu(List<TimingOrderVO> timingOrders, Set<String> orderNos) {
        //查询省心送订单信息
        if (timingOrders.size() < orderNos.size()){
            return AjaxResult.getErrorWithMsg("存在无效订单");
        }
        //校验是否为待收货的省心送订单
        String errorOrderNoStr = timingOrders.stream().filter(e -> !(OrderTypeEnum.TIMING.getId().equals(e.getType()) && OrderStatusEnum.DELIVERING.getId() == e.getStatus()))
                .map(TimingOrderVO::getOrderNo).distinct().collect(Collectors.joining(Global.SEPARATING_SYMBOL));
        if (StrUtil.isNotBlank(errorOrderNoStr)){
            return AjaxResult.getErrorWithMsg(String.format("请选择待收货的省心送订单！订单编号：%s异常", errorOrderNoStr));
        }
        //查询配置表中短保商品SPU项
        Config config = configMapper.selectOne(SHORT_TERM_SPU_CONFIG);
        if (config == null || StrUtil.isBlank(config.getValue())){
            return AjaxResult.getErrorWithMsg("未配置有效的短保商品SPUID");
        }
        String spuStr = config.getValue();
        List<String> spuList = Arrays.asList(spuStr.split("、"));
        //校验是否为指定的SPU
        errorOrderNoStr = timingOrders.stream().filter(e -> !spuList.contains(e.getPdNo())).map(TimingOrderVO::getOrderNo).collect(Collectors.joining(Global.SEPARATING_SYMBOL));
        if (StrUtil.isNotBlank(errorOrderNoStr)){
            return AjaxResult.getErrorWithMsg(String.format("请选择SPUID：%s的订单商品！订单编号：%s异常", spuStr, errorOrderNoStr));
        }
        return AjaxResult.getOK();
    }

}
