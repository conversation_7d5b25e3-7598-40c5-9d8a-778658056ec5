package net.summerfarm.service.impl;

import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageHelper;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.DtsUtils;
import net.summerfarm.common.util.OptionFlagUtil;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.PreCutOffOrderUtil;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.enums.*;
import net.summerfarm.facade.ofc.OfcQueryFacade;
import net.summerfarm.facade.ofc.OfcSampleApplyQueryFacade;
import net.summerfarm.facade.tms.TmsDeliveryRuleFacade;
import net.summerfarm.facade.tms.input.DeliveryRuleQueryInput;
import net.summerfarm.facade.wms.AreaStoreFacade;
import net.summerfarm.facade.wms.dto.AreaStoreQueryReq;
import net.summerfarm.facade.wms.dto.AreaStoreQueryRes;
import net.summerfarm.facade.wms.dto.AreaStoreUnLockReq;
import net.summerfarm.facade.wms.dto.OrderUnLockSkuDetailReqDTO;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.model.bo.price.PriceInfoBO;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.SampleApplyVO;
import net.summerfarm.module.wms.biz.service.StockTaskOrderCancelService;
import net.summerfarm.module.wms.infrastructure.repository.StockTaskOrderSkuRepository;
import net.summerfarm.module.wms.infrastructure.repository.StockTaskWaveRepository;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.ofc.client.enums.OfcOrderSourceEnum;
import net.summerfarm.ofc.client.resp.DeliveryDateQueryResp;
import net.summerfarm.service.*;
import net.summerfarm.warehouse.enums.WarehouseLogisticsConfigEnum;
import net.summerfarm.warehouse.model.domain.WarehouseInventoryMapping;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;
import net.summerfarm.warehouse.model.vo.WarehouseLogisticsCenterVO;
import net.summerfarm.warehouse.service.WarehouseInventoryService;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.summerfarm.warehouse.service.WarehouseStorageService;
import net.summerfarm.wnc.client.enums.SourceEnum;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ct
 * create at:  2020/4/26  15:46
 */
@Service
public class SampleApplyServiceImpl implements SampleApplyService {

    @Resource
    InventoryMapper inventoryMapper;
    @Resource
    AreaStoreMapper areaStoreMapper;
    @Resource
    AreaMapper areaMapper;
    @Resource
    AreaSkuMapper areaSkuMapper;
    @Resource
    SampleSkuMapper sampleSkuMapper;
    @Resource
    SampleApplyMapper sampleApplyMapper;
    @Resource
    ConfigMapper configMapper;
    @Resource
    DingTalkService dingTalkService;
    @Resource
    BaseService baseService;
    @Resource
    AdminAuthExtendMapper adminAuthExtendMapper;
    @Resource
    StockTaskMapper stockTaskMapper;
    @Resource
    StockTaskItemMapper stockTaskItemMapper;
    @Resource
    MerchantMapper merchantMapper;
    @Resource
    AreaStoreService areaStoreService;
    @Resource
    QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    AdminMapper adminMapper;
    @Resource
    WarehouseStorageService warehouseStorageService;
    @Resource
    WarehouseLogisticsService warehouseLogisticsService;
    @Resource
    WarehouseInventoryService warehouseInventoryService;
    @Resource
    PreCutOffOrderUtil preCutOffOrderUtil;
    @Resource
    ContactMapper contactMapper;
    @Resource
    private SampleApplyReviewMapper sampleApplyReviewMapper;
    @Resource
    private StockTaskWaveRepository stockTaskWaveRepository;

    @Resource
    private AreaService areaService;
    @Resource
    private FenceService fenceService;
    @Resource
    private TmsStopDeliveryMapper tmsStopDeliveryMapper;
    @Resource
    private DingTalkMsgSender dingTalkMsgSender;
    @Lazy
    @Resource StockTaskService stockTaskService;

    @Resource
    PriceService priceService;
    @Resource
    TmsDeliveryRuleFacade tmsDeliveryRuleFacade;
    @Resource
    private StockTaskOrderSkuService stockTaskOrderSkuService;
    @Resource
    private WarehouseConfigService warehouseConfigService;
    @Resource
    private StockTaskOrderSkuRepository stockTaskOrderSkuRepository;
    @Resource
    private StockTaskOrderCancelService stockTaskOrderCancelService;
    @Resource
    DeliverPlanRemarkSnapshotService deliverPlanRemarkSnapshotService;
    @Resource
    private OfcSampleApplyQueryFacade sampleApplyQueryFacade;
    @Resource
    private AreaStoreFacade areaStoreFacade;
    @Resource
    private OfcQueryFacade ofcQueryFacade;

    private static final Logger logger = LoggerFactory.getLogger(SampleApplyService.class);
    /**
     * 数字7
     */
    private static final Integer SEVEN_INTEGER = 7;

    /**
     * status字段
     */
    private static final String STATUS = "status";

    /**
     * 主键id
     */
    private static final String SAMPLE_ID = "sample_id";

    /**
     * 取消状态
     */
    private static final Integer CANCEL_STATUS = 2;


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult insertSampleApply(SampleApplyVO samleApplyVO) {
        // 判断是否切仓
        Merchant merchant = merchantMapper.selectByPrimaryKey(samleApplyVO.getMId());
        Integer areaNo = merchant.getAreaNo();
        Area area = areaMapper.selectByAreaNo(areaNo);
        if(areaService.inChange(areaNo,  null, merchant.getmId())){
            return AjaxResult.getError("切仓中，该功能暂时无法使用");
        }
        List<SampleSku> sampleSkuList = samleApplyVO.getSampleSkuList();
        // check库存
        StringBuffer checkSampleStock = this.checkSampleStock(sampleSkuList, samleApplyVO.getContactId());
        if(checkSampleStock.length() > 0){
            return AjaxResult.getError(ResultConstant.DEFAULT_FAILED,checkSampleStock.append("无可用库存").toString());
        }
        // 样品申请数据同步至数据库
        samleApplyVO.setAddTime(new Date());
        samleApplyVO.setUpdateTime(new Date());
        samleApplyVO.setStatus(3);
        samleApplyVO.setBdName(baseService.getAdminName());
        samleApplyVO.setBdId(baseService.getAdminId());
        samleApplyVO.setPurchaseIntention(0);
        samleApplyVO.setSatisfaction(0);
        samleApplyVO.setGrade(merchant.getGrade());
        samleApplyVO.setCreateId(baseService.getAdminId());
        samleApplyVO.setCreateName(baseService.getAdminName());
        samleApplyVO.setMSize(merchant.getSize());
        samleApplyVO.setMName(merchant.getMname());
        samleApplyVO.setAreaName(area.getAreaName());
        Contact contact = contactMapper.selectByPrimaryKey(Long.valueOf(samleApplyVO.getContactId()));
        Integer storeNo = contact.getStoreNo();
        samleApplyVO.setStoreNo(storeNo);
        //samleApplyVO.setDeliveryTime(getDeliveryDate(area,merchant,null,(long)samleApplyVO.getContactId()));
        /*LocalDate deliveryDate = tmsDeliveryRuleFacade.queryCloudDeliveryDate(DeliveryRuleQueryInput.builder()
                .merchantId(merchant.getmId())
                .orderTime(LocalDateTime.now())
                .contactId((long) samleApplyVO.getContactId())
                .source(SourceEnum.XM_SAMPLE_APPLY)
                .build());*/
        //改成调用ofc获取配送日期
        List<String> skuList = sampleSkuList.stream().map(SampleSku::getSku).collect(Collectors.toList());
        DeliveryDateQueryResp dateQueryResp = ofcQueryFacade.queryDeliveryDate(LocalDateTime.now(),contact.getmId(),contact.getContactId(),null,null, OfcOrderSourceEnum.XM_SAMPLE_APPLY, skuList);
        samleApplyVO.setDeliveryTime(dateQueryResp.getDeliveryDate());
        sampleApplyMapper.insertSampleApply(samleApplyVO);
        // 样品申请 商品信息同步至数据库
        sampleSkuList.forEach(sampleSku -> {
            InventoryVO inventoryVO = inventoryMapper.selectSkuType(sampleSku.getSku());
            sampleSku.setSampleId(samleApplyVO.getSampleId());
            sampleSku.setPdName(inventoryVO.getPdName());
        });

        //创建样品申请的快照
        deliverPlanRemarkSnapshotService.addSampleApplySnapshot(contact, samleApplyVO);
        sampleSkuMapper.insertSampleSku(sampleSkuList);
        return AjaxResult.getOK();
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult cancelSampleApply(int sampleId) {
        // 参数校验
        SampleApply sampleApply = sampleApplyMapper.selectSampleById(sampleId);
        List<SampleSku> sampleSkuList = sampleSkuMapper.selectBySampleId(sampleId);
        if (CollectionUtils.isEmpty(sampleSkuList) || Objects.isNull(sampleApply)) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }

        // 过配送日期前一天20：15无法取消
        if (LocalDate.now().isAfter(sampleApply.getDeliveryTime().minusDays(NumberUtils.INTEGER_ONE))
                || (LocalDate.now()).equals(sampleApply.getDeliveryTime().minusDays(NumberUtils.INTEGER_ONE)) && !LocalTime.now().isBefore(LocalTime.of(20, 15, 00))) {
            return AjaxResult.getErrorWithMsg("样品出库任务已生成无法取消");
        }

        //自提样品单不支持取消
        if (sampleApplyQueryFacade.hasSampleApplySelfPicked(sampleId)){
            return AjaxResult.getErrorWithMsg("样品单已经自提，无法取消");
        }

        // 获取提前截单仓库存信息
        /*Contact contact = contactMapper.selectByPrimaryKey(Long.valueOf(sampleApply.getContactId()));
        Integer storeNo = contact.getStoreNo();*/
        // 待反馈状态可直接取消
        if (!Objects.equals(SampleApplyStatusEnum.REVIEWING.getId(),sampleApply.getStatus())) {
            if (!Objects.equals(SampleApplyStatusEnum.WAIT_HANDLE.getId(),sampleApply.getStatus())) {
                return AjaxResult.getErrorWithMsg("只有待审核或待反馈状态申请才能取消");
            }
        }
        // 取消样品申请
        sampleApplyMapper.cancelSampleApply(sampleId);
        // 判断是否已审核，已审核则还原库存，未审核不需要还原
        if(Objects.nonNull(sampleApplyReviewMapper.isReview(sampleId,SampleApplyStatusEnum.WAIT_HANDLE.getId()))){
            // 恢复库存信息--库存释放老模型替换
            /*Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
            for (SampleSku sampleSku : sampleSkuList) {
                String sku = sampleSku.getSku();
                //加虚拟库存
                areaStoreService.updateOnlineStockByStoreNo(true, sampleSku.getAmount(), sampleSku.getSku(), storeNo,
                        SaleStockChangeTypeEnum.DEMO_CANCEL,null, recordMap,NumberUtils.INTEGER_ZERO);
                //减锁定库存
                areaStoreService.updateLockStockByStoreNo(-sampleSku.getAmount(), sampleSku.getSku(), storeNo,
                        SaleStockChangeTypeEnum.DEMO_CANCEL,null, recordMap);
                WarehouseInventoryMapping mapping = warehouseInventoryService.selectByUniqueIndex(storeNo, sku);
                if(!Objects.isNull(mapping)){
                    Integer warehouseNo = mapping.getWarehouseNo();
                    areaStoreService.updateAreaStoreStatus(warehouseNo, sampleSku.getSku());
                }
            }
            quantityChangeRecordService.insertRecord(recordMap);*/

            //库存释放 新模型
            AreaStoreUnLockReq areaStoreUnLockReq = new AreaStoreUnLockReq();
            areaStoreUnLockReq.setContactId(Long.valueOf(sampleApply.getContactId()));
            areaStoreUnLockReq.setOrderType(SaleStockChangeTypeEnum.DEMO_CANCEL.getTypeName());
            areaStoreUnLockReq.setOrderNo(String.valueOf(sampleId));
            areaStoreUnLockReq.setIdempotentNo(String.valueOf(sampleId));
            areaStoreUnLockReq.setOperatorNo(String.valueOf(sampleId));
            areaStoreUnLockReq.setMerchantId(sampleApply.getMId());
            areaStoreUnLockReq.setSource(SourceEnum.XM_SAMPLE_APPLY.getValue());
            List<OrderUnLockSkuDetailReqDTO> orderReleaseSkuDetailReqDTOS = new ArrayList<>(sampleSkuList.size());
            sampleSkuList.stream().forEach(e -> {
                OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
                orderUnLockSkuDetailReqDTO.setSkuCode(e.getSku());
                orderUnLockSkuDetailReqDTO.setReleaseQuantity(e.getAmount());
                orderReleaseSkuDetailReqDTOS.add(orderUnLockSkuDetailReqDTO);
            });
            areaStoreUnLockReq.setOrderReleaseSkuDetailReqDTOS(orderReleaseSkuDetailReqDTOS);
            areaStoreFacade.storeUnLock(areaStoreUnLockReq);
        }
        return AjaxResult.getOK();
    }

      @Override
    public AjaxResult updateSampleApply(SampleApply sampleApply) {
        // old sampleApply
        SampleApply querySample = sampleApplyMapper.selectSampleById(sampleApply.getSampleId());
        if(Objects.isNull(querySample)){
            return AjaxResult.getErrorWithMsg("样品申请不存在");
        }
        if (!Objects.equals(querySample.getStatus(), 0)) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT,"当前样品申请状态不可填写反馈");
        }
        sampleApply.setStatus(1);
        sampleApply.setUpdateTime(new Date());
        sampleApplyMapper.updateSampleApply(sampleApply);

        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectSampleApplyVO(int pageIndex, int pageSize, SampleApply sampleApply,
                                          String keyword) {

        boolean bd = baseService.isBD();
        if(bd){
            if(!(baseService.isSaleSA() || baseService.isAreaSA() || baseService.isSA())){
                sampleApply.setBdId(baseService.getAdminId());
            }
        }
        PageHelper.startPage(pageIndex, pageSize);
        List<SampleApplyVO> sampleApplyVOS = sampleApplyMapper.selectSampleApplies(sampleApply, keyword);
        for (SampleApplyVO sampleApplyVO : sampleApplyVOS) {
            Integer sampleId = sampleApplyVO.getSampleId();
            List<SampleSku> sampleSkus = sampleSkuMapper.selectBySampleId(sampleId);
            sampleApplyVO.setSampleSkuList(sampleSkus);
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(sampleApplyVOS));
    }

    @Override
    public void createStockDemo() {
        logger.info("开始生成出样出库任务");
        //查询所有配送仓
        List<WarehouseLogisticsCenterVO> centers = warehouseLogisticsService.selectLogisticsAll(WarehouseLogisticsConfigEnum.Status.VALID.ordinal());
        LocalDate deliveryDate = LocalDate.now().plusDays(1);
        for (WarehouseLogisticsCenterVO center : centers) {
            //过滤提前截单
            Integer storeNo = center.getStoreNo();
            List<WarehouseStorageCenter> storageCenters = warehouseStorageService.selectByStoreNo(storeNo);
            for (WarehouseStorageCenter storageCenter : storageCenters) {
                Integer warehouseNo = storageCenter.getWarehouseNo();

                // 精细化仓管理
                if (warehouseConfigService.openCabinetManagement(warehouseNo) ||
                        stockTaskWaveRepository.isExistWaveConfig(warehouseNo, storeNo)) {
                    Integer stockTaskId = this.cabinetManagementSampleCreateStockTask(
                            warehouseNo, storeNo, deliveryDate, false);
                    if(Objects.nonNull(stockTaskId)){
                        stockTaskService.sendCreateTrunk(Arrays.asList(stockTaskId));
                    }
                    continue;
                }

                //查询申请sku信息
                List<SampleSku> sampleSkus = sampleApplyMapper.selectByDeliveryTime(storeNo, deliveryDate, warehouseNo);
                //生成出样出库单
                Integer stockTaskId = createStockTask(sampleSkus, storeNo, warehouseNo, SaleOutTypeEnum.ACCROSS_STORE, false);
                if(Objects.nonNull(stockTaskId)){
                    stockTaskService.sendCreateTrunk(Arrays.asList(stockTaskId));
                }
            }
        }
        logger.info("生成出样出库任务结束");
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public Integer cabinetManagementSampleCreateStockTask(Integer warehouseNo, Integer storeNo, LocalDate deliveryDate, Boolean isWaveStockTask) {
        //查询申请sku信息
        List<SampleSku> sampleSkus = stockTaskWaveRepository.getSampleApplyItem(warehouseNo, deliveryDate, storeNo);

        Integer stockTaskId = null;

        List<String> orderNoList = sampleSkus.stream().map(s -> "" + s.getSampleId()).distinct().collect(Collectors.toList());
        // 订单号 + 配送日期 过滤expect_time
        List<String> existOrderNoList = stockTaskOrderSkuRepository.selectOutOrderNoByOutOrderNoList(
                warehouseNo, storeNo, net.summerfarm.common.util.DateUtil.formatYmdDate(deliveryDate), orderNoList,
                StoreRecordType.DEMO_OUT.getId());
        if (!CollectionUtils.isEmpty(orderNoList)) {
            List<SampleSku> notCreateTaskOrderItems = sampleSkus.stream()
                    .filter(orderItem -> !existOrderNoList.contains("" + orderItem.getSampleId()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(notCreateTaskOrderItems)) {
                stockTaskId = createStockTask(notCreateTaskOrderItems, storeNo, warehouseNo, SaleOutTypeEnum.ACCROSS_STORE, isWaveStockTask);

                // todo zzq 异步勾起指定单据锁库
            }
        }

        // 拉取当天已取消的订单明细数据，生成多出返库入库
        stockTaskOrderCancelService.handleCancelSampleOrderReturnInbound(
                warehouseNo,
                storeNo,
                deliveryDate,
                sampleSkus,
                StoreRecordType.DEMO_OUT.getId()
                );

        return stockTaskId;
    }

    /*@Override
    public LocalDate getDeliveryDate(Area area,Merchant merchant,LocalDateTime startTime,Long contactId) {

        //计算配送日期
        LocalDate defaultDeliveryDate;

        if(Objects.isNull(startTime)){
            startTime = getStartTime(merchant);
            defaultDeliveryDate = startTime.plusDays(2).toLocalDate();
        }else {
            defaultDeliveryDate = startTime.toLocalDate();
        }
        Contact contact = contactMapper.selectByPrimaryKey(contactId);
        Integer[] frequent = fenceService.selectDeliveryFrequentByFence(contact);
        if (frequent == null || frequent.length == 0) {
            throw new DefaultServiceException(0, "该城市暂未开放");
        }
        //计算配送日期
        if (!Objects.equals(0, frequent[0])) {
            int dayOfWeek = defaultDeliveryDate.getDayOfWeek().getValue();
            Boolean flag = true;
            for (Integer deliveryDay : frequent) {
                if (dayOfWeek <= deliveryDay) {
                    defaultDeliveryDate = defaultDeliveryDate.plusDays(deliveryDay - dayOfWeek);
                    flag = false;
                    break;
                }
            }
            if (flag) {
                defaultDeliveryDate = defaultDeliveryDate.plusDays(7 - dayOfWeek + frequent[0]);
            }
        }
        //根据地址配送仓判断是否有停运信息
        TmsStopDelivery tmsStopDelivery = tmsStopDeliveryMapper.selectByStoreNo(contact.getStoreNo());
        //无停运信息直接返回
        if (Objects.isNull(tmsStopDelivery)) {
            return defaultDeliveryDate;
        }
        //如果配送时间在停运时间内,调整配送时间
        if (!defaultDeliveryDate.isBefore(tmsStopDelivery.getShutdownStartTime()) && !defaultDeliveryDate.isAfter(tmsStopDelivery.getShutdownEndTime())) {
            //如果是每天配送，直接在提运结束时间后一天配送
            if(Objects.equals(NumberUtils.INTEGER_ZERO, frequent[NumberUtils.INTEGER_ZERO])){
                return tmsStopDelivery.getShutdownEndTime().plusDays(NumberUtils.LONG_ONE);
            }
            //如果非每天配送，默认取提运结束时间后一天配送，判断是否在配送周期内,不在周期内,按照周期添加
            if(!Objects.equals(NumberUtils.INTEGER_ZERO,frequent[NumberUtils.INTEGER_ZERO])){
                LocalDate deliveryDate = tmsStopDelivery.getShutdownEndTime().plusDays(NumberUtils.LONG_ONE);
                int dayOfWeek = deliveryDate.getDayOfWeek().getValue();
                boolean flag = true;
                //循环配送周期
                for (Integer deliveryDay : frequent) {
                    //如果配送日期小于配送周期
                    if (dayOfWeek <= deliveryDay) {
                        //配送日期加上配送周期-配送日期的天数
                        defaultDeliveryDate = deliveryDate.plusDays(deliveryDay - dayOfWeek);
                        flag = false;
                        break;
                    }
                }
                //配送日期大于配送周期,取下个周期的日期
                if (flag) {
                    defaultDeliveryDate = deliveryDate.plusDays( SEVEN_INTEGER - dayOfWeek + frequent[NumberUtils.INTEGER_ZERO]);
                }
                return defaultDeliveryDate;
            }
        }
        return defaultDeliveryDate;
    }
*/

    /**
     *  判断样品申请中的商品信息是否满足配送条件、在样品池、有库存信息、有可用库存
     * @param sampleSkuList
     * @param contactId
     * @return
     */
    @Override
    public StringBuffer checkSampleStock(List<SampleSku> sampleSkuList,Integer contactId){
        // 获取仓库编号
        Contact contact = contactMapper.selectByPrimaryKey(Long.valueOf(contactId));
        Integer storeNo = contact.getStoreNo();
        // 判断库存信息，用于拼接库存不足状态的sku
        StringBuffer skuBuf = new StringBuffer();
        boolean flag = false;

        //改用新模型获取库存信息
        List<String> skuList = sampleSkuList.stream().map(SampleSku::getSku).collect(Collectors.toList());
        AreaStoreQueryReq areaStoreQueryReq = new AreaStoreQueryReq();
        areaStoreQueryReq.setContactId(contactId.longValue());
        areaStoreQueryReq.setSkuCodeList(skuList);
        Map<String, AreaStoreQueryRes> storeQueryResMap = areaStoreFacade.getInfo(areaStoreQueryReq);
        if (storeQueryResMap == null || storeQueryResMap.isEmpty()) {
            throw new BizException("库存信息获取异常");
        }

        // 判断库存信息
        for (SampleSku sampleSku : sampleSkuList) {
            String sku = sampleSku.getSku();
            // 是否在样品池，0不在，1在
            InventoryVO inventoryVO = inventoryMapper.selectSkuType(sku);
            Integer samplePool = inventoryVO.getSamplePool();
            if(Objects.equals(samplePool,0)){
                logger.info(sku+"不在样品池中");
                flag = true;
            }

            //获取库存使用仓--去掉老模型
            /*WarehouseInventoryMapping mapping = warehouseInventoryService.selectByUniqueIndex(storeNo, sku);
            AreaStore areaStore = new AreaStore();
            areaStore.setAreaNo(mapping.getWarehouseNo());
            areaStore.setSku(sku);
            AreaStore queryStore = areaStoreMapper.selectWithOutDataPermission(areaStore);*/

            AreaStoreQueryRes queryStore = storeQueryResMap.get(sku);
            if(Objects.isNull(queryStore)){
                logger.info(sku+"无库存信息");
                flag = true;
            }
            if(queryStore.getOnlineQuantity() - sampleSku.getAmount() < 0){
                logger.info(sku+"无可用库存");
                flag = true;
            }
            // 有异常库存信息，拼接sku
            if(flag){
                skuBuf.append(sku).append(";");
                flag = false;
            }
        }
        return skuBuf;
    }

    /**
     * 生成出样出库任务
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer createStockTask(List<SampleSku> sampleSkus, Integer storeNo, Integer warehouseNo, SaleOutTypeEnum outType, Boolean isWaveStockTask){

        if(CollectionUtils.isEmpty(sampleSkus)){
            return null;
        }
        //生成出样出库任务
        StockTask stockTask = new StockTask();
        stockTask.setAreaNo(warehouseNo);
        stockTask.setAddtime(LocalDateTime.now());
        stockTask.setType(StoreRecordType.DEMO_OUT.getId());
        stockTask.setExpectTime(LocalDate.now().plusDays(1).atTime(0, 0));
        stockTask.setState(StockTaskState.WAIT_IN_OUT.getId());
        stockTask.setOutStoreNo(storeNo);
        stockTask.setOutType(outType.ordinal());
        if (isWaveStockTask != null && isWaveStockTask) {
            stockTask.setOptionFlag(OptionFlagUtil.setValueEffective(
                    0L, OptionFlagTypeEnum.WAVE_STOCK_TASK.getCode()));
        }
        stockTaskMapper.insert(stockTask);
        //生成任务单详情
        List<StockTaskItem> stockTaskItems = new ArrayList<>();
        sampleSkus.stream().collect(Collectors.groupingBy(SampleSku::getSku)).forEach((sku,sampleSkuList) ->{
            StockTaskItem stockTaskItem = new StockTaskItem();
            stockTaskItem.setSku(sku);
            stockTaskItem.setActualQuantity(0);
            stockTaskItem.setStockTaskId(stockTask.getId());
            int sum = sampleSkuList.stream().mapToInt(SampleSku::getAmount).sum();
            stockTaskItem.setQuantity(sum);
            stockTaskItems.add(stockTaskItem);
//            try {
                Map<Integer, List<SampleSku>> sampleMap = sampleSkuList.stream().collect(Collectors.groupingBy(SampleSku::getSampleId));
                sampleMap.forEach((sampleId, skuList) -> {
                    StockTaskOrderSku orderSku = StockTaskOrderSku.builder()
                            .stockTaskId(stockTask.getId().longValue())
                            .outOrderNo(sampleId.toString())
                            .sku(sku)
                            .quantity(skuList.stream().mapToInt(SampleSku::getAmount).sum())
                            .actualQuantity(NumberUtils.INTEGER_ZERO)
                            .creator("系统-样品单")
                            .operator("系统-样品单").build();
                    stockTaskOrderSkuService.insertStockTaskOrderSku(orderSku);
                });
//            } catch (Exception e) {
//                logger.error("出样出库任务订单sku明细生成失败 sampleSkuList:{}", JSONUtil.toJsonStr(sampleSkuList), e);
//            }
        });
        stockTaskItemMapper.insertBatch(stockTaskItems);
        return stockTask.getId();
    }

    //提前截单仓为20:00
    private LocalDateTime getStartTime(Merchant merchant){
        LocalDateTime startTime = Global.getSampleStartTime();
        //大客户提前截单
        if(merchant != null && Objects.equals(merchant.getSize(),Global.BIG_MERCHANT)){
            Integer adminId = merchant.getAdminId();
            Admin admin = adminMapper.selectByPrimaryKey(adminId);
            if(admin != null && Objects.equals(admin.getCloseOrderType(),Global.CLOSE_ORDER_TYPE)){
                String closeOrderTime = admin.getCloseOrderTime();
                LocalTime parse = LocalTime.parse(closeOrderTime, DateTimeFormatter.ofPattern("HH:mm:ss"));
                LocalDateTime bigMerchantDeliveryTime = LocalDateTime.of(LocalDateTime.now().toLocalDate(), parse);
                startTime = LocalDateTime.now().isBefore(bigMerchantDeliveryTime) ? bigMerchantDeliveryTime.minusDays(1) : bigMerchantDeliveryTime;
            }
        }
        return startTime;
    }

    @Override
    public void sendSaveMessage(Integer id) {
        SampleApply sampleApply = sampleApplyMapper.selectSampleById(id);
        if (Objects.isNull(sampleApply)) {
            return;
        }
        Merchant merchant = merchantMapper.selectByPrimaryKey(sampleApply.getMId());
        Integer areaNo = merchant.getAreaNo();
        Area area = areaMapper.selectByAreaNo(areaNo);
        // 准备钉钉消息内容
        Config config = configMapper.selectOne("sampleRobotUrl");
        HashMap<String, String> msgMap = new HashMap<>(NumberUtils.INTEGER_TWO);
        // 拼接title
        StringBuffer title = new StringBuffer(sampleApply.getCreateName());
        title.append("提交了").append(sampleApply.getSampleId()).append("号样品申请单，请审核");
        msgMap.put("title",title.toString());
        // 拼接内容
        StringBuilder sb = new StringBuilder("#### " + title + "\n");
        sb.append("> ").append("###### ").append("详细说明:");
        List<SampleSku> sampleSkuList = sampleSkuMapper.selectBySampleId(id);
        sampleSkuList.forEach( sampleSku -> {
            AreaSku areaSku = new AreaSku();
            areaSku.setAreaNo(areaNo);
            areaSku.setSku(sampleSku.getSku());
            AreaSku querySku = areaSkuMapper.selectOne(areaSku);
            sb.append(sampleSku.getPdName())
                    .append("-")
                    .append(sampleSku.getWeight())
                    .append("-")
                    .append(sampleSku.getAmount())
                    .append("-");
            if(querySku != null){
                PriceInfoBO priceInfoBO = priceService.getNormalPrice(querySku);
                sb.append(priceInfoBO.getPrice().multiply(BigDecimal.valueOf(sampleSku.getAmount())));
            }
            sb.append("\n");
        });
        sb.append(sampleApply.getMName())
                .append("-V")
                .append(sampleApply.getGrade() == null ? 0 : sampleApply.getGrade())
                .append("-")
                .append(sampleApply.getMSize())
                .append("-")
                .append(area.getAreaName());
        msgMap.put("text",sb.toString());
        // 发送钉钉消息
        DingTalkRobotUtil.sendMarkDownMsg(config.getValue(), () -> msgMap, null);
    }

    @Override
    public void sendCancelMessage(DtsModel dtsModel) {
        List<Map<String, String>> oldDataList = dtsModel.getOld();
        for (int i = 0; i < oldDataList.size(); i++) {
            String id = DtsUtils.searchChangeId(dtsModel, i, STATUS, SAMPLE_ID);
            if (Objects.isNull(id)) {
                continue;
            }
            Integer sampleId = Integer.valueOf(id);
            SampleApply sampleApply = sampleApplyMapper.selectSampleById(sampleId);
            if (Objects.isNull(sampleApply)) {
                return;
            }
            if (!Objects.equals(sampleApply.getStatus(), CANCEL_STATUS)) {
                return;
            }
            List<SampleSku> sampleSkuList = sampleSkuMapper.selectBySampleId(sampleId);
            // 准备钉钉消息内容
            String url = configMapper.selectOne("sampleRobotUrl").getValue();
            HashMap<String, String> msgMap = new HashMap<>(NumberUtils.INTEGER_TWO);
            // title
            StringBuffer title = new StringBuffer(sampleApply.getCreateName());
            title.append("取消了").append(sampleApply.getSampleId()).append("号样品申请单");
            msgMap.put("title",title.toString());
            // 拼接内容
            StringBuilder sb = new StringBuilder("#### " + title + "\n");
            sb.append("> ").append("###### ").append("详细说明:");
            sampleSkuList.forEach(sampleSku -> {
                AreaSku areaSku = new AreaSku();
                areaSku.setAreaNo(sampleApply.getAreaNo());
                areaSku.setSku(sampleSku.getSku());
                AreaSku querySku = areaSkuMapper.selectOne(areaSku);
                sb.append(sampleSku.getPdName())
                        .append("-")
                        .append(sampleSku.getWeight())
                        .append("-")
                        .append(sampleSku.getAmount())
                        .append("-");
                if(querySku != null){
                    PriceInfoBO priceInfo = priceService.getNormalPrice(querySku);
                    sb.append(priceInfo.getPrice().multiply(BigDecimal.valueOf(sampleSku.getAmount())));
                }
                sb.append("\n");
            });

            String areaName = areaMapper.selectByAreaNo(sampleApply.getAreaNo()).getAreaName();
            sb.append(sampleApply.getMName())
                    .append("-V")
                    .append(sampleApply.getGrade() == null ? 0 : sampleApply.getGrade())
                    .append("-")
                    .append(sampleApply.getMSize())
                    .append("-")
                    .append(areaName);
            msgMap.put("text",sb.toString());
            // 发送钉钉消息
            DingTalkRobotUtil.sendMarkDownMsg(url, () -> msgMap, null);
        }
    }

    @Override
    public AjaxResult checkSampleApplyReviewLegitimacy(SampleApplyVO sampleApplyVO) {
        if(Objects.isNull(sampleApplyVO)){
            return AjaxResult.getErrorWithMsg("请联系管理员并问问他,为啥接口不传参?");
        }

        // 获取参数
        Long mId = sampleApplyVO.getMId();
        Integer contactId = sampleApplyVO.getContactId();
        List<SampleSku> sampleSkuList = sampleApplyVO.getSampleSkuList();

        // 申请id不为null时,为审核校验
        if(Objects.nonNull(sampleApplyVO.getSampleId())){
            SampleApply sampleApply = sampleApplyMapper.selectSampleById(sampleApplyVO.getSampleId());
            mId = sampleApply.getMId();
            contactId = sampleApply.getContactId();
            sampleSkuList = sampleSkuMapper.selectBySampleId(sampleApplyVO.getSampleId());
        }

        // 判断是否正在切仓
        Merchant merchant = merchantMapper.selectByPrimaryKey(mId);
        Integer areaNo = merchant.getAreaNo();
        if(areaService.inChange(areaNo,  null, merchant.getmId())){
            return AjaxResult.getErrorWithMsg("切仓中，该功能暂时无法使用");
        }

        // 检查库存信息
        StringBuffer checkSampleStock = this.checkSampleStock(sampleSkuList, contactId);
        if(checkSampleStock.length() > 0){
            return AjaxResult.getError(ResultConstant.DEFAULT_FAILED,checkSampleStock.append("无可用库存").toString());
        }

        // 返回关键信息
        Contact contact = contactMapper.selectByPrimaryKey(Long.valueOf(contactId));
        Integer storeNo = contact.getStoreNo();
        sampleApplyVO.setStoreNo(storeNo);
        Area area = areaMapper.selectByAreaNo(areaNo);
        sampleApplyVO.setAreaName(area.getAreaName());
        //LocalDate deliveryDate = getDeliveryDate(area, merchant, null, (long) contactId);
        LocalDate deliveryDate = tmsDeliveryRuleFacade.queryCloudDeliveryDate(DeliveryRuleQueryInput.builder()
                .merchantId(merchant.getmId())
                .orderTime(LocalDateTime.now())
                .contactId((long) contactId)
                .source(SourceEnum.XM_SAMPLE_APPLY)
                .city(contact.getCity())
                .area(contact.getArea())
                .poi(contact.getPoiNote())
                .build());
        sampleApplyVO.setDeliveryTime(deliveryDate);

        return AjaxResult.getOK(sampleApplyVO);
    }

    @Override
    public void updateSampleApplyAreaNo(Integer sampleId, Integer oldAreaNo, Integer newAreaNo) {
        SampleApply sampleApply = sampleApplyMapper.selectSampleById(sampleId);
        if (sampleApply == null) {
            logger.error("切仓样品单不存在，sampleId：{}", sampleId);
            return;
        }
        sampleApplyMapper.updateSampleApplyArea(sampleApply.getSampleId(), newAreaNo);
    }
}
