package net.summerfarm.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.NumberUtils;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.config.DynamicConfig;
import net.summerfarm.contexts.Global;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.ConfigValueEnum;
import net.summerfarm.enums.CrmBdConfigTypeEnum;
import net.summerfarm.enums.DataSynchronizationInformationEnum;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.repository.CrmBdConfigRepository;
import net.summerfarm.mapper.offline.CrmBdMonthGmvMapper;
import net.summerfarm.mapper.offline.DataSynchronizationInformationMapper;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.input.*;
import net.summerfarm.model.vo.*;
import net.summerfarm.service.ChangeRecordService;
import net.summerfarm.service.CommissionService;
import net.summerfarm.service.FollowUpRelationService;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CommissionServiceImpl extends BaseService implements CommissionService {

    @Resource
    ConfigMapper configMapper;

    @Resource
    CrmCommissionSkuMapper crmCommissionSkuMapper;

    @Resource
    CrmCommissionMerchantMapper crmCommissionMerchantMapper;

    @Resource
    CrmBdConfigMapper crmBdConfigMapper;

    @Resource
    CrmBdAreaMapper crmBdAreaMapper;

    @Resource
    ChangeRecordService changeRecordService;

    @Resource
    InventoryMapper inventoryMapper;
    @Resource
    private OrderItemMapper orderItemMapper;

    @Resource
    FollowUpRelationMapper followUpRelationMapper;

    @Resource
    private AreaMapper areaMapper;

    @Resource
    private FollowUpRelationService followUpRelationService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private DataSynchronizationInformationMapper dataSynchronizationInformationMapper;

    @Resource
    private CrmBdMonthGmvMapper crmBdMonthGmvMapper;

    @Resource
    private AdCodeMsgMapper adCodeMsgMapper;

    @Resource
    private CrmCommissionCategoryMapper crmCommissionCategoryMapper;
    @Resource
    private CrmCommissionMerchantLevelMapper crmCommissionMerchantLevelMapper;
    @Resource
    private CrmCommissionCoreMerchantMapper crmCommissionCoreMerchantMapper;
    @Resource
    private CrmBdConfigRepository crmBdConfigRepository;

    @Resource
    private DynamicConfig dynamicConfig;

    private final static String NEW_AREA_MONTH = "new_area_month";

    /**
     * 新城市新销售定义查询
     * @return
     */
    @Override
    public AjaxResult selectAreaBd() {
        Config newAreaMonth = configMapper.selectOne("new_area_month");
        Config newBdMonth = configMapper.selectOne("new_bd_month");
        HashMap<String, Config> info = new HashMap<>();
        info.put("newAreaMonth",newAreaMonth);
        info.put("newBdMonth",newBdMonth);
        return AjaxResult.getOK(info);
    }

    /**
     * 新城市新销售定义修改
     * @param config
     * @return
     */

    @Override
    public AjaxResult saveAreaBd(Config config) {
        redisTemplate.delete(NEW_AREA_MONTH);
        Config info = configMapper.selectOne(config.getKey());
        configMapper.update(config);
        String handleName = "";
        String oldValue ="";
        String newValue = "";
        if(Objects.equals(config.getKey(),"new_area_month")) {
            handleName ="新城市定义";
            oldValue = assemblyNewCity(info.getValue());
            newValue = assemblyNewCity(config.getValue());
        }

        if(Objects.equals(config.getKey(),"new_bd_month")){
            handleName = "新销售定义";
            oldValue = assemblyNewArea(info.getValue());
            newValue = assemblyNewArea(config.getValue());
        }

        if(Objects.equals(config.getKey(),"gmv_target")) {
            handleName = "gmv部分目标";
            oldValue = assemblyGmvTarget(info.getValue());
            newValue = assemblyGmvTarget(config.getValue());
        }
        changeRecordService.insertRecord(info.getId(),oldValue,newValue,handleName,1);
        return AjaxResult.getOK();
    }

    private String assemblyGmvTarget(String value) {
        return "为"+value+"%";
    }

    private String assemblyNewCity(String value) {
        return "首配时间开始"+value+"个月内";
    }

    private String assemblyNewArea(String value) {
        return "账号配置时间开始"+value+"个月内";
    }

    @Override
    public AjaxResult selectsku(int pageIndex, int pageSize, String zoneName, String sku, String pdName) {
        PageHelper.startPage(pageIndex,pageSize);
        List<CrmCommissionSkuVo> info = crmCommissionSkuMapper.selectAll(zoneName,null,sku,pdName);
        PageInfo pageInfo = new PageInfo(info);
        return AjaxResult.getOK(pageInfo);
    }

    /**
     * 按件奖励删除
     * @param id
     * @return
     */

    @Override
    public AjaxResult deleteAreasku(int id) {
        List<CrmCommissionSkuVo> crmCommissionSku = crmCommissionSkuMapper.selectAll(null,id, null, null);
        crmCommissionSkuMapper.deleteByPrimaryKey(id);
        String oldValue = "区域名称："+crmCommissionSku.get(0).getZoneName()+"，sku编码/规格："+crmCommissionSku.get(0).getSku()+"/"+crmCommissionSku.get(0).getWeight()+"，商品名称："+crmCommissionSku.get(0).getPdName()+"，单件奖励金额："+crmCommissionSku.get(0).getReward()+"元";
        changeRecordService.insertRecord(id,oldValue,null,"按件奖励",2);
        return AjaxResult.getOK();
    }



    /**
     * 按件奖励新增
     * @param crmCommissionSkuVo
     * @return
     */
    @Override
    @Transactional
    public AjaxResult saveAreaSku(CrmCommissionSkuVo crmCommissionSkuVo) {

        int existCrm = crmCommissionSkuMapper.exist(crmCommissionSkuVo);
        if(existCrm >0){
            return AjaxResult.getError("SKU_EXIST","sku:"+crmCommissionSkuVo.getSku()+"已存在，不可重复提交");
        }
        CrmCommissionSku crmCommissionSku = new CrmCommissionSku();
        BeanUtils.copyProperties(crmCommissionSkuVo,crmCommissionSku);
        //获取当前用户ID
        Integer adminId = getAdminId();
        crmCommissionSku.setUpdater(adminId);
        //根据sku取规格
        OrderItem orderItem = orderItemMapper.selectSkuInfosBySku(crmCommissionSkuVo.getSku());

        String newValue = "区域名称："+crmCommissionSkuVo.getZoneName()+"，sku编码/规格："+crmCommissionSkuVo.getSku()+"/"+orderItem.getWeight()+"，商品名称："+crmCommissionSkuVo.getPdName()+"，单件奖励金额："+crmCommissionSkuVo.getReward()+"元";
        //ID为空时，新增
        if(crmCommissionSkuVo.getId()==null){
            crmCommissionSku.setCreator(adminId);
            crmCommissionSkuMapper.insert(crmCommissionSku);
            changeRecordService.insertRecord(crmCommissionSku.getId(),null,newValue,"按件奖励",0);
        }else{  //ID不为空时，编辑
            List<CrmCommissionSkuVo> crmCommissionSkuInfo = crmCommissionSkuMapper.selectAll(null,crmCommissionSkuVo.getId(), null, null);
            crmCommissionSkuMapper.updateByPrimaryKey(crmCommissionSku);
            String oldValue = "区域名称："+crmCommissionSkuInfo.get(0).getZoneName()+"，sku编码/规格："+crmCommissionSkuInfo.get(0).getSku()+"/"+crmCommissionSkuInfo.get(0).getWeight()+"，商品名称："+crmCommissionSkuInfo.get(0).getPdName()+"，单件奖励金额："+crmCommissionSkuInfo.get(0).getReward()+"元";
            changeRecordService.insertRecord(crmCommissionSku.getId(),oldValue,newValue,"按件奖励",1);
        }
        return AjaxResult.getOK();
    }

    /**
     * 按件奖励复制
     * @param copyInfoInput
     * @return
     */
    @Transactional
    @Override
    public AjaxResult copyAreaSku(CopyInfoInput copyInfoInput) {
        //取当前用户
        Integer adminId = getAdminId();
        //被复制按件奖励信息
        List<String> exist = new ArrayList<>();

        exist = crmCommissionSkuMapper.isExistAreaSkuInfo(copyInfoInput);
        if(!exist.isEmpty()){
            return AjaxResult.getError("SKU_EXIST", "sku:"+StringUtils.join(exist,",")+"已存在，不可重复提交");
        }
        List<CrmCommissionSkuVo> crmCommissionSkuInfo = crmCommissionSkuMapper.selectAll(copyInfoInput.getCopyInfo(),null, null, null);
        List<String> areaSkuInfo = copyInfoInput.getInfo();
        for (String skuInfo : areaSkuInfo) {
            crmCommissionSkuMapper.copyAreaSku(copyInfoInput.getCopyInfo(),skuInfo,adminId);
            for (CrmCommissionSkuVo crmCommissionSkuVo : crmCommissionSkuInfo) {
                String newValue = "区域名称："+skuInfo+",sku编码/规格："+crmCommissionSkuInfo.get(0).getSku()+"/"+crmCommissionSkuInfo.get(0).getWeight()+"，商品名称："+crmCommissionSkuVo.getPdName()+"，单件奖励金额："+crmCommissionSkuVo.getReward()+"元";
                changeRecordService.insertRecord(null,null,newValue,"按件奖励",0);
            }
        }

        return AjaxResult.getOK();
    }

    /**
     * 拉新奖励查询
     * @param pageIndex
     * @param pageSize
     * @param zoneName
     * @return
     */
    @Override
    public AjaxResult selectMerchant(int pageIndex, int pageSize, String zoneName) {
        PageHelper.startPage(pageIndex,pageSize);
        List<CrmCommissionMerchantVo> info =  crmCommissionMerchantMapper.selectMerchant(zoneName);
        PageInfo pageInfo= new PageInfo(info);
        return AjaxResult.getOK(pageInfo);
    }

    /**
     * 拉新奖励删除
     * @param id
     * @return
     */
    @Override
    @Transactional
    public AjaxResult deleteMerchant(int id) {
        CrmCommissionMerchant crmCommissionMerchant = crmCommissionMerchantMapper.selectByPrimaryKey(id);
        crmCommissionMerchantMapper.deleteByPrimaryKey(id);
        String oldValue = assemblyMerchantRecordValue(crmCommissionMerchant);
        changeRecordService.insertRecord(id,oldValue,null,"按拉新奖励",2);
        return AjaxResult.getOK();
    }



    /**
     * 拉新奖励保存
     * @param crmCommissionMerchantVo
     * @return
     */
    @Override
    @Transactional
    public AjaxResult saveMerchant(CrmCommissionMerchantVo crmCommissionMerchantVo) {
        Integer adminId = getAdminId();
        CrmCommissionMerchant crmCommissionMerchant = new CrmCommissionMerchant();
        BeanUtils.copyProperties(crmCommissionMerchantVo,crmCommissionMerchant);
        crmCommissionMerchant.setUpdater(adminId);
        //新增
        if(crmCommissionMerchantVo.getId()==null){
            crmCommissionMerchant.setCreator(adminId);
            crmCommissionMerchantMapper.insert(crmCommissionMerchant);
            //插入记录
            changeRecordService.insertRecord(crmCommissionMerchant.getId(),null,assemblyMerchantRecordValue(crmCommissionMerchant),"按拉新奖励",0);

        }else{   //编辑
            //查原值
            CrmCommissionMerchant oldCrmCommissionMerchant = crmCommissionMerchantMapper.selectByPrimaryKey(crmCommissionMerchant.getId());
            crmCommissionMerchantMapper.updateByPrimaryKey(crmCommissionMerchant);
            changeRecordService.insertRecord(crmCommissionMerchant.getId(),assemblyMerchantRecordValue(oldCrmCommissionMerchant),assemblyMerchantRecordValue(crmCommissionMerchant),"按拉新奖励",1);
        }
        return AjaxResult.getOK();
    }

    /**
     * 按新奖励复制
     * @param copyInfoInput
     * @return
     */
    @Override
    @Transactional
    public AjaxResult copyMerchant(CopyInfoInput copyInfoInput) {
        Integer adminId = getAdminId();
        List<String> infos = copyInfoInput.getInfo();
        CrmCommissionMerchant crmCommissionMerchant = crmCommissionMerchantMapper.selectByZoneName(copyInfoInput.getCopyInfo());
        for (String info : infos) {
            crmCommissionMerchantMapper.copyMerchant(copyInfoInput.getCopyInfo(),info,adminId);
            crmCommissionMerchant.setZoneName(info);
            changeRecordService.insertRecord(null,null,assemblyMerchantRecordValue(crmCommissionMerchant),"按拉新奖励",0);
        }
        return AjaxResult.getOK();
    }

    @Override
    @Transactional
    public AjaxResult batchModifyMerchant(BatchModifyMerchantInput batchModifyMerchantInput) {
        batchModifyMerchantInput.setUpdater( getAdminId());
        batchModifyMerchantInput.setUpdateTime(LocalDateTime.now());
        //取原
        List<CrmCommissionMerchant> oldCrmCommissionMerchants = crmCommissionMerchantMapper.selectByPrimaryKeyList(batchModifyMerchantInput.getId());

        crmCommissionMerchantMapper.updateByPrimaryKeySelective(batchModifyMerchantInput);
        //取新
        List<CrmCommissionMerchant> newCrmCommissionMerchants = crmCommissionMerchantMapper.selectByPrimaryKeyList(batchModifyMerchantInput.getId());
        for (CrmCommissionMerchant newCrmCommissionMerchant : newCrmCommissionMerchants) {
            for (CrmCommissionMerchant oldCrmCommissionMerchant : oldCrmCommissionMerchants) {
                if(Objects.equals(newCrmCommissionMerchant.getId(),oldCrmCommissionMerchant.getId())){
                    changeRecordService.insertRecord(newCrmCommissionMerchant.getId(),assemblyMerchantRecordValue(oldCrmCommissionMerchant),
                            assemblyMerchantRecordValue(newCrmCommissionMerchant),"按拉新奖励",1);
                }
            }
        }
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectIncentiveIndex(int pageIndex, int pageSize, List<Integer> area,String adminName) {
        if(dynamicConfig.isManageCustomFenceSwitch()) {
            logger.error("该接口[adCodeMsgMapper.selectAndStopByCity]已下线，请联系管理员处理!");
            throw new BizException("该接口已下线，请联系管理员处理");
        }
        PageHelper.startPage(pageIndex,pageSize);
        List<CrmBdConfigVo> crmBdConfigVos = crmBdConfigMapper.selectIncentiveIndex(area, adminName);
        AdCodeMsg adCodeMsg = new AdCodeMsg();
        for (CrmBdConfigVo crmBdConfigVo : crmBdConfigVos) {
            // 销售负责运营区域
            List<MajorRebate> bdArea = crmBdConfigMapper.selectBdArea(crmBdConfigVo.getAdminId());
            crmBdConfigVo.setAreaCity(bdArea);
            // 销售所属行政城市(省+市)
            adCodeMsg.setCity(crmBdConfigVo.getAdministrativeCity());
            List<AdCodeMsg> adCodes = adCodeMsgMapper.selectAndStopByCity(adCodeMsg);
            if(!CollectionUtils.isEmpty(adCodes)){
                List<String> list = Arrays.asList(adCodes.get(0).getProvince(), crmBdConfigVo.getAdministrativeCity());
                crmBdConfigVo.setAdministrativeCitys(list);
            }
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(crmBdConfigVos));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteIncentiveIndex(int id) {
        CrmBdConfig crmBdConfig = crmBdConfigMapper.selectByPrimaryKey(id);
        if(Objects.isNull(crmBdConfig)){
            return AjaxResult.getErrorWithMsg("请不要重复删除!刷新一下试试看");
        }
        crmBdConfigMapper.deleteByPrimaryKey(id);
        crmBdAreaMapper.deleteByAdminId(crmBdConfig.getAdminId());
        FollowUpRelation followUpRelation = new FollowUpRelation();

        followUpRelation.setReassign(false);
        followUpRelation.setReason("主管分配");
        followUpRelation.setAdminId(crmBdConfig.getAdminId());
        followUpRelation.setReassignTime(LocalDateTime.now());
        //将该用户下的所有私海释放
        followUpRelation.setReassign(true);
        followUpRelationMapper.updateReassignByAdminId(followUpRelation);
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveIncentiveIndex(CrmBdConfigVo crmBdConfigVo) {
        if(dynamicConfig.isManageCustomFenceSwitch()) {
            logger.error("该接口[adCodeMsgMapper.selectAndStopByCity]已下线，请联系管理员处理!");
            throw new BizException("该接口已下线，请联系管理员处理");
        }
        // 具有权限的城市
        List<AreaResultVo> areaLists = areaMapper.selectAreaResult(null);
        List<Integer> bdPermissionArea = areaLists.stream().map(AreaResultVo::getAreaNo).collect(Collectors.toList());
        // 页面传值,城市
        List<Integer> inputArea = crmBdConfigVo.getAreaCityIds();
        Set<Integer> areaCityIds = new HashSet<>(inputArea);
        // 判断权限
        if(Objects.isNull(crmBdConfigVo.getId())){
            inputArea.removeAll(bdPermissionArea);
            if(!CollectionUtils.isEmpty(inputArea)){
              return  AjaxResult.getError(ResultConstant.UNAUTHORIZED,"你没有该城市权限，无法修改");
            }
        }else{
            List<Integer> bdArea = crmBdAreaMapper.selectByAdminId(crmBdConfigVo.getAdminId());
            inputArea.removeAll(bdArea);
            inputArea.removeAll(bdPermissionArea);
            if(!CollectionUtils.isEmpty(inputArea)){
               return AjaxResult.getError(ResultConstant.UNAUTHORIZED,"你没有该城市权限，无法修改");
            }
        }

        Integer adminId = super.getAdminId();
        try {
            // 获取所属城市
            List<String> city = crmBdConfigVo.getAdministrativeCitys();
            // 销售所属行政城市(省+市)
            AdCodeMsg adCodeMsg = new AdCodeMsg();
            adCodeMsg.setCity(city.get(1));
            List<AdCodeMsg> adCodes = adCodeMsgMapper.selectAndStopByCity(adCodeMsg);
            if(CollectionUtils.isEmpty(adCodes)){
                return AjaxResult.getErrorWithMsg( "所属行政城市不在围栏中,请更换城市或设置围栏!");
            }
            crmBdConfigVo.setAdministrativeCity(city.get(1));
        } catch (Exception e) {
            logger.error("新增销售归属城市错误:{}",e.getMessage());
            return AjaxResult.getErrorWithMsg("参数错误,请联系管理员解决!");
        }
        //如果type类型为2时，取上月核心客户数作为基础值
        if(Objects.equals(CrmBdConfigTypeEnum.LAST_MONTH.ordinal(),crmBdConfigVo.getType())){
            int coreNum = this.getCoreNum(crmBdConfigVo.getAdminId());
            crmBdConfigVo.setCoreMerchantAmount(coreNum);
        }
        CrmBdConfig crmBdConfig = new CrmBdConfig();
        BeanUtils.copyProperties(crmBdConfigVo,crmBdConfig);
        crmBdConfig.setUpdater(adminId);
        //新增
        if(Objects.isNull(crmBdConfigVo.getId())){
            CrmBdConfig bdConfig = crmBdConfigMapper.selectByAdminId(crmBdConfigVo.getAdminId());
            if(Objects.nonNull(bdConfig)){
                return AjaxResult.getErrorWithMsg("销售已存在激励指标,请勿重复添加");
            }
            crmBdConfig.setCreator(adminId);
            crmBdConfigMapper.insertSelective(crmBdConfig);
            // 批量插入城市信息
            crmBdAreaMapper.insertBatch(areaCityIds,crmBdConfigVo.getAdminId(),adminId);
        }else{
            List<Integer> info = crmBdAreaMapper.selectByAdminId(crmBdConfigVo.getAdminId());
            crmBdConfigMapper.updateByPrimaryKey(crmBdConfig);
            crmBdAreaMapper.deleteByAdminId(crmBdConfigVo.getAdminId());
            crmBdAreaMapper.insertBatch(areaCityIds,crmBdConfigVo.getAdminId(),adminId);
            //将删除的城市所关联的私海删除
            info.removeAll(areaCityIds);
            FollowUpRelation followUpRelation = new FollowUpRelation();
            followUpRelation.setReassign(true);
            followUpRelation.setReason("主管分配");
            followUpRelation.setAdminId(crmBdConfig.getAdminId());
            followUpRelation.setReassignTime(LocalDateTime.now());
            if(!info.isEmpty()){
                // 将bd被删除区域的私海客户释放
                followUpRelationMapper.updateReassignByAdminIdArea(crmBdConfigVo.getAdminId(),info,followUpRelation);
            }
        }
        return AjaxResult.getOK();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult copyIncentiveIndex(CopyIntInfoInput copyIntInfoInput) {
        // 权限验证
        List<Integer> bdArea = crmBdAreaMapper.selectByAdminId(copyIntInfoInput.getCopyIntInfo());
        List<AreaResultVo> areaLists = areaMapper.selectAreaResult(null);
        List<Integer> bdPermissionArea = areaLists.stream().map(AreaResultVo::getAreaNo).collect(Collectors.toList());
        bdArea.removeAll(bdPermissionArea);
        if(!bdArea.isEmpty()){
           return AjaxResult.getError(ResultConstant.UNAUTHORIZED,"你没有该城市权限，无法操作");
        }

        Integer adminId = getAdminId();
        Integer intInfos = copyIntInfoInput.getIntInfo();
        CrmBdConfig crmBdConfig = crmBdConfigMapper.selectByAdminId(copyIntInfoInput.getCopyIntInfo());
        if(Objects.isNull(crmBdConfig)){
            return AjaxResult.getErrorWithMsg("被复制人不存在激励指标,请为他添加后重试");
        }
        //如果被复制人插入时也是使用上月的数据,那新增销售也将取个人上月数据作为基础值
        if(Objects.equals(CrmBdConfigTypeEnum.LAST_MONTH.ordinal(),crmBdConfig.getType())){
            int coreNum = this.getCoreNum(intInfos);
            crmBdConfigMapper.copyIncentiveIndexLastMonthGmv(copyIntInfoInput.getCopyIntInfo(),intInfos,adminId,coreNum);
        }else{
            crmBdConfigMapper.copyIncentiveIndex(copyIntInfoInput.getCopyIntInfo(),intInfos,adminId);
        }
        // 复制城市信息
        crmBdAreaMapper.copyAreaByAdminId(copyIntInfoInput.getCopyIntInfo(),intInfos,adminId);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult batchModifyIncentiveIndex(BatchModifyIncentiveIndexInput batchModifyIncentiveIndexInput) {
        Integer type = batchModifyIncentiveIndexInput.getType();
        batchModifyIncentiveIndexInput.setUpdater(getAdminId());
        batchModifyIncentiveIndexInput.setUpdateTime(LocalDateTime.now());
        //如果type类型为2时，取上月核心客户数作为基础值
        if(Objects.equals(CrmBdConfigTypeEnum.LAST_MONTH.ordinal(),type)){
            List<Integer> adminIds = batchModifyIncentiveIndexInput.getId();
            for (Integer adminId : adminIds) {
                int coreNum = this.getCoreNum(adminId);
                batchModifyIncentiveIndexInput.setCoreMerchantAmount(coreNum);
                batchModifyIncentiveIndexInput.setAdminId(adminId);
                crmBdConfigMapper.updateByAdminId(batchModifyIncentiveIndexInput);
            }
        }else{
            crmBdConfigMapper.updateByPrimaryKeySelective(batchModifyIncentiveIndexInput);
        }
        return AjaxResult.getOK();
    }

    /**
     * 模糊查询表格中存在和不存在区域
     * @param isExist
     * @param flag
     * @return
     */
    @Override
    public AjaxResult queryZoneNameCondition(Boolean isExist, String flag,String zoneName) {
        String tableName = "";
        switch (flag){
            case "1" :
                tableName = "crm_commission_sku";
                break;
            case "2" :
                tableName = "crm_commission_ration";
                break;
            case "3" :
                tableName = "crm_commission_merchant";
                break;
        }
        if(tableName.isEmpty()){
            return AjaxResult.getError("FLAG_ERROR","flag错误");
        }
        List<String> zoneNameCondition = crmBdConfigMapper.queryZoneNameCondition(tableName,isExist,zoneName);
        return AjaxResult.getOK(zoneNameCondition);


    }

    /**
     * 模糊查询表格中存在和不存在名称
     * @param isExist
     * @param bdName
     * @return
     */
    @Override
    public AjaxResult queryBdName(Boolean isExist, String bdName) {
        Config crmAdminAuth = configMapper.selectOne("crm_admin_auth");
        List<Integer> roleIds = Arrays.asList(crmAdminAuth.getValue().split(Global.SEPARATING_SYMBOL)).stream().map(o -> Integer.valueOf(o)).collect(Collectors.toList());
        List<ValuesVo> info = crmBdConfigRepository.queryBdName(isExist, bdName, roleIds);
        return AjaxResult.getOK(info);
    }

    /**
     * 查询区域选择
     * @return
     */
    @Override
    public AjaxResult selectZoneNameList() {
        List<ZoneNameListVo> zoneNameListVos = crmBdConfigMapper.selectZoneNameList();
        for (ZoneNameListVo zoneNameListVo : zoneNameListVos) {
            List<ZoneNameListVo> info = crmBdConfigMapper.selectselectZoneNameChildrenList(zoneNameListVo.getId());
            zoneNameListVo.setChildren(info);
        }
        return AjaxResult.getOK(zoneNameListVos);
    }

    @Override
    public AjaxResult selectNumLastMonth(Integer adminId) {
        int coreNum = this.getCoreNum(adminId);
        return AjaxResult.getOK(coreNum);
    }

    @Override
    public AjaxResult selectWarehouse() {
        List<AreaResultVo> centerList = crmBdConfigMapper.selectLogisticsAreaResult();
        for (AreaResultVo el : centerList) {//查询城市信息 ok
            Area query = new Area();
            query.setLargeAreaNo(el.getAreaNo());
            //全部
            List<AreaResultVo> areaListVos = areaMapper.selectAreaResultVo(query);
            //带权限的城市
            List<AreaResultVo> areaLists = areaMapper.selectAreaResult(query);
            boolean b = areaListVos.removeAll(areaLists);
            if(b){
                el.setDisabled(false);
            }else {
                el.setDisabled(true);
            }
            for (AreaResultVo areaResultVo : areaListVos) {
                areaResultVo.setDisabled(true);
            }
            for (AreaResultVo areaList : areaLists) {
                areaList.setDisabled(false);
            }
            areaListVos.addAll(areaLists);
            el.setChildren(areaListVos);
        }
        return AjaxResult.getOK(centerList);
    }

    @Override
    public AjaxResult selectGmvTarget() {
        // 获取系数
        Config commissionCoefficient = configMapper.selectOne(Global.COMMISSION_COEFFICIENT);
        Config targetOfMonthLiving = configMapper.selectOne(Global.TARGET_MONTH_LIVING);
        Config targetOfGmv = configMapper.selectOne(Global.TARGET_GMV);
        // 返回值信息
        GmvTargetVO gmvTargetVO = new GmvTargetVO();
        gmvTargetVO.setCommissionCoefficient(commissionCoefficient.getValue());
        gmvTargetVO.setTargetOfMonthLiving(targetOfMonthLiving.getValue());
        gmvTargetVO.setTargetOfGmv(targetOfGmv.getValue());

        return AjaxResult.getOK(gmvTargetVO);
    }

    @Override
    public AjaxResult saveGmvTarget(Config config) {
        // 更新gmv奖励系数
        if(Objects.isNull(config)){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        configMapper.update(config);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectCategoryAward() {
        // 获取品类系数奖励
        List<CrmCommissionCategory> crmCommissionCategories = crmCommissionCategoryMapper.selectAll();
        return AjaxResult.getOK(crmCommissionCategories);
    }

    @Override
    public AjaxResult saveCategoryAward(CrmCommissionCategory crmCommissionCategory) {
        if(Objects.isNull(crmCommissionCategory)){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        // 插入或更新品类系数奖励
        crmCommissionCategory.setCreateName(super.getAdminName());
        crmCommissionCategory.setUpdateName(super.getAdminName());
        crmCommissionCategoryMapper.insertOrUpdateById(crmCommissionCategory);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectCoreMerchant(Integer merchantLevelType, String grade) {
        // 获取商户等级系数
        List<CrmCommissionMerchantLevel> crmCommissionMerchantLevels =  crmCommissionMerchantLevelMapper.selectAllCoreMerchantLevel(merchantLevelType,grade);
        return AjaxResult.getOK(crmCommissionMerchantLevels);
    }

    @Override
    public AjaxResult saveCoreMerchant(CrmCommissionMerchantLevel crmCommissionMerchantLevel) {
        if(Objects.isNull(crmCommissionMerchantLevel)){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        // 商户等级gmv及客单价区间最高值不能低于最低值
        boolean isNotDelete = Objects.isNull(crmCommissionMerchantLevel.getDeleteFlag());
        if(isNotDelete && !NumberUtils.INTEGER_ONE.equals(crmCommissionMerchantLevel.getMerchantLevelType())){
            boolean isGmvSmaller = crmCommissionMerchantLevel.getGmvMaximum().compareTo(crmCommissionMerchantLevel.getGmvMinimum()) <= NumberUtils.INTEGER_ZERO;
            boolean isPriceSmaller = crmCommissionMerchantLevel.getPriceMaximum().compareTo(crmCommissionMerchantLevel.getPriceMinimum()) <= NumberUtils.INTEGER_ZERO;
            if(isGmvSmaller || isPriceSmaller){
                return AjaxResult.getErrorWithMsg("商户等级区间中最大值必须大于最小值");
            }
        }
        // 插入或更新商户等级系数
        crmCommissionMerchantLevel.setCreateName(super.getAdminName());
        crmCommissionMerchantLevel.setUpdateName(super.getAdminName());
        crmCommissionMerchantLevelMapper.insertOrUpdateById(crmCommissionMerchantLevel);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectCoreMerchantsNetGrowth() {
        // 获取核心客户净增长数牌级
        List<CrmCommissionCoreMerchant> crmCommissionCoreMerchants = crmCommissionCoreMerchantMapper.selectCoreMerchantsNetGrowth();
        return AjaxResult.getOK(crmCommissionCoreMerchants);
    }

    @Override
    public AjaxResult saveCoreMerchantsNetGrowth(CrmCommissionCoreMerchant crmCommissionCoreMerchant) {
        if(Objects.isNull(crmCommissionCoreMerchant)){
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        boolean isNotDelete = Objects.isNull(crmCommissionCoreMerchant.getDeleteFlag());
        if(isNotDelete){
            // 最高值不能低于最低值
            boolean isSmaller = crmCommissionCoreMerchant.getMaximum().compareTo(crmCommissionCoreMerchant.getMinimum()) <= NumberUtils.INTEGER_ZERO;
            if(isSmaller){
                return AjaxResult.getErrorWithMsg("净增长数区间最大值必须大于最小值");
            }
        }
        // 插入或更新核心客户净增长数牌级
        crmCommissionCoreMerchant.setCreateName(super.getAdminName());
        crmCommissionCoreMerchant.setUpdateName(super.getAdminName());
        crmCommissionCoreMerchantMapper.insertOrUpdateById(crmCommissionCoreMerchant);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult selectMonthLivingCouponQuota() {
        Config config = configMapper.selectOne(ConfigValueEnum.MONTHLY_LIVING_POOL_QUOTA.getKey());
        return AjaxResult.getOK(config.getValue());
    }

    @Override
    public AjaxResult updateMonthLivingCouponQuota(String monthLivingCouponQuota) {
        try {
            BigDecimal bigDecimal = new BigDecimal(monthLivingCouponQuota);
        } catch (NullPointerException e) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        configMapper.updateValue(ConfigValueEnum.MONTHLY_LIVING_POOL_QUOTA.getKey(),monthLivingCouponQuota);
        return AjaxResult.getOK();
    }

    @Override
    public void updateMonthLivingCouponQuotaEveryMonth() {
        BigDecimal bigDecimal = BigDecimal.ZERO;
        configMapper.updateValue(ConfigValueEnum.MONTHLY_LIVING_POOL_QUOTA.getKey(),bigDecimal.toPlainString());
    }

    /**
     * 获取上月核心客户数
     */
    private int getCoreNum(Integer adminId){
        DataSynchronizationInformation lastMonth = dataSynchronizationInformationMapper.selectByTableName(DataSynchronizationInformationEnum.CRM_BD_MONTH_GMV.getTableName());
        AdminInfoVo adminInfoVo = crmBdMonthGmvMapper.selectByAdminId(adminId, lastMonth.getDateFlag());
        return Objects.isNull(adminInfoVo) ? 0 : adminInfoVo.getCoreMerchantNum();
    }

    /**
     * 拼装拉新奖励记录值
     * @param crmCommissionMerchant
     * @return
     */
    private String assemblyMerchantRecordValue(CrmCommissionMerchant crmCommissionMerchant) {
        String value = "区域名称："+crmCommissionMerchant.getZoneName()+"，新销售："+crmCommissionMerchant.getNewBdReward()+"元，其他销售："+crmCommissionMerchant.getNormalBdReward()+"元";
        return value;
    }

}
