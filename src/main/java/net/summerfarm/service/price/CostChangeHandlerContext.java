package net.summerfarm.service.price;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mapper.manage.AreaStoreMapper;
import net.summerfarm.mapper.manage.CycleInventoryCostMapper;
import net.summerfarm.model.domain.CycleInventoryCost;
import net.summerfarm.model.vo.CostChangeVo;
import net.summerfarm.mq.DtsModel;
import net.summerfarm.repository.price.CycleInventoryCostRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 成本倒挂处理上下文
 * <AUTHOR> <a href="mailto:<EMAIL>"><EMAIL></a>
 * @since 2022/3/17 11:06 上午
 */
@Slf4j
@Component
public class CostChangeHandlerContext {

    @Resource
    private List<CostChangeHandler> costChangeHandlers;

    @Resource
    AreaStoreMapper areaStoreMapper;
    @Resource
    private CycleInventoryCostRepository cycleInventoryCostRepository;
    @Resource
    CycleInventoryCostMapper cycleInventoryCostMapper;

    /**
     * 调用处理成本倒挂的各类的情况
     * @param warehouseNo 库存仓编号
     * @param sku         商品编号
     */
    @Async("asycExecutor")
    public void handler(Integer warehouseNo, String sku){
        if(CollectionUtils.isEmpty(costChangeHandlers)){
            log.error("无成本倒挂处理器");
            return;
        }
        // 1.获取最新批次成本信息
        CostChangeVo costChangeVo = areaStoreMapper.selectLastBatchCostPriceBySkuAndAreaNo(warehouseNo, sku);
        if(costChangeVo == null){
            log.info("当前sku在当前库存仓下无最新批次成本信息。warehouseNo:{}, sku:{}", warehouseNo, sku);
            return;
        }
        // 2.获取日周期成本信息
        CycleInventoryCost cycleInventoryCost = cycleInventoryCostRepository.selectBySku(sku, warehouseNo);

        // 3.开始处理
        for (CostChangeHandler costChangeHandler : costChangeHandlers) {
            if(!costChangeHandler.skuIsNeedHandler(sku, warehouseNo)){
                continue;
            }
            try {
                costChangeHandler.handler(costChangeVo, cycleInventoryCost, warehouseNo);
            } catch (Exception e) {
                log.error("倒挂及波动处理异常,库存仓:{}, sku:{}", warehouseNo, sku, e);
            }
        }
    }
}