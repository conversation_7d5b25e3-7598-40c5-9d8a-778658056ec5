package net.summerfarm.service.price;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jdk.nashorn.internal.runtime.regexp.joni.ast.BackRefNode;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.DateUtils;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.dingtalk.DingTalkRobotUtil;
import net.summerfarm.contexts.Global;
import net.summerfarm.dingding.bo.DingTalkMsgBO;
import net.summerfarm.dingding.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.dingding.service.DingTalkMsgSender;
import net.summerfarm.enums.AdminAuthExtendEnum;
import net.summerfarm.enums.ConfigValueEnum;
import net.summerfarm.enums.InventoryExtTypeEnum;
import net.summerfarm.enums.MajorPriceType;
import net.summerfarm.mapper.manage.*;
import net.summerfarm.mapper.manage.repository.AdminAuthExtendRepository;
import net.summerfarm.model.domain.*;
import net.summerfarm.model.vo.CostChangeExtVo;
import net.summerfarm.model.vo.CostChangeVo;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.model.vo.price.SkuMinPriceVO;
import net.summerfarm.service.MajorPriceService;
import org.checkerframework.checker.units.qual.A;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 成本与大客户报价单报价倒挂处理器
 * <AUTHOR> <a href="mailto:<EMAIL>"><EMAIL></a>
 * @since 2022/3/18 3:30 下午
 */
@Slf4j
@Component("costChangeAndMajorPriceHandler")
public class CostChangeAndMajorPriceHandler extends CostChangeBaseService implements CostChangeHandler {

    @Resource
    MajorPriceMapper majorPriceMapper;

    @Resource
    AdminMapper adminMapper;

    @Resource
    AdminAuthExtendRepository adminAuthExtendRepository;

    @Resource
    private DingTalkMsgSender dingTalkMsgSender;

    @Override
    public void handler(CostChangeVo costChangeVo, CycleInventoryCost cycleInventoryCost, Integer warehouseNo) {
        String sku = costChangeVo.getSku();
        //查看大客户报价单数据
        List<CostChangeExtVo> costChanges = majorPriceMapper.lowerCostWhenCostChange(sku, warehouseNo);
        if(CollectionUtils.isEmpty(costChanges)){
            log.info("当前库存仓当前sku无大客户报价单数据。warehouse:{}， sku:{}", warehouseNo, costChangeVo.getSku());
            return;
        }

        BigDecimal lastBatchCostPrice = costChangeVo.getCostPrice();


        // 按大区维度 分组 (默认取第一条)
        Map<Integer, List<CostChangeExtVo>> costChangeMap = costChanges.stream().collect(Collectors.groupingBy(CostChangeExtVo::getLargeAreaNo));
        costChangeMap.forEach((k,v) ->{
            CostChangeExtVo costChange = v.get(0);
            log.info("开始大客户倒挂/成本波动预警. costChange:{}", JSON.toJSONString(costChange));
            if(costChange.getPrice() == null){
                return;
            }
            BigDecimal price = costChange.getPrice();
            BigDecimal firstCycleCost = null;
            if(cycleInventoryCost != null){
                firstCycleCost = cycleInventoryCost.getFirstCycleCost();
            }

            if(lastBatchCostPrice != null && lastBatchCostPrice.compareTo(price) > 0) {
                sendDingDingMsg(costChangeVo, cycleInventoryCost, price, warehouseNo, costChange.getAdminId());
            }else if(firstCycleCost != null && firstCycleCost.compareTo(price) > 0){
                sendDingDingMsg(costChangeVo, cycleInventoryCost, price, warehouseNo, costChange.getAdminId());
            }else{
                largeFluctuationRateTip(costChangeVo, cycleInventoryCost, warehouseNo, price, sku, costChange.getAdminId(), costChange.getLargeAreaName());
            }
        });

    }

    @Override
    public boolean skuIsNeedHandler(String sku, Integer warehouseNo) {
        Set<String> skus = Sets.newHashSet(sku);
        List<Integer> inventoryExtType = Lists.newArrayList(InventoryExtTypeEnum.ACTIVITY.type(), InventoryExtTypeEnum.TEMPORARY_INSURANCE.type(),
                InventoryExtTypeEnum.NOT_SALE.type(), InventoryExtTypeEnum.BROKEN_BAG.type());
        Set<String> noPriceRiskSkus = inventoryMapper.selectBySkusAndExtTypes(skus, inventoryExtType);
        if(!CollectionUtils.isEmpty(noPriceRiskSkus)){
            if(noPriceRiskSkus.contains(sku)){
                log.info("当前库存仓当前sku不需要倒挂监控,库存仓:{},sku:{}", warehouseNo, sku);
                return false;
            }
        }
        return true;
    }

    public void sendDingDingMsg(CostChangeVo costChangeVo, CycleInventoryCost cycleInventoryCost, BigDecimal price,
                                             Integer warehouseNo, Integer adminId) {
        Admin admin = adminMapper.selectByPrimaryKey(adminId);
        //List<String> userIds = getNoticeDingdingUserIds(admin);
        List<Long> userIds = new ArrayList<>();
        if (Objects.nonNull(admin.getSalerId())) {
            userIds.add(admin.getSalerId().longValue());
        }
        if (Objects.nonNull(admin.getOperateId())) {
            userIds.add(admin.getOperateId().longValue());
        }
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        String sku = costChangeVo.getSku();
        InventoryVO inventoryVO = inventoryMapper.selectInventoryVOBySku(sku);
        String warehouseName = Global.warehouseMap.get(warehouseNo);
        if (StringUtils.isEmpty(warehouseName)) {
            log.error("未从Global.warehouseMap中获取到仓库名称,仓库编号:{}", warehouseNo);
            warehouseName = "";
        }
        String batch = costChangeVo.getBatch();
        BigDecimal costPrice = costChangeVo.getCostPrice();
        //发送低价钉钉消息
        if (!CollectionUtils.isEmpty(userIds)) {
            String title = "【倒挂提醒】 "+ admin.getNameRemakes();
            String majorCycleStr = Admin.transMajorCycle(admin.getMajorCycle());
            StringBuilder content = new StringBuilder("##### " + title + "\n");
            content.append("> ###### ").append(inventoryVO.getPdName()+" "+inventoryVO.getWeight()
                    + " 在" + warehouseName +" 价格倒挂").append("\n");
            content.append("> ###### SKU：").append(sku).append("\n");
            content.append("> ###### 报价周期：").append(majorCycleStr).append("\n");
            content.append("> ###### 对比批次号：").append(batch).append("\n");
            DecimalFormat decimalFormat = new DecimalFormat("#0.00");
            content.append("> ###### 批次成本：").append(decimalFormat.format(costPrice)).append("\n");
            if(cycleInventoryCost == null){
                content.append("> ###### 对比日周期：").append("--").append("\n");
                content.append("> ###### 日周期成本：").append("--").append("\n");
            }else{
                content.append("> ###### 对比日周期：").append(DateUtils.localDateToString(cycleInventoryCost.getFirstCycleCostTime().toLocalDate(),
                        DateUtils.DEFAULT_DATE_FORMAT)).append("\n");
                content.append("> ###### 日周期成本：").append(decimalFormat.format(cycleInventoryCost.getFirstCycleCost())).append("\n");
            }
            content.append("> ###### 当前售价：").append(decimalFormat.format(price)).append("\n");
            DingTalkMsgReceiverIdBO dingTalkMsgReceiverIdBO = new DingTalkMsgReceiverIdBO();
            dingTalkMsgReceiverIdBO.setReceiverIdList(userIds);
            dingTalkMsgReceiverIdBO.setMsgType(DingTalkMsgTypeEnum.MARKDOWN.getType());
            dingTalkMsgReceiverIdBO.setTitle(title);
            dingTalkMsgReceiverIdBO.setText(content.toString());
            try {
                dingTalkMsgSender.sendMessageWithFeiShu(dingTalkMsgReceiverIdBO);
            } catch (Exception e) {
                log.warn("【大客户成本【倒挂提醒】】发送飞书消息失败,客户名:{},param:{},e:{}",admin.getNameRemakes(), JSON.toJSONString(dingTalkMsgReceiverIdBO),e.getMessage());
            }
        }
    }

    private List<String> getNoticeDingdingUserIds(Admin admin) {
        List<String> userIds = new ArrayList<>();
        AdminAuthExtend bd = adminAuthExtendRepository.selectByAdminId(AdminAuthExtendEnum.Type.DING_TALK.ordinal(), admin.getSalerId());
        if (bd == null) {
            log.warn("{}没有钉钉登陆信息无法发送钉钉消息", admin.getSalerId());
        } else {
            userIds.add(bd.getUserId());
        }

        AdminAuthExtend operate = adminAuthExtendRepository.selectByAdminId(AdminAuthExtendEnum.Type.DING_TALK.ordinal(), admin.getOperateId());
        if (operate == null) {
            log.warn("{}没有钉钉登陆信息无法发送钉钉消息", admin.getOperateId());
        } else {
            userIds.add(operate.getUserId());
        }
        return userIds;
    }

    private void largeFluctuationRateTip(CostChangeVo costChangeVo, CycleInventoryCost cycleInventoryCost, Integer warehouseNo,
                                         BigDecimal price, String sku, Integer adminId, String largeAreaName) {
        log.info("开始大客户成本波动预警. costChangeVo:{}, cycleInventoryCost:{}, warehouseNo:{}, price:{}, sku:{}, adminId:{}, largeAreaName:{}", JSON.toJSONString(costChangeVo), JSON.toJSONString(cycleInventoryCost), warehouseNo, price, sku, adminId, largeAreaName);
        BigDecimal fluctuationRate = calFluctuationRate(costChangeVo, cycleInventoryCost, warehouseNo);
        if(fluctuationRate == null){
            log.info("阈值波动为空。warehouseNo:{}, sku:{}", warehouseNo, sku);
            return;
        }
        //其实cycleInventoryCost == null不会走到这一步，上面就跳出去了
        if (cycleInventoryCost == null) {
            return;
        }
        BigDecimal configFluctuationRate = getConfigFluctuationRate(cycleInventoryCost.getFirstCycleCost(), warehouseNo, sku);
        if(configFluctuationRate == null){
            log.info("波动配置为空。warehouseNo:{}, sku:{}", warehouseNo, sku);
            return;
        }
        if(fluctuationRate.compareTo(configFluctuationRate) > 0){
            sendLargeFluctuations(costChangeVo, cycleInventoryCost, price, warehouseNo, fluctuationRate, adminId, largeAreaName);
        }
    }

    public void sendLargeFluctuations(CostChangeVo costChangeVo, CycleInventoryCost cycleInventoryCost,
                                      BigDecimal price, Integer warehouseNo, BigDecimal fluctuationRate, Integer adminId, String largeAreaName) {
        String sku = costChangeVo.getSku();
        BigDecimal costPrice = costChangeVo.getCostPrice();
        String batchNo = costChangeVo.getBatch();
        Admin admin = adminMapper.selectByPrimaryKey(adminId);
        //List<String> userIds = getNoticeDingdingUserIds(admin);
        List<Long> userIds = new ArrayList<>();
        if (Objects.nonNull(admin.getSalerId())) {
            userIds.add(admin.getSalerId().longValue());
        }
        if (Objects.nonNull(admin.getOperateId())) {
            userIds.add(admin.getOperateId().longValue());
        }
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        boolean costUp = this.isCostUp(costChangeVo, cycleInventoryCost, warehouseNo);
        String costUpOrDown = costUp ? "上涨" : "下降";
        try {

            InventoryVO inventoryVO = inventoryMapper.selectSkuType(sku);
            String warehouseName = Global.warehouseMap.get(warehouseNo);
            if (StringUtils.isEmpty(warehouseName)) {
                log.error("未从Global.warehouseMap中获取到仓库名称,仓库编号:{}", warehouseNo);
                warehouseName = "";
            }
            // 查询sku在所有区域
            String title = "【成本波动】 ";
            StringBuilder content = new StringBuilder("##### " + title + "\n");
            content.append("> ###### 规格：").append(inventoryVO.getPdName()).append("")
                    .append(inventoryVO.getWeight())
                    .append(" 在" + warehouseName + " 成本" + costUpOrDown + ":" + fluctuationRate.multiply(BigDecimal.valueOf(100)) + "%")
                    .append("\n");
            content.append("> ###### SKU：").append(sku).append("\n");
            content.append("> ###### 最新批次号：").append(batchNo).append("\n");
            content.append("> ###### 批次成本：").append(new DecimalFormat("#0.00").format(costPrice)).append("\n");
            content.append("> ###### 库存成本：").append(new DecimalFormat("#0.00").format(cycleInventoryCost.getFirstCycleCost()))
                    .append("（").append(DateUtils.localDateToString(cycleInventoryCost.getFirstCycleCostTime().toLocalDate(),
                            DateUtils.DEFAULT_DATE_FORMAT)).append("）").append("\n");
            content.append("> ###### 当前售价：").append(price).append("\n");
            content.append("> ###### 受影响客户id：").append(adminId).append("\n");
            content.append("> ###### 受影响客户名称：").append(admin.getNameRemakes()).append("\n");
            content.append("> ###### 受影响运营大区：").append(largeAreaName).append("\n");
            DingTalkMsgReceiverIdBO dingTalkMsgReceiverIdBO = new DingTalkMsgReceiverIdBO();
            dingTalkMsgReceiverIdBO.setReceiverIdList(userIds);
            dingTalkMsgReceiverIdBO.setMsgType(DingTalkMsgTypeEnum.MARKDOWN.getType());
            dingTalkMsgReceiverIdBO.setTitle(title);
            dingTalkMsgReceiverIdBO.setText(content.toString());
            dingTalkMsgSender.sendMessageWithFeiShu(dingTalkMsgReceiverIdBO);
        } catch (Exception e) {
            log.warn("【成本波动】 倒挂钉钉提醒异常,costPrice:{}, price:{}, areaNo:{}, sku:{}, warehouseNo:{}, batchNo:{}",
                    costPrice, price, sku, warehouseNo, batchNo, e);
        }
    }

}