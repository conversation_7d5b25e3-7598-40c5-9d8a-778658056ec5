package net.summerfarm.service.price;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.enums.ConfigValueEnum;
import net.summerfarm.mapper.manage.ConfigMapper;
import net.summerfarm.mapper.manage.InventoryMapper;
import net.summerfarm.mapper.manage.PriceAdjustmentRuleSectionMapper;
import net.summerfarm.model.domain.Config;
import net.summerfarm.model.domain.CycleInventoryCost;
import net.summerfarm.model.domain.PriceAdjustmentRuleSection;
import net.summerfarm.model.vo.CostChangeVo;
import net.summerfarm.model.vo.InventoryVO;
import net.summerfarm.service.AreaSkuService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 *
 * <AUTHOR> href="mailto:<EMAIL>>黄棽</a>
 * @since 2022-03-29
 */
@Data
@Slf4j
@Component
public class CostChangeBaseService {

    @Resource
    InventoryMapper inventoryMapper;

    @Resource
    ConfigMapper configMapper;

    @Resource
    PriceAdjustmentRuleSectionMapper priceAdjustmentRuleSectionMapper;

    public DingdingTipBaseData dingdingTipBaseData(Integer warehouseNo, String sku, String configKey){
        DingdingTipBaseData data = new DingdingTipBaseData();
        Config config = configMapper.selectOne(configKey);
        InventoryVO inventoryVO = inventoryMapper.selectSkuType(sku);
        String warehouseName = Global.warehouseMap.get(warehouseNo);
        if (StringUtils.isEmpty(warehouseName)) {
            log.error("未从Global.warehouseMap中获取到仓库名称,仓库编号:{}", warehouseNo);
            warehouseName = "";
        }
        data.setWarehouseName(warehouseName);
        data.setInventoryVO(inventoryVO);
        data.setUrl(config.getValue());
        return data;
    }

    public BigDecimal calFluctuationRate(CostChangeVo costChangeVo, CycleInventoryCost cycleInventoryCost, Integer warehouseNo){
        String sku = costChangeVo.getSku();
        if(cycleInventoryCost == null || cycleInventoryCost.getFirstCycleCost() == null || cycleInventoryCost.getFirstCycleCost().compareTo(BigDecimal.ZERO) <= 0){
            log.info("当前库存仓当前sku无日周期成本数据。warehouseNo:{}, sku:{}", warehouseNo, sku);
            return null;
        }
        // 日周期成本在产品文档中也被定义为老成本
        BigDecimal oldCostPrice = cycleInventoryCost.getFirstCycleCost();
        // 最新批次成本在产品文档中也被定义为新成本
        BigDecimal costPrice = costChangeVo.getCostPrice();
        return costPrice.subtract(oldCostPrice).abs()
                .divide(oldCostPrice, 2, RoundingMode.UP);
    }


    public boolean isCostUp(CostChangeVo costChangeVo, CycleInventoryCost cycleInventoryCost, Integer warehouseNo){
        String sku = costChangeVo.getSku();
        if(cycleInventoryCost == null || cycleInventoryCost.getFirstCycleCost() == null || cycleInventoryCost.getFirstCycleCost().compareTo(BigDecimal.ZERO) <= 0){
            log.info("当前库存仓当前sku无日周期成本数据。warehouseNo:{}, sku:{}", warehouseNo, sku);
            return false;
        }
        // 日周期成本在产品文档中也被定义为老成本
        BigDecimal oldCostPrice = cycleInventoryCost.getFirstCycleCost();
        // 最新批次成本在产品文档中也被定义为新成本
        BigDecimal costPrice = costChangeVo.getCostPrice();
        return costPrice.compareTo(oldCostPrice) > 0;
    }

    public BigDecimal getConfigFluctuationRate(BigDecimal oldCostPrice, Integer warehouseNo, String sku){
        // 1. 查询当前成本的波动值
        PriceAdjustmentRuleSection priceAdjustmentRuleSection = priceAdjustmentRuleSectionMapper.selectByCycleCost(oldCostPrice);
        if(priceAdjustmentRuleSection == null || priceAdjustmentRuleSection.getFluctuationValue() == null){
            log.info("当前库存仓当前sku未查询到有符合调价的波动配置数据。warehouseNo:{}, sku:{}", warehouseNo, sku);
            return null;
        }
        return priceAdjustmentRuleSection.getFluctuationValue();
    }

    @Setter
    @Getter
    static class DingdingTipBaseData{

        private String warehouseName;

        private InventoryVO inventoryVO;

        /**
         * 钉钉机器人url
         */
        private String url;
    }

}
