package net.summerfarm.enums;

public enum ConfigValueEnum {


    COST_PRODUCTS_SENDER("cost_products_sender","普通商品调价发送钉钉消息指定人"),
    GMV_EXCLUDE_ADMIN("gmv_exclude_admin","gmv排除客户编码为121"),
    BD_ACHIEVEMENT_RECEIVE("bd_achievement_receive","bd业绩邮件接收人"),
    GMV_TARGET("gmv_target","gmv目标值"),
    EXPRIED_PRICE_RULE("expried_price_rule","临保活动价格规则"),
    FREE_DAY_AREANO("free_day_areano","免邮城市弹窗提示编号 0全部城市"),
    PANIC_BUY_ORDER_TIME("panic_buy_order_time","秒杀已购时间"),
    NEW_AREA_MONTH("new_area_month","新城市定义"),
    COST_PRODUCTS_SENDER_ROBOT("cost_products_sender_robot", "成本倒挂钉钉群机器人"),
    COST_PRICE_FLUCTUATION_SENDER_ROBOT("cost_price_fluctuation_sender_robot", "成本波动钉钉群机器人"),
    COST_INVERSION_CHANGE_PRICE_SENDER_ROBOT("cost_inversion_change_price_sender_robot", "成本倒挂自动调价钉钉群机器人"),
    COST_MARKET_PRICE_INVERSION_FLUCTUATION_ROBOT("cost_market_price_inversion_fluctuation_robot", "成本营销价倒挂和成本波动钉钉群机器人"),
    SPECIAL_PRICE_ACTIVITY_INVERSION_ROBOT("special_price_activity_inversion_robot", "特价活动倒挂钉钉机器人"),
    MONTHLY_LIVING_POOL_QUOTA("MONTHLY_LIVING_POOL_QUOTA","月活池额度"),
    FINANCE_PERIOD_OVERVIEW_EXCEL("FINANCE_PERIOD_OVERVIEW_EXCEL","账单概览excel模板"),
    FINANCE_PERIOD_DETAILS_EXCEL("FINANCE_PERIOD_DETAILS_EXCEL","账单明细excel模板"),
    ADMIN_ORDER_DETAILS_EXCEL("ADMIN_ORDER_DETAILS_EXCEL","大客户对账单导出模板"),
    BILL_REDO_UPDATE_TIME("BILL_REDO_UPDATE_TIME","账期重做上线时间"),
    CBD_ADMIN_ID("CBD_admin_id", "茶百道id"),
    SHU_YI_ADMIN_ID("SHU_YI_ADMIN_ID", "书亦id"),

    /**
     * es商品数据关注属性
     */
    ES_PRODUCT_PROPERTY_IDS("es_product_property_ids", "es商品数据关注属性"),
    PRODUCT_MAPPING_TEMPLATE_URL("PRODUCT_MAPPING_TEMPLATE_URL", "大客户商品映射模板URL"),

    PRODUCT_MESSAGE_MAIL("PRODUCT_MESSAGE_MAIL", "商品上新通知人的邮箱"),
    TIMING_CHECK_BEGIN_TIME("TIMING_CHECK_BEGIN_TIME", "省心送设置配送计划校验开始时间"),
    CONSIGNMENT_NO_WAREHOUSE_ENABLE("CONSIGNMENT_NO_WAREHOUSE_ENABLE", "代仓 sku 开关"),
    PRINT_ORDER("PRINT_ORDER", "配送单打印逻辑开关"),
    BATCH_UPDATE_SKU_WAREHOUSE_SWITCH("BATCH_UPDATE_SKU_WAREHOUSE_SWITCH", "批量更新sku库存使用仓使用时间限制开关"),
    ;

    ConfigValueEnum(String key, String remark) {
        this.key = key;
        this.remark = remark;
    }

    private String key;

    private String remark;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
