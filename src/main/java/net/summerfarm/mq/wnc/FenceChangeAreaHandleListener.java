package net.summerfarm.mq.wnc;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.model.input.wnc.FenceChangeAreaHandleMsg;
import net.summerfarm.service.MerchantService;
import net.summerfarm.service.contact.ContactService;
import net.summerfarm.wnc.client.mq.WncMqConstants;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/8/31  10:39
 */
@Slf4j
@Component
@MqListener(topic = WncMqConstants.Topic.TOPIC_WNC_FENCE, tag = WncMqConstants.Tag.TAG_FENCE_CHANGE_AREA_HANDLE, consumerGroup = WncMqConstants.ConsumeGroup.GID_MANAGE_FENCE_CHANGE_AREA_HANDLE)
public class FenceChangeAreaHandleListener extends AbstractMqListener<FenceChangeAreaHandleMsg> {
    @Resource
    private ContactService contactService;
    @Resource
    private MerchantService merchantService;

    @Override
    public void process(FenceChangeAreaHandleMsg areaHandleMsg) {
        log.info("FenceChangeListener process: {}", areaHandleMsg);

        // 城配仓编号有变更时更新地址信息
        if (!Objects.equals(areaHandleMsg.getOldStoreNo(), areaHandleMsg.getNewStoreNo())) {
            contactService.updateStoreNoBatch(areaHandleMsg.getFenceCity(), areaHandleMsg.getFenceAreas(), areaHandleMsg.getNewStoreNo());
        }

        //运营服务区有变更时更改客户运营服务区
        if (!Objects.equals(areaHandleMsg.getOldAreaNo(), areaHandleMsg.getNewAreaNo())) {
            merchantService.updateMerchantAreaBatch(areaHandleMsg.getFenceProvince(), areaHandleMsg.getFenceCity(), areaHandleMsg.getFenceAreas(), areaHandleMsg.getNewAreaNo());
        }
    }
}
