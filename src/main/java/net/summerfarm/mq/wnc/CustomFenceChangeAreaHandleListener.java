package net.summerfarm.mq.wnc;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.model.input.wnc.CustomFenceChangeAreaHandleMsg;
import net.summerfarm.model.input.wnc.FenceChangeAreaHandleMsg;
import net.summerfarm.service.MerchantService;
import net.summerfarm.service.contact.ContactService;
import net.summerfarm.wnc.client.mq.WncMqConstants;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 自定义围栏切仓
 * <AUTHOR>
 * @date 2023/8/31  10:39
 */
@Slf4j
@Component
@MqListener(topic = WncMqConstants.Topic.TOPIC_WNC_FENCE, tag = WncMqConstants.Tag.TAG_FENCE_CHANGE_AREA_HANDLE, consumerGroup = WncMqConstants.ConsumeGroup.GID_MANAGE_FENCE_CHANGE_AREA_HANDLE)
public class CustomFenceChangeAreaHandleListener extends AbstractMqListener<CustomFenceChangeAreaHandleMsg> {
    @Resource
    private ContactService contactService;
    @Resource
    private MerchantService merchantService;

    @Override
    public void process(CustomFenceChangeAreaHandleMsg areaHandleMsg) {
        log.info("自定义围栏切仓开始执行: {}", areaHandleMsg);

        // 城配仓编号有变更时更新地址信息
        this.handleAddress(areaHandleMsg);

        //运营服务区有变更时更改客户运营服务区
        this.handleArea(areaHandleMsg);

        log.info("自定义围栏切仓执行完成: {}", areaHandleMsg);
    }


    /**
     * 处理影响范围内的地址
     */
    private void handleAddress(CustomFenceChangeAreaHandleMsg areaHandleMsg) {
        // 1. 查询影响范围内的地址

        // 2. 更新地址信息
    }


    /**
     * 处理影响范围内的运营区域
     */
    private void handleArea(CustomFenceChangeAreaHandleMsg areaHandleMsg) {
        // 1. 查询影响范围内的运营区域
        // 2. 更新运营区域信息
    }
}
