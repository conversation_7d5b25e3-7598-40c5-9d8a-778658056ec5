package net.summerfarm.facade.auth;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.AdminAuthExtendEnum;
import net.summerfarm.mapper.manage.AdminMapper;
import net.summerfarm.model.domain.Admin;
import net.summerfarm.model.domain.AdminAuthExtend;
import net.summerfarm.model.domain.Inventory;
import net.xianmu.authentication.client.dto.AuthUser;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.provider.AuthUserAuthProvider;
import net.xianmu.authentication.client.provider.AuthUserProvider;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AuthUserAuthFacade {
    @DubboReference
    private AuthUserAuthProvider authUserAuthProvider;
    @DubboReference
    private AuthUserProvider authUserProvider;
    @Resource
    AuthUserFacade authUserFacade;

    @Resource
    AdminMapper adminMapper;

    public AdminAuthExtend selectByUserId(Integer type, String userId) {
        //根据admin_id 找到user_base_id
        DubboResponse<AuthUserAuthResp> authUserAuthRespDubboResponse = authUserAuthProvider.queryUserAuthByUserId(SystemOriginEnum.ADMIN, userId, type);
        AuthUserAuthResp data = authUserAuthRespDubboResponse.getData();
        if (data == null) {
            return null;
        }
        Long baseUserId = data.getBaseUserId();
        Integer adminId = adminMapper.selectAdminIdByUserBaseId(baseUserId);
        return convert(adminId, type, data);
    }


    public AdminAuthExtend selectByAdminId(Integer type, Integer adminId) {
        //根据admin_id 找到user_base_id
        Long userBaseId = adminMapper.selectUserBaseIdById(adminId);
        if (userBaseId == null) {
            log.error("用户数据错误 adminId{}", adminId);
            return null;
        }
        DubboResponse<AuthUserAuthResp> authUserAuthRespDubboResponse = authUserAuthProvider.queryUserAuth(SystemOriginEnum.ADMIN, userBaseId, type);
        if (authUserAuthRespDubboResponse.getData() == null) {
            return null;
        }
        AuthUserAuthResp data = authUserAuthRespDubboResponse.getData();
        return convert(adminId, type, data);
    }

    private AdminAuthExtend convert(Integer adminId, Integer type, AuthUserAuthResp authUserAuthResp) {
        if (adminId == null) {
            return null;
        }
        AdminAuthExtend extend = new AdminAuthExtend();
        extend.setAdminId(adminId);
        extend.setOpenid(authUserAuthResp.getAuthId());
        extend.setType(type);
        //dingding
        if (type.equals(AdminAuthExtendEnum.Type.DING_TALK.ordinal())) {
            extend.setUserId(authUserAuthResp.getThirdPartyId());
        }
        extend.setUnionId(authUserAuthResp.getThirdPartyId());
        return extend;
    }

    public List<AdminAuthExtend> selectByBaseUserIds(List<Long> userBaseIds, Integer type) {
        DubboResponse<List<AuthUserAuthResp>> listDubboResponse = authUserAuthProvider.queryUsersAuth(SystemOriginEnum.ADMIN, userBaseIds, type);
        List<AuthUserAuthResp> data = listDubboResponse.getData();
        if (data == null) {
            return new ArrayList<>();
        }
        List<Admin> admins = adminMapper.selectByUserBaseIds(userBaseIds);
        Map<Long, Integer> inventoryMap = admins.stream().collect(Collectors.toMap(Admin::getBaseUserId, Admin::getAdminId, (x1, x2) -> x1));
        List<AdminAuthExtend> collect = data.stream().map(
                it -> {
                    Integer adminId = inventoryMap.get(it.getBaseUserId());
                    return convert(adminId, type, it);
                }
        ).collect(Collectors.toList());
        return collect;
    }
}
