package net.summerfarm.facade.tms.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import net.summerfarm.wnc.client.enums.SourceEnum;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2023/3/10 10:45<br/>
 *
 * <AUTHOR> />
 */
@Data
@AllArgsConstructor
@Builder
public class DeliveryRuleQueryInput {
    /**
     * 下单时间
     */
    private LocalDateTime orderTime;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 联系人id
     */
    private Long contactId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * poi
     */
    private String poi;

    /**
     * 开始查询时间
     */
    private LocalDate queryBeginDate;

    /**
     * 结束查询时间
     */
    private LocalDate queryEndDate;

    /**
     * 订单来源
     */
    private SourceEnum source;
}
