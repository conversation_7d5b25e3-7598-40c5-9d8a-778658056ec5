package net.summerfarm.facade.wnc.dto;

import lombok.Data;
import net.summerfarm.wnc.client.enums.SourceEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/10/16 16:12
 * @PackageName:net.summerfarm.mall.service.facade.dto
 * @ClassName: AreaQueryReq
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class FenceCloseTimeInput implements Serializable {

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 来源
     */
    private SourceEnum source;

    /**
     * 地址
     */
    private Long contactId;

    /**
     * 租户号
     */
    private Long tenantId;

    /**
     * poi
     */
    private String poi;
}
