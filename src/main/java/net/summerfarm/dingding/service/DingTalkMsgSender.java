package net.summerfarm.dingding.service;

import net.summerfarm.dingding.bo.DingTalkMsgBO;
import net.summerfarm.dingding.bo.DingTalkMsgReceiverIdBO;

/**
 * @description 钉钉消息发送接口
 * <AUTHOR>
 * @date 2022/3/9 10:14
 */
public interface DingTalkMsgSender {

    /**
     * 发送钉钉消息 发送给个人
     * 机器人消息请移步 DingTalkRobotUtil
     * @param dingTalkMsgBO
     */
    void sendMessage(DingTalkMsgBO dingTalkMsgBO);

    /**
     * 飞书消息兼容
     * @param msgBO 消息体
     */
    void sendMessageWithFeiShu(DingTalkMsgReceiverIdBO msgBO);
}
