package net.summerfarm.dingding.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.client.req.MessageBodyReq;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.taobao.api.ApiException;
import net.summerfarm.common.base.BaseService;
import net.summerfarm.common.util.DingTalkUtils;
import net.summerfarm.contexts.Global;
import net.summerfarm.dingding.bo.DingTalkMsgBO;
import net.summerfarm.dingding.bo.DingTalkMsgReceiverIdBO;
import net.summerfarm.dingding.enums.DingTalkMsgTypeEnum;
import net.summerfarm.facade.msg.FeiShuPersonalMsgFacade;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.robot.feishu.dto.card.FeishuCard;
import net.xianmu.robot.feishu.dto.card.FeishuCardBasic;
import net.xianmu.robot.feishu.dto.card.FeishuCardElement;
import net.xianmu.robot.feishu.dto.card.FeishuCardRequest;
import net.xianmu.robot.feishu.enums.CardTagEnum;
import net.xianmu.robot.feishu.enums.MsgTypeEnum;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * <AUTHOR>
 * @description 钉钉消息发送实现类，迁移到消息中心、使用飞书，文档地址：
 * @date 2022/3/9 10:33
 */
@Service
@Deprecated
public class DingTalkMsgSenderImpl extends BaseService implements DingTalkMsgSender {
    @Resource
    private FeiShuPersonalMsgFacade feiShuPersonalMsgFacade;

    @Override
    @Deprecated
    public void sendMessage(DingTalkMsgBO dingTalkMsgBO) {
        logger.info("发送消息给个人，dingTalkMsgBO：{}", JSON.toJSONString(dingTalkMsgBO));
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
        OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
        request.setAgentId(Long.valueOf(Global.AGENT_ID));
        request.setUseridList(dingTalkMsgBO.getUserIdList());
        request.setToAllUser(false);
        OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
        assemblyMsg(msg, dingTalkMsgBO);
        request.setMsg(msg);
        String accessToken = DingTalkUtils.init().getToken();
        try {
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, accessToken);
            logger.info("正在发送给{},msg{}", dingTalkMsgBO.getUserIdList(), msg);
            if (!rsp.isSuccess()) {
                logger.error("钉钉信息发送失败，异常信息：{}", rsp.getErrmsg());
            }
        } catch (ApiException e) {
            logger.error("钉钉消息发送失败, 异常信息：{}", e.getErrMsg());
        }
    }

    private void assemblyMsg(OapiMessageCorpconversationAsyncsendV2Request.Msg msg, DingTalkMsgBO dingTalkMsgBO) {
        if (dingTalkMsgBO.getMsgType().equals(DingTalkMsgTypeEnum.TXT.getType())) {
            msg.setMsgtype(DingTalkMsgTypeEnum.TXT.getType());
            msg.setText(new OapiMessageCorpconversationAsyncsendV2Request.Text());
            msg.getText().setContent(dingTalkMsgBO.getContent());
        } else if (dingTalkMsgBO.getMsgType().equals(DingTalkMsgTypeEnum.MARKDOWN.getType())) {
            msg.setMsgtype(DingTalkMsgTypeEnum.MARKDOWN.getType());
            msg.setMarkdown(new OapiMessageCorpconversationAsyncsendV2Request.Markdown());
            msg.getMarkdown().setText(dingTalkMsgBO.getText());
            msg.getMarkdown().setTitle(dingTalkMsgBO.getTitle());
        } else if (dingTalkMsgBO.getMsgType().equals(DingTalkMsgTypeEnum.LINK.getType())) {
            msg.setMsgtype(DingTalkMsgTypeEnum.LINK.getType());
            msg.setLink(new OapiMessageCorpconversationAsyncsendV2Request.Link());
            msg.getLink().setTitle(dingTalkMsgBO.getTitle());
            msg.getLink().setText(dingTalkMsgBO.getText());
            msg.getLink().setMessageUrl(dingTalkMsgBO.getMessageUrl());
            msg.getLink().setPicUrl(dingTalkMsgBO.getPicUrl());
        }
    }

    @Override
    public void sendMessageWithFeiShu(DingTalkMsgReceiverIdBO msgBO) {
        logger.info("发送消息给个人，dingTalkMsgBO：{}", JSON.toJSONString(msgBO));
        if (CollectionUtils.isEmpty(msgBO.getReceiverIdList())) {
            throw new ParamsException("消息发送失败，未指定消息接收人");
        }

        MessageBodyReq req = new MessageBodyReq();
        req.setContentType(1);
        if (msgBO.getMsgType().equals(DingTalkMsgTypeEnum.TXT.getType())) {
            req.setMsgBodyType(0);
            req.setTitle(msgBO.getTitle());

            JSONObject text = new JSONObject();
            text.put("text", msgBO.getContent());
            req.setData(text.toJSONString());
        } else {
            req.setMsgBodyType(3);
            req.setTitle(msgBO.getTitle());

            String msgStr = msgBO.getText().replaceAll("#","")
                    .replaceAll(">>", ">")
                    .replaceAll(">", "\n")
                    .replaceAll("\n\n", "\n")
                    .replaceAll(" ","");

            FeishuCardElement feishuCardElement = new FeishuCardElement();
            feishuCardElement.setTag(CardTagEnum.div.getValue());
            feishuCardElement.setText(new FeishuCardBasic(CardTagEnum.lark_md.getValue(), msgStr));

            FeishuCard feishuCard = new FeishuCard();
            feishuCard.setElements(Collections.singletonList(feishuCardElement));

            req.setData(JSON.toJSONString(feishuCard));
        }

        feiShuPersonalMsgFacade.sendFeiShuPersonalMsg(msgBO.getReceiverIdList(), req);
    }
}
