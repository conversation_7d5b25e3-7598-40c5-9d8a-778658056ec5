package net.summerfarm.model.input.wnc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.validation.groups.Add;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/31  10:56
 */
@Data
public class CustomFenceChangeAreaHandleMsg implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 省
     */
    private String province;


    /**
     * 市
     */
    private String city;


    /**
     * 区
     */
    private String area;
}
